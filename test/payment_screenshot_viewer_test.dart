import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:rauth/admin/payment_screenshot_viewer.dart';

void main() {
  testWidgets('PaymentScreenshotViewer displays correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      MaterialApp(
        home: PaymentScreenshotViewer(
          imageUrl: 'https://example.com/test-image.jpg',
          userName: 'Test User',
          paymentId: 'payment123',
          paymentDetails: 'Basic Plan - 10,000 MMK',
        ),
      ),
    );

    // Verify that the app bar is displayed
    expect(find.text('Payment Screenshot'), findsOneWidget);
    
    // Verify that the user name is displayed
    expect(find.text('Test User'), findsOneWidget);
    
    // Verify that the payment details are displayed
    expect(find.text('Basic Plan - 10,000 MMK'), findsOneWidget);
    
    // Verify that the payment ID is displayed
    expect(find.text('ID: payment123'), findsOneWidget);
    
    // Verify that the loading indicator is displayed initially
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
    
    // Verify that the action buttons are present
    expect(find.byIcon(Icons.open_in_browser), findsOneWidget);
    expect(find.byIcon(Icons.copy), findsOneWidget);
  });
}
