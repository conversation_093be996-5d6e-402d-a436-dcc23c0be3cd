name: rauth
description: "A new Flutter package project."
version: 0.0.4
homepage:
publish_to: none

environment:
  sdk: ^3.7.2
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  firebase_core:
  firebase_auth:
  cloud_firestore:
  shared_preferences:
  device_info_plus:
  path_provider:
  flutter_riverpod:
  flutter_animate:
  fluttertoast:
  lottie:
  url_launcher:
  uuid: ^4.5.1
  intl:
  image_picker:
  http:
  dio:
  photo_view:
  package_info_plus:
  permission_handler: ^12.0.0+1
  open_file:
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/lotties/
    - assets/images/
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
