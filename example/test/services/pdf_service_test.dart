import 'package:flutter_test/flutter_test.dart';
import 'package:example/services/pdf_service.dart';
import 'package:example/services/image_export_service.dart';

void main() {
  group('PdfService', () {
    test('should generate PDF with ledger data', () async {
      // Arrange
      final Map<String, int> testLedger = {
        '00': 1500,
        '01': 2000,
        '02': 500,
        '03': 3000,
        '04': 1000,
        '05': 2500,
        '06': 800,
        '07': 1200,
        '08': 1800,
        '09': 900,
        '10': 2200,
        '11': 1600,
        '12': 700,
        '13': 3500,
        '14': 1100,
        '15': 1900,
        '16': 600,
        '17': 2800,
        '18': 1300,
        '19': 2100,
      };
      const int total = 32500;
      const String matchTitle = 'Test Match 2024-01-15';
      const int brakeAmount = 1000;

      // Act
      final pdfBytes = await PdfService.generateLedgerPdf(
        ledger: testLedger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
      );

      // Assert
      expect(pdfBytes, isNotNull);
      expect(pdfBytes.isNotEmpty, true);
      expect(
        pdfBytes.length,
        greaterThan(1000),
      ); // PDF should have reasonable size
    });

    test('should handle empty ledger', () async {
      // Arrange
      final Map<String, int> emptyLedger = <String, int>{};
      const int total = 0;
      const String matchTitle = 'Empty Match';
      const int brakeAmount = 1000;

      // Act
      final pdfBytes = await PdfService.generateLedgerPdf(
        ledger: emptyLedger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
      );

      // Assert
      expect(pdfBytes, isNotNull);
      expect(pdfBytes.isNotEmpty, true);
    });

    test('should handle large ledger data', () async {
      // Arrange - Create a large ledger with 100 entries
      final Map<String, int> largeLedger = <String, int>{};
      for (int i = 0; i < 100; i++) {
        largeLedger[i.toString().padLeft(2, '0')] = (i + 1) * 100;
      }
      final int total = largeLedger.values.fold(0, (sum, value) => sum + value);
      const String matchTitle = 'Large Match Data';
      const int brakeAmount = 2000;

      // Act
      final pdfBytes = await PdfService.generateLedgerPdf(
        ledger: largeLedger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
      );

      // Assert
      expect(pdfBytes, isNotNull);
      expect(pdfBytes.isNotEmpty, true);
      expect(pdfBytes.length, greaterThan(2000)); // Larger PDF for more data
    });
  });

  group('ImageExportService', () {
    test('should generate image with ledger data', () async {
      // Arrange
      final Map<String, int> testLedger = {
        '00': 1500,
        '01': 2000,
        '02': 500,
        '03': 3000,
        '04': 1000,
      };
      const int total = 8000;
      const String matchTitle = 'Test Match 2024-01-15';
      const int brakeAmount = 1000;

      // Act
      final imageBytes = await ImageExportService.generateLedgerImage(
        ledger: testLedger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
      );

      // Assert
      expect(imageBytes, isNotNull);
      expect(imageBytes!.isNotEmpty, true);
      expect(
        imageBytes.length,
        greaterThan(100),
      ); // Image should have reasonable size
    });

    test('should handle empty ledger for image generation', () async {
      // Arrange
      final Map<String, int> emptyLedger = <String, int>{};
      const int total = 0;
      const String matchTitle = 'Empty Match';
      const int brakeAmount = 1000;

      // Act
      final imageBytes = await ImageExportService.generateLedgerImage(
        ledger: emptyLedger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
      );

      // Assert
      expect(imageBytes, isNotNull);
      expect(imageBytes!.isNotEmpty, true);
    });
  });
}
