import 'package:flutter_test/flutter_test.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/util/rfix.dart';
import 'package:example/util/util.dart';

void main() {
  group("Generate real world tests", () {
    test("normal", () {
      final String input = '''

 48/57/29/1000
 78,=50,90,900
 78
 97
 900
 3 4 ပါ2000
 1/4ပါ2000
 ပါဝါ=1000
 Total 800
''';
      final String fixed = fix(input);
      print(fixed);
      final BetRaw betRaw = multiGenerateTwo(fixed);
      print(betRaw.toMap());
      expect(betRaw.total>=3000,true );
    });

    test('htate bate tests',(){
      final String input = '''
1/67ထိပ်နောက်1000

ဒါကိုမဝင်ဘူးလုပ်နေတယ်''';
  String fixed = fix(input);
  print(fixed);

      final BetRaw betRaw = multiGenerateTwo(fixed);
      print(betRaw.toMap());
      expect(betRaw.total>1000,true );
    
    });
    test('should generate tests from real world examples', () {
      final String input = '''
 
12r500
17,78=50
67,62,15,600
10,65,60,r1000
67,600
68,90,98,67,600





''';
      final String fixed = fix(input);
      final BetRaw betRaw = multiGenerateTwo(fixed);
      print(betRaw.toMap());
      expect(betRaw.total>1000,true );
    });
  });
  group('fix function with real-world examples', () {
    test('should handle real-world lottery input example 1', () {
      // Arrange
      final String input = """
      12.400
      34.500
      56.600
      78.700
      90.800
      """;

      // Act
      final String result = fix(input);

      // Assert
      final String expected =
          """
12=400
34=500
56=600
78=700
90=800""".trim();

      expect(result, equals(expected));
    });

    test(
      'should handle real-world lottery input example 2 with Myanmar characters',
      () {
        // Arrange
        final String input = """
      အပူး.1000
      နက္ခက်.2000
      ပါဝါ.3000
      """;

        // Act
        final String result = fix(input);

        // Assert
        final String expected =
            """
အပူး=1000
နက္ခက်=2000
ပါဝါ=3000""".trim();

        expect(result, equals(expected));
      },
    );

    test(
      'should handle real-world lottery input example 3 with mixed formats',
      () {
        // Arrange
        final String input = """
      12,34,56.1000
      78.2000
      90=3000
      """;

        // Act
        final String result = fix(input);

        // Assert
        final String expected =
            """
12,34,56=1000
78=2000
90=3000""".trim();

        expect(result, equals(expected));
      },
    );

    test(
      'should handle real-world lottery input example 4 with r notation',
      () {
        // Arrange
        final String input = """
      12.1000r2000
      34.3000r4000
      """;

        // Act
        final String result = fix(input);

        // Assert
        final String expected =
            """
12=1000r2000
34=3000r4000""".trim();

        expect(result, equals(expected));
      },
    );

    test(
      'should handle real-world lottery input example 5 with multiple dots',
      () {
        // Arrange
        final String input = """
      12.....1000
      34.....2000
      56,78,90.....3000
      """;

        // Act
        final String result = fix(input);

        // Assert
        final String expected =
            """
12=1000
34=2000
56,78,90=3000""".trim();

        expect(result, equals(expected));
      },
    );

    test('should handle real-world lottery input example 6 with spaces', () {
      // Arrange
      final String input = """
      12 . 1000
      34 = 2000
      56 , 78 , 90 . 3000
      """;

      // Act
      final String result = fix(input);

      // Assert
      final String expected =
          """
12=1000
34=2000
56,78,90=3000""".trim();

      expect(result, equals(expected));
    });

    test(
      'should handle real-world lottery input example 7 with mixed Myanmar and numbers',
      () {
        // Arrange
        final String input = """
      12.1000
      34အပါ2000
      56ထိပ်3000
      78ဘိတ်4000
      """;

        // Act
        final String result = fix(input);

        // Assert
        final String expected =
            """
12=1000
34အပါ=2000
56ထိပ်=3000
78ဘိတ်=4000""".trim();

        expect(result, equals(expected));
      },
    );
  });
}
