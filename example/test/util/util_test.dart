import 'package:flutter_test/flutter_test.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/util/util.dart';

void main() {
  group('generateTwo', () {
    test("general test", () {
      final String input = '23=r1000';
      final RBetModel result = generateTwo(input);

      expect(result.total, 2000);
      expect(result.bets.length, 2);
    });
    test('should generate bets for အပူး (ahpu)', () {
      // Arrange
      final String input = 'အပူး=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(result.length, 10); // There are 10 pairs (00, 11, 22, ..., 99)
      expect(result[0].number, '00');
      expect(result[0].amount, 1000);
      expect(result[9].number, '99');
      expect(result[9].amount, 1000);

      // Verify all pairs are included
      final List<String> expectedNumbers = [
        '00',
        '11',
        '22',
        '33',
        '44',
        '55',
        '66',
        '77',
        '88',
        '99',
      ];
      for (int i = 0; i < expectedNumbers.length; i++) {
        expect(result[i].number, expectedNumbers[i]);
        expect(result[i].amount, 1000);
      }
    });

    test('should generate bets for အပါ (ahpar)', () {
      // Arrange
      final String input = '1အပါ=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(
        result.length,
        19,
      ); // 10 numbers with 1 at the beginning and 10 with 1 at the end, with 11 counted only once

      // Check for numbers with 1 at the beginning (10, 11, 12, ..., 19)
      // and numbers with 1 at the end (01, 11, 21, ..., 91)
      // Note: 11 appears in both sets but should only be included once
      final List<String> expectedNumbers = [
        '01',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
        '18',
        '19',
        '21',
        '31',
        '41',
        '51',
        '61',
        '71',
        '81',
        '91',
      ];

      for (final number in expectedNumbers) {
        expect(
          result.any((bet) => bet.number == number && bet.amount == 1000),
          true,
          reason: 'Expected number $number not found in result',
        );
      }
    });

    test('should generate bets for နက္ခက် (natkat)', () {
      // Arrange
      final String input = 'နက္ခက်=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(result.length, 10); // There are 10 natkat numbers

      // Verify all natkat numbers are included
      final List<String> expectedNumbers = [
        '07',
        '18',
        '24',
        '35',
        '96',
        '69',
        '42',
        '81',
        '70',
        '53',
      ];
      for (int i = 0; i < expectedNumbers.length; i++) {
        expect(result[i].number, expectedNumbers[i]);
        expect(result[i].amount, 1000);
      }
    });

    test('should generate bets for ပါဝါ (power)', () {
      // Arrange
      final String input = 'ပါဝါ=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(result.length, 10); // There are 10 power numbers

      // Verify all power numbers are included
      final List<String> expectedNumbers = [
        '05',
        '16',
        '27',
        '38',
        '49',
        '94',
        '83',
        '72',
        '61',
        '50',
      ];
      for (int i = 0; i < expectedNumbers.length; i++) {
        expect(result[i].number, expectedNumbers[i]);
        expect(result[i].amount, 1000);
      }
    });

    test('should generate bets for comma-separated numbers', () {
      // Arrange
      final String input = '12,34,56=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(result.length, 3); // Three numbers: 12, 34, 56
      expect(result[0].number, '12');
      expect(result[0].amount, 1000);
      expect(result[1].number, '34');
      expect(result[1].amount, 1000);
      expect(result[2].number, '56');
      expect(result[2].amount, 1000);
    });

    test(
      'should generate bets with reversed numbers when using r notation',
      () {
        // Arrange
        final String input = '12=1000r2000';

        // Act
        final List<IBetModel> result = generateTwo(input).bets;

        // Assert
        expect(result.length, 2); // Original number and its reverse
        expect(result[0].number, '12');
        expect(result[0].amount, 1000);
        expect(result[1].number, '21'); // Reversed
        expect(result[1].amount, 2000);
      },
    );

    test('should generate bets for ထိပ် (htate)', () {
      // Arrange
      final String input = '1 ထိပ်=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(
        result.length,
        10,
      ); // 10 numbers with 1 at the beginning (10, 11, 12, ..., 19)

      for (int i = 0; i < 10; i++) {
        expect(result[i].number, '1$i');
        expect(result[i].amount, 1000);
      }
    });

    test('should generate bets for ဘိတ် (bate)', () {
      // Arrange
      final String input = '1 ဘိတ်=1000';

      // Act
      final List<IBetModel> result = generateTwo(input).bets;

      // Assert
      expect(
        result.length,
        10,
      ); // 10 numbers with 1 at the end (01, 11, 21, ..., 91)

      for (int i = 0; i < 10; i++) {
        expect(result[i].number, '${i}1');
        expect(result[i].amount, 1000);
      }
    });
  });
}
