import 'package:example/data/models/bet_raw.dart';
import 'package:example/util/util.dart';
import 'package:flutter_test/flutter_test.dart';


void main() {
  group('generateThree', () {
    test('should generate bets for အပူး (ahpu)', () async {
      // Arrange
      final String input = 'အပူး=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 10); // There are 10 triplets (000, 111, 222, ..., 999)
      
      // Verify all triplets are included
      final List<String> expectedNumbers = ['000', '111', '222', '333', '444', '555', '666', '777', '888', '999'];
      for (int i = 0; i < expectedNumbers.length; i++) {
        expect(result[i].number, expectedNumbers[i]);
        expect(result[i].amount, 1000);
      }
    });
    
    test('should generate bets for ထိပ် (htate)', () async {
      // Arrange
      final String input = '1 ထိပ်=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 100); // 100 numbers with 1 at the beginning (100, 101, 102, ..., 199)
      
      for (int i = 0; i < 100; i++) {
        final expected = '1${i.toString().padLeft(2, '0')}';
        expect(
          result.any((bet) => bet.number == expected && bet.amount == 1000),
          true,
          reason: 'Expected number $expected not found in result',
        );
      }
    });
    
    test('should generate bets for ဘိတ် (bate)', () async {
      // Arrange
      final String input = '1 ဘိတ်=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 100); // 100 numbers with 1 at the end (001, 011, 021, ..., 991)
      
      for (int i = 0; i < 100; i++) {
        final expected = '${i.toString().padLeft(2, '0')}1';
        expect(
          result.any((bet) => bet.number == expected && bet.amount == 1000),
          true,
          reason: 'Expected number $expected not found in result',
        );
      }
    });
    
    test('should generate bets for လယ် (late)', () async {
      // Arrange
      final String input = '1 လယ်=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 100); // 100 numbers with 1 in the middle (010, 011, 012, ..., 919)
      
      for (int i = 0; i < 10; i++) {
        for (int j = 0; j < 10; j++) {
          final expected = '$i' + '1' + '$j';
          expect(
            result.any((bet) => bet.number == expected && bet.amount == 1000),
            true,
            reason: 'Expected number $expected not found in result',
          );
        }
      }
    });
    
    test('should generate bets for comma-separated numbers', () async {
      // Arrange
      final String input = '123,456,789=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 3); // Three numbers: 123, 456, 789
      expect(result[0].number, '123');
      expect(result[0].amount, 1000);
      expect(result[1].number, '456');
      expect(result[1].amount, 1000);
      expect(result[2].number, '789');
      expect(result[2].amount, 1000);
    });
    
    test('should generate bets with reversed numbers when using r notation', () async {
      // Arrange
      final String input = '123=1000r2000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 2); // Original number and its reverse
      expect(result[0].number, '123');
      expect(result[0].amount, 1000);
      expect(result[1].number, '321'); // Reversed
      expect(result[1].amount, 2000);
    });
    
    test('should generate bets for ပတ်လည် (patlon)', () async {
      // Arrange
      final String input = '123 ပတ်လည်=1000';
      
      // Act
      final List<IBetModel> result = await generateThree(input);
      
      // Assert
      expect(result.length, 6); // 6 permutations of 123: 123, 132, 213, 231, 312, 321
      
      final List<String> expectedNumbers = ['123', '132', '213', '231', '312', '321'];
      for (final number in expectedNumbers) {
        expect(
          result.any((bet) => bet.number == number && bet.amount == 1000),
          true,
          reason: 'Expected number $number not found in result',
        );
      }
    });
  });
}
