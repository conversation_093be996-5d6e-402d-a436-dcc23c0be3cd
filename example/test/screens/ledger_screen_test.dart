import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/screens/ledger_screen.dart';

void main() {
  group('LedgerScreen', () {
    testWidgets('should display export button', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LedgerScreen(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the export button is present
      expect(find.byIcon(Icons.download), findsOneWidget);
      expect(find.byTooltip('Export Ledger'), findsOneWidget);
    });

    testWidgets('should show export options when export button is tapped', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LedgerScreen(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Find and tap the export button
      final exportButton = find.byIcon(Icons.download);
      if (exportButton.evaluate().isNotEmpty) {
        await tester.tap(exportButton);
        await tester.pumpAndSettle();

        // Verify that the export options modal is shown
        expect(find.text('Export Ledger'), findsOneWidget);
        expect(find.text('Export as PDF'), findsOneWidget);
        expect(find.text('Export as Image'), findsOneWidget);
      }
    });

    testWidgets('should display no match selected state when no match is selected', (WidgetTester tester) async {
      // Build our app and trigger a frame
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LedgerScreen(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the no match selected state is displayed
      expect(find.text('Please select a match'), findsOneWidget);
      expect(find.text('Go to Matches screen to select a match'), findsOneWidget);
      expect(find.byIcon(Icons.calendar_month_outlined), findsOneWidget);
    });
  });
}
