import 'package:flutter/material.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/rauth.dart';
import 'package:rauth/rauth_init.dart';
import './firebase_options.dart';

import 'theme/app_theme.dart';
import 'screens/home_screen.dart';
import 'data/database/database_manager.dart';
import 'data/database/web_database_initializer.dart';
import 'data/providers/simple_providers.dart';
import 'data/services/shared_prefs_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await setupFirebase(DefaultFirebaseOptions.currentPlatform, init: false);

  try {
    // Initialize web database if running on web
    if (kIsWeb) {
      await WebDatabaseInitializer.initialize();
      debugPrint('Web database initialized successfully');
    }

    // Initialize the database
    await databaseManager.initializeDatabase();
    debugPrint('Database initialized successfully in main');

    // Initialize shared preferences service
    await SharedPrefsService.initialize();
    debugPrint('Shared preferences service initialized successfully');
  } catch (e) {
    debugPrint('Error in initialization: $e');
    // Continue with the app even if initialization fails
  }

  // Run app with ProviderScope for Riverpod
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the dark mode provider to update the theme when it changes
    final isDarkMode = ref.watch(isDarkModeProvider);

    return MaterialApp(
      title: 'Ledger App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      home: RAuth(const HomeScreen()),
    );
  }
}
