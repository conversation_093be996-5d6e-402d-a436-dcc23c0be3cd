import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

import '../data/models/bet.dart';
import '../data/models/bet_raw.dart';
import '../data/models/fighter.dart';
import '../theme/app_colors.dart';
import '../util/format_utils.dart';
import '../util/image_capture_util.dart';
import '../util/util.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../data/models/match.dart';
import '../data/providers/simple_providers.dart';

class SettlementModel {
  final BetRaw betRaws;
  final int luckyNumber;
  final int commission;
  final int netTotal;
  final Fighter fighter;

  SettlementModel({
    required this.betRaws,
    required this.luckyNumber,
    required this.fighter,
    required this.commission,
    required this.netTotal,
  });

  SettlementModel copyWith({
    BetRaw? betRaws,
    int? luckyNumber,
    Fighter? fighter,
    int? commission,
    int? netTotal,
  }) {
    return SettlementModel(
      betRaws: betRaws ?? this.betRaws,
      luckyNumber: luckyNumber ?? this.luckyNumber,
      fighter: fighter ?? this.fighter,
      commission: commission ?? this.commission,
      netTotal: netTotal ?? this.netTotal,
    );
  }
}

class SettlementScreen extends ConsumerStatefulWidget {
  const SettlementScreen({super.key});

  @override
  ConsumerState<SettlementScreen> createState() => _SettlementScreenState();
}

class _SettlementScreenState extends ConsumerState<SettlementScreen> {
  // Map to store global keys for each fighter card
  final Map<int, GlobalKey> _fighterCardKeys = {};

  @override
  Widget build(BuildContext context) {
    final betsStream = ref.watch(currentBetsProvider);
    final fightersStream = ref.watch(allFightersProvider);
    final Match? selectedMatch = ref.watch(selectedMatchProvider);

    return AppScaffold(
      title: "Settlement",
      body:
          selectedMatch == null
              ? _buildNoMatchSelectedState()
              : _buildSettlementContent(
                context,
                ref,
                fightersStream,
                betsStream,
                selectedMatch,
              ),
    );
  }

  Widget _buildNoMatchSelectedState() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calendar_month_outlined,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          Text(
            'Please select a match',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Go to Matches screen to select a match',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettlementContent(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<Fighter>> fightersStream,
    AsyncValue<List<Bet>> betsStream,
    Match selectedMatch,
  ) {
    return fightersStream.when(
      error: (error, stackTrace) => _buildErrorView(error.toString()),
      loading: () => const Center(child: CircularProgressIndicator()),
      data: (fighters) {
        return betsStream.when(
          data: (data) {
            final settlementModels = _calculateSettlements(
              fighters,
              data,
              selectedMatch,
            );

            if (settlementModels.isEmpty) {
              return _buildEmptyStateView();
            }

            return _buildReceiptView(context, selectedMatch, settlementModels);
          },
          error: (error, stackTrace) => _buildErrorView(error.toString()),
          loading: () => const Center(child: CircularProgressIndicator()),
        );
      },
    );
  }

  Widget _buildReceiptView(
    BuildContext context,
    Match match,
    List<SettlementModel> models,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Calculate totals
    int totalBets = 0;
    int totalCommission = 0;
    int totalLuckyPayout = 0;
    int totalNetAmount = 0;

    for (var model in models) {
      totalBets += model.betRaws.total;
      totalCommission += model.commission;
      totalLuckyPayout += (model.luckyNumber * model.fighter.odd).toInt();
      totalNetAmount += model.netTotal;
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Receipt body - Summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Summary",
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),
                _buildSummaryRow(
                  "Total",
                  FormatUtils.formatNumber(totalBets),
                  colorScheme.primary,
                ),
                const SizedBox(height: 8),
                _buildSummaryRow(
                  "Total Commission",
                  FormatUtils.formatNumber(totalCommission),
                  colorScheme.tertiary,
                ),
                const SizedBox(height: 8),
                _buildSummaryRow(
                  "Total Lucky Payout",
                  FormatUtils.formatNumber(totalLuckyPayout),
                  AppColors.success,
                ),
                const SizedBox(height: 8),
                Divider(
                  thickness: 1,
                  color: colorScheme.outline.withValues(alpha: 0.3),
                ),
                const SizedBox(height: 8),
                _buildSummaryRow(
                  "Net Amount",
                  FormatUtils.formatNumber(totalNetAmount),
                  totalNetAmount >= 0 ? AppColors.success : colorScheme.error,
                  isTotal: true,
                ),
              ],
            ),
          ),

          // Fighter Details Section Title
          Padding(
            padding: const EdgeInsets.only(top: 24, bottom: 12, left: 4),
            child: Text(
              "Fighter Details",
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
          ),

          // Fighter Cards
          ...models.map((model) => _buildFighterCard(model)),
        ],
      ),
    );
  }

  Widget _buildFighterCard(SettlementModel model) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDealer = model.fighter.isDealer;
    final isPositiveNet = model.netTotal >= 0;

    // Create a unique key for this fighter if it doesn't exist
    if (!_fighterCardKeys.containsKey(model.fighter.id)) {
      _fighterCardKeys[model.fighter.id] = GlobalKey();
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isDealer
                ? BorderSide(
                  color: colorScheme.error.withValues(alpha: 0.5),
                  width: 1.5,
                )
                : BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
      ),
      child: Stack(
        children: [
          // The actual card content with RepaintBoundary
          RepaintBoundary(
            key: _fighterCardKeys[model.fighter.id],
            child: Container(
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                gradient:
                    isDealer
                        ? LinearGradient(
                          begin: Alignment.topRight,
                          end: Alignment.bottomLeft,
                          colors: [
                            colorScheme.errorContainer.withValues(alpha: 0.1),
                            colorScheme.surface,
                          ],
                        )
                        : null,
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Fighter name and dealer badge
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            model.fighter.name,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isDealer)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.errorContainer,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: colorScheme.error.withValues(alpha: 0.5),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.store_rounded,
                                  size: 16,
                                  color: colorScheme.onErrorContainer,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  "Dealer",
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: colorScheme.onErrorContainer,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 16),
                    Divider(
                      height: 1,
                      color: colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 16),

                    // Details in a grid
                    Row(
                      children: [
                        // Left column
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildCardDetailItem(
                                "Total",
                                FormatUtils.formatNumber(model.betRaws.total),
                                colorScheme.primary,
                              ),
                              const SizedBox(height: 12),
                              _buildCardDetailItem(
                                "Commission",
                                FormatUtils.formatNumber(model.commission),
                                colorScheme.tertiary,
                              ),
                            ],
                          ),
                        ),

                        // Right column
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildCardDetailItem(
                                "Lucky Payout",
                                FormatUtils.formatNumber(model.luckyNumber),
                                AppColors.success,
                              ),
                              const SizedBox(height: 12),
                              _buildCardDetailItem(
                                "Net Amount",
                                FormatUtils.formatNumber(model.netTotal),
                                isPositiveNet
                                    ? AppColors.success
                                    : colorScheme.error,
                                isBold: true,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Save button overlay
          Positioned(
            top: 8,
            right: 8,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () => _saveCardAsImage(model),
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colorScheme.surface.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Icon(
                    Icons.save_alt_rounded,
                    size: 20,
                    color: colorScheme.primary,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Method to save a fighter card as an image
  Future<void> _saveCardAsImage(SettlementModel model) async {
    final key = _fighterCardKeys[model.fighter.id];
    if (key != null) {
      // Show a loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Preparing to save image...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      try {
        // First check if we have storage permission
        bool hasPermission = await Permission.storage.status.isGranted;
        if (!hasPermission) {
          // Show permission dialog with explanation
          if (mounted) {
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Storage Permission Required'),
                    content: const Text(
                      'To save fighter cards as images, this app needs permission to access your device storage. '
                      'Please grant storage permission in the next dialog.',
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Continue with the save process
                          _performImageSave(key, model);
                        },
                        child: const Text('Continue'),
                      ),
                    ],
                  ),
            );
          }
        } else {
          // Already have permission, proceed with saving
          await _performImageSave(key, model);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: ${e.toString()}'),
              backgroundColor: AppColors.error,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      }
    }
  }

  // Helper method to perform the actual image saving
  Future<void> _performImageSave(GlobalKey key, SettlementModel model) async {
    try {
      final success = await ImageCaptureUtil.captureAndSaveWidget(
        key,
        context,
        name:
            'fighter_${model.fighter.name}_${DateTime.now().millisecondsSinceEpoch}.png',
        showResult: false, // We'll handle our own result messages
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Saved ${model.fighter.name}\'s card to gallery'),
              backgroundColor: AppColors.success,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to save ${model.fighter.name}\'s card'),
              backgroundColor: AppColors.error,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Widget _buildCardDetailItem(
    String label,
    String value,
    Color valueColor, {
    bool isBold = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: valueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value,
    Color color, {
    bool isTotal = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style:
              isTotal
                  ? theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  )
                  : theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                  ),
        ),
        Text(
          value,
          style:
              isTotal
                  ? theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  )
                  : theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
        ),
      ],
    );
  }

  List<SettlementModel> _calculateSettlements(
    List<Fighter> fighters,
    List<Bet> bets,
    Match selectedMatch,
  ) {
    List<SettlementModel> settlementModels = [];

    for (Fighter fighter in fighters) {
      SettlementModel settlementModel = SettlementModel(
        betRaws: BetRaw(total: 0, bets: []),
        luckyNumber: 0,
        fighter: fighter,
        commission: 0,
        netTotal: 0,
      );

      List<Bet> currentFighterBets =
          bets.where((element) => element.fighter == fighter.id).toList();

      if (currentFighterBets.isEmpty) {
        continue; // Skip fighters with no bets
      }

      BetRaw betRaws = multiGenerateTwo(
        currentFighterBets
            .where(
              (e) =>
                  e.number.toString().trim().isNotEmpty &&
                  e.amount.toString().trim().isNotEmpty,
            )
            .map((e) => "${e.number}=${e.amount}")
            .join('\n')
            .trimRight(),
      );

      int commission = ((betRaws.total * fighter.commission) / 100).toInt();

      log("Total: ${betRaws.total}, Commission: $commission");

      int lucky = 0;
      if (selectedMatch.luckyNo != null && selectedMatch.luckyNo!.isNotEmpty) {
        lucky = betRaws.bets
            .map((e) => e.bets)
            .expand((element) => element)
            .where((e) => e.number == selectedMatch.luckyNo!)
            .fold(0, (previousValue, d) => previousValue + d.amount);
      }

      double netTotal =
          fighter.isDealer
              ? -betRaws.total + (commission + (lucky * fighter.odd))
              : betRaws.total - (commission + (lucky * fighter.odd));

      settlementModel = settlementModel.copyWith(
        betRaws: betRaws,
        luckyNumber: lucky,
        commission: commission,
        netTotal: netTotal.toInt(),
      );

      settlementModels.add(settlementModel);
    }

    return settlementModels;
  }

  Widget _buildEmptyStateView() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          Text(
            "No Bets Found",
            style: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            "There are no bets for this match yet.",
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String errorMessage) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              color: colorScheme.error,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              "Error Loading Data",
              style: theme.textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
