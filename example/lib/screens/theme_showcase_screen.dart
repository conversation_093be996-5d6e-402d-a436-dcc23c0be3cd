import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../widgets/dialogs/app_dialogs.dart';

/// A screen that showcases all the theme components and UI elements.
class ThemeShowcaseScreen extends StatefulWidget {
  const ThemeShowcaseScreen({super.key});

  @override
  State<ThemeShowcaseScreen> createState() => _ThemeShowcaseScreenState();
}

class _ThemeShowcaseScreenState extends State<ThemeShowcaseScreen> {
  final _formKey = GlobalKey<FormState>();
  String _textFieldValue = '';
  bool _switchValue = false;
  double _sliderValue = 0.5;
  int _radioValue = 0;
  bool _checkboxValue = false;
  
  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'Theme Showcase',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection('Typography'),
            _buildTypographyShowcase(),
            _buildDivider(),
            
            _buildSection('Colors'),
            _buildColorsShowcase(),
            _buildDivider(),
            
            _buildSection('Buttons'),
            _buildButtonsShowcase(),
            _buildDivider(),
            
            _buildSection('Form Elements'),
            _buildFormElementsShowcase(),
            _buildDivider(),
            
            _buildSection('Dialogs'),
            _buildDialogsShowcase(),
            _buildDivider(),
            
            _buildSection('Cards'),
            _buildCardsShowcase(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSection(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.headlineMedium,
      ),
    );
  }
  
  Widget _buildDivider() {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 16.0),
      child: Divider(),
    );
  }
  
  Widget _buildTypographyShowcase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Display Large', style: Theme.of(context).textTheme.displayLarge),
        Text('Display Medium', style: Theme.of(context).textTheme.displayMedium),
        Text('Display Small', style: Theme.of(context).textTheme.displaySmall),
        Text('Headline Medium', style: Theme.of(context).textTheme.headlineMedium),
        Text('Title Large', style: Theme.of(context).textTheme.titleLarge),
        Text('Title Medium', style: Theme.of(context).textTheme.titleMedium),
        Text('Body Large', style: Theme.of(context).textTheme.bodyLarge),
        Text('Body Medium', style: Theme.of(context).textTheme.bodyMedium),
        Text('Body Small', style: Theme.of(context).textTheme.bodySmall),
        Text('Label Large', style: Theme.of(context).textTheme.labelLarge),
        Text('Label Small', style: Theme.of(context).textTheme.labelSmall),
      ],
    );
  }
  
  Widget _buildColorsShowcase() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        _buildColorItem('Primary', AppColors.primary),
        _buildColorItem('Primary Light', AppColors.primaryLight),
        _buildColorItem('Primary Dark', AppColors.primaryDark),
        _buildColorItem('Secondary', AppColors.secondary),
        _buildColorItem('Secondary Light', AppColors.secondaryLight),
        _buildColorItem('Secondary Dark', AppColors.secondaryDark),
        _buildColorItem('Success', AppColors.success),
        _buildColorItem('Warning', AppColors.warning),
        _buildColorItem('Error', AppColors.error),
        _buildColorItem('Info', AppColors.info),
        _buildColorItem('Income', AppColors.income),
        _buildColorItem('Expense', AppColors.expense),
        _buildColorItem('Transfer', AppColors.transfer),
        _buildColorItem('Investment', AppColors.investment),
      ],
    );
  }
  
  Widget _buildColorItem(String name, Color color) {
    final textColor = color.computeLuminance() > 0.5 ? Colors.black : Colors.white;
    
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
  
  Widget _buildButtonsShowcase() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        ElevatedButton(
          onPressed: () {},
          child: const Text('Elevated Button'),
        ),
        OutlinedButton(
          onPressed: () {},
          child: const Text('Outlined Button'),
        ),
        TextButton(
          onPressed: () {},
          child: const Text('Text Button'),
        ),
        ElevatedButton.icon(
          onPressed: () {},
          icon: const Icon(Icons.add),
          label: const Text('With Icon'),
        ),
        ElevatedButton(
          onPressed: null,
          child: const Text('Disabled'),
        ),
      ],
    );
  }
  
  Widget _buildFormElementsShowcase() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            decoration: const InputDecoration(
              labelText: 'Text Field',
              hintText: 'Enter some text',
            ),
            onChanged: (value) {
              setState(() {
                _textFieldValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            decoration: const InputDecoration(
              labelText: 'Password Field',
              hintText: 'Enter password',
              errorText: 'This is an error message',
            ),
            obscureText: true,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Text('Switch:'),
              const SizedBox(width: 16),
              Switch(
                value: _switchValue,
                onChanged: (value) {
                  setState(() {
                    _switchValue = value;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text('Slider: ${(_sliderValue * 100).toInt()}%'),
          Slider(
            value: _sliderValue,
            onChanged: (value) {
              setState(() {
                _sliderValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Radio(
                value: 0,
                groupValue: _radioValue,
                onChanged: (value) {
                  setState(() {
                    _radioValue = value as int;
                  });
                },
              ),
              const Text('Option 1'),
              const SizedBox(width: 16),
              Radio(
                value: 1,
                groupValue: _radioValue,
                onChanged: (value) {
                  setState(() {
                    _radioValue = value as int;
                  });
                },
              ),
              const Text('Option 2'),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Checkbox(
                value: _checkboxValue,
                onChanged: (value) {
                  setState(() {
                    _checkboxValue = value ?? false;
                  });
                },
              ),
              const Text('Checkbox'),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildDialogsShowcase() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        ElevatedButton(
          onPressed: () {
            AppDialogs.showConfirmDialog(
              context: context,
              title: 'Confirm Action',
              message: 'Are you sure you want to perform this action?',
            );
          },
          child: const Text('Confirm Dialog'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.warning,
            foregroundColor: Colors.black,
          ),
          onPressed: () {
            AppDialogs.showWarningDialog(
              context: context,
              title: 'Warning',
              message: 'This action may have consequences. Proceed with caution.',
            );
          },
          child: const Text('Warning Dialog'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
          ),
          onPressed: () {
            AppDialogs.showErrorDialog(
              context: context,
              title: 'Error',
              message: 'An error occurred while processing your request.',
            );
          },
          child: const Text('Error Dialog'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.info,
            foregroundColor: Colors.white,
          ),
          onPressed: () {
            AppDialogs.showInfoDialog(
              context: context,
              title: 'Information',
              message: 'This is an informational message for the user.',
            );
          },
          child: const Text('Info Dialog'),
        ),
      ],
    );
  }
  
  Widget _buildCardsShowcase() {
    return Column(
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Basic Card',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'This is a basic card with some content.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Transaction Card',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Grocery Shopping'),
                    Text(
                      '-\$45.99',
                      style: AppTypography.amountMedium.copyWith(
                        color: AppColors.expense,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Salary Deposit'),
                    Text(
                      '+\$2,500.00',
                      style: AppTypography.amountMedium.copyWith(
                        color: AppColors.income,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
