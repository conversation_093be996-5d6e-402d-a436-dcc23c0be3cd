// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:intl/intl.dart';
// import 'package:example/data/models/bet.dart';
// import 'package:example/data/models/fighter.dart';

// import 'package:example/data/providers/simple_providers.dart';
// import 'package:example/screens/voucher_detail_screen.dart';
// import 'package:example/theme/app_colors.dart';
// import 'package:example/theme/app_typography.dart';
// import 'package:example/util/format_utils.dart';
// import 'package:example/widgets/scaffold/app_scaffold.dart';

// /// Sort order for vouchers
// enum VoucherSortOrder {
//   /// Newest first
//   newestFirst,

//   /// Oldest first
//   oldestFirst,
// }

// /// Filter state for vouchers
// class VoucherFilterState {
//   /// Sort order
//   final VoucherSortOrder sortOrder;

//   /// Selected fighter IDs (empty means all)
//   final List<int> selectedFighterIds;

//   /// Show only dealers
//   final bool onlyDealers;

//   /// Constructor
//   const VoucherFilterState({
//     this.sortOrder = VoucherSortOrder.newestFirst,
//     this.selectedFighterIds = const [],
//     this.onlyDealers = false,
//   });

//   /// Create a copy with updated values
//   VoucherFilterState copyWith({
//     VoucherSortOrder? sortOrder,
//     List<int>? selectedFighterIds,
//     bool? onlyDealers,
//   }) {
//     return VoucherFilterState(
//       sortOrder: sortOrder ?? this.sortOrder,
//       selectedFighterIds: selectedFighterIds ?? this.selectedFighterIds,
//       onlyDealers: onlyDealers ?? this.onlyDealers,
//     );
//   }
// }

// /// Provider for voucher filter state
// final voucherFilterProvider = StateProvider<VoucherFilterState>((ref) {
//   return const VoucherFilterState();
// });

// /// Screen that displays vouchers (bets grouped by timestamp)
// class VoucherScreen extends ConsumerStatefulWidget {
//   const VoucherScreen({super.key});

//   @override
//   ConsumerState<VoucherScreen> createState() => _VoucherScreenState();
// }

// class _VoucherScreenState extends ConsumerState<VoucherScreen> {
//   @override
//   Widget build(BuildContext context) {
//     final selectedMatch = ref.watch(selectedMatchProvider);
//     final vouchers = ref.watch(vouchersProvider);
//     final filterState = ref.watch(voucherFilterProvider);

//     return AppScaffold(
//       title: 'Vouchers',
//       actions: [
//         // Filter button
//         IconButton(
//           icon: const Icon(Icons.filter_list),
//           tooltip: 'Filter',
//           onPressed: () => _showFilterDialog(context),
//         ),
//       ],
//       body: Column(
//         children: [
//           // Match info

//           // Vouchers list
//           Expanded(
//             child: vouchers.when(
//               data:
//                   (data) => FutureBuilder<Widget>(
//                     future: _buildVouchersListAsync(data),
//                     builder: (context, snapshot) {
//                       if (snapshot.connectionState == ConnectionState.waiting) {
//                         return const Center(child: CircularProgressIndicator());
//                       } else if (snapshot.hasError) {
//                         return Center(child: Text('Error: ${snapshot.error}'));
//                       } else {
//                         return snapshot.data!;
//                       }
//                     },
//                   ),
//               loading: () => const Center(child: CircularProgressIndicator()),
//               error:
//                   (error, stackTrace) =>
//                       Center(child: Text('Error: ${error.toString()}')),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   Future<Widget> _buildVouchersListAsync(List<Bet> vouchers) async {
//     if (vouchers.isEmpty) {
//       final selectedMatch = ref.watch(selectedMatchProvider);
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(Icons.receipt_long, size: 64, color: Colors.grey[400]),
//             const SizedBox(height: 16),
//             Text(
//               'No vouchers found',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.w500,
//                 color: Colors.grey[600],
//               ),
//             ),
//             const SizedBox(height: 8),
//             Text(
//               selectedMatch == null
//                   ? 'Please select a match first'
//                   : 'No bets have been saved for this match',
//               style: TextStyle(fontSize: 14, color: Colors.grey[500]),
//             ),
//           ],
//         ),
//       );
//     }

//     // Apply filters
//     final filterState = ref.watch(voucherFilterProvider);

//     // Filter by fighter IDs if any are selected
//     List<Bet> filteredVouchers = vouchers;
//     if (filterState.selectedFighterIds.isNotEmpty) {
//       filteredVouchers =
//           vouchers
//               .where(
//                 (bet) => filterState.selectedFighterIds.contains(bet.fighter),
//               )
//               .toList();
//     }

//     // Filter by dealer status if needed
//     if (filterState.onlyDealers) {
//       // Get all fighters first
//       final allFightersAsync = ref.read(allFightersProvider.future);
//       final allFighters = await allFightersAsync;

//       // Create a map of fighter IDs to dealer status for quick lookup
//       final Map<int, bool> dealerMap = {
//         for (var fighter in allFighters) fighter.id: fighter.isDealer,
//       };

//       // Filter vouchers by dealer status
//       filteredVouchers =
//           filteredVouchers
//               .where((bet) => dealerMap[bet.fighter] ?? false)
//               .toList();
//     }

//     // If filtered list is empty after applying filters, show a message
//     if (filteredVouchers.isEmpty) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(Icons.filter_list, size: 64, color: Colors.grey[400]),
//             const SizedBox(height: 16),
//             Text(
//               'No vouchers match the filters',
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.w500,
//                 color: Colors.grey[600],
//               ),
//             ),
//             const SizedBox(height: 8),
//             TextButton.icon(
//               icon: const Icon(Icons.clear_all),
//               label: const Text('Clear Filters'),
//               onPressed: () {
//                 ref.read(voucherFilterProvider.notifier).state =
//                     const VoucherFilterState();
//               },
//             ),
//           ],
//         ),
//       );
//     }

//     // Group vouchers by timestamp and fighter
//     final Map<String, List<Bet>> groupedVouchers = {};
//     for (final voucher in filteredVouchers) {
//       // Extract the time part (HH:MM:SS) from the timestamp to group by time and fighter
//       DateTime dateTime;
//       try {
//         dateTime = DateTime.parse(voucher.timestamp);
//       } catch (e) {
//         // If timestamp can't be parsed, use current time
//         dateTime = DateTime.now();
//       }
//       final String timeKey = DateFormat('HH:mm:ss').format(dateTime);
//       final String groupKey = '$timeKey-${voucher.fighter}';

//       if (!groupedVouchers.containsKey(groupKey)) {
//         groupedVouchers[groupKey] = [];
//       }
//       groupedVouchers[groupKey]!.add(voucher);
//     }

//     // Sort the groups based on timestamp
//     final sortedKeys = groupedVouchers.keys.toList();
//     if (filterState.sortOrder == VoucherSortOrder.newestFirst) {
//       sortedKeys.sort((a, b) => b.compareTo(a)); // Descending (newest first)
//     } else {
//       sortedKeys.sort(); // Ascending (oldest first)
//     }

//     return Column(
//       children: [
//         // Active filters indicator
//         if (filterState.selectedFighterIds.isNotEmpty ||
//             filterState.onlyDealers)
//           _buildActiveFiltersBar(filterState),

//         // Voucher list
//         Expanded(
//           child: ListView.builder(
//             itemCount: sortedKeys.length,
//             itemBuilder: (context, index) {
//               final groupKey = sortedKeys[index];
//               final voucherGroup = groupedVouchers[groupKey]!;
//               // Use the timestamp from the first bet in the group
//               return _buildVoucherCard(
//                 voucherGroup.first.timestamp,
//                 voucherGroup,
//               );
//             },
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildVoucherCard(String timestamp, List<Bet> vouchers) {
//     // Calculate total amount
//     int totalAmount = 0;
//     for (final voucher in vouchers) {
//       final amountStr = voucher.amount.replaceAll(RegExp(r'[^0-9]'), '');
//       final amount = int.tryParse(amountStr) ?? 0;
//       totalAmount += amount;
//     }

//     // Get fighter details for the first voucher (all vouchers in a group have the same fighter)
//     final fighterId = vouchers.first.fighter;
//     final fighterDetails = ref.watch(fighterDetailsProvider(fighterId));

//     // Format timestamp for display
//     DateTime dateTime;
//     try {
//       dateTime = DateTime.parse(timestamp);
//     } catch (e) {
//       // Handle invalid timestamp format
//       dateTime = DateTime.now();
//     }
//     final String formattedTime = DateFormat('h:mm a').format(dateTime);

//     return GestureDetector(
//       onTap: () {
//         Navigator.of(context).push(
//           MaterialPageRoute(
//             builder:
//                 (context) => VoucherDetailScreen(
//                   timestamp: timestamp,
//                   vouchers: vouchers,
//                 ),
//           ),
//         );
//       },
//       child: Card(
//         margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Header with timestamp and fighter info
//             Container(
//               padding: const EdgeInsets.all(16),
//               decoration: BoxDecoration(
//                 color: AppColors.primary.withAlpha(25),
//                 borderRadius: const BorderRadius.only(
//                   topLeft: Radius.circular(12),
//                   topRight: Radius.circular(12),
//                 ),
//               ),
//               child: Row(
//                 children: [
//                   const Icon(Icons.receipt, color: AppColors.primary),
//                   const SizedBox(width: 8),
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           'Voucher at $formattedTime',
//                           style: AppTypography.subtitle,
//                         ),
//                         fighterDetails.when(
//                           data:
//                               (fighter) =>
//                                   fighter != null
//                                       ? Text(
//                                         'Fighter: ${fighter.name}',
//                                         style: TextStyle(
//                                           color:
//                                               fighter.isDealer
//                                                   ? AppColors.error
//                                                   : null,
//                                           fontWeight:
//                                               fighter.isDealer
//                                                   ? FontWeight.bold
//                                                   : null,
//                                         ),
//                                       )
//                                       : const Text('Unknown Fighter'),
//                           loading: () => const Text('Loading fighter...'),
//                           error: (_, __) => const Text('Error loading fighter'),
//                         ),
//                       ],
//                     ),
//                   ),
//                   Container(
//                     padding: const EdgeInsets.symmetric(
//                       horizontal: 12,
//                       vertical: 6,
//                     ),
//                     decoration: BoxDecoration(
//                       color: AppColors.primary,
//                       borderRadius: BorderRadius.circular(16),
//                     ),
//                     child: Text(
//                       FormatUtils.formatNumber(totalAmount),
//                       style: const TextStyle(
//                         color: Colors.white,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),

//             // Preview of bets (show only first 3)
//             ListView.builder(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               itemCount: vouchers.length > 3 ? 3 : vouchers.length,
//               itemBuilder: (context, index) {
//                 final bet = vouchers[index];
//                 return _buildBetItem(bet);
//               },
//             ),

//             // "See more" indicator if there are more than 3 bets
//             if (vouchers.length > 3)
//               Container(
//                 width: double.infinity,
//                 padding: const EdgeInsets.symmetric(vertical: 8),
//                 alignment: Alignment.center,
//                 decoration: BoxDecoration(
//                   color: Colors.grey.shade100,
//                   borderRadius: const BorderRadius.only(
//                     bottomLeft: Radius.circular(12),
//                     bottomRight: Radius.circular(12),
//                   ),
//                 ),
//                 child: Text(
//                   'See ${vouchers.length - 3} more bets...',
//                   style: TextStyle(
//                     color: AppColors.primary,
//                     fontWeight: FontWeight.w500,
//                   ),
//                 ),
//               ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildBetItem(Bet bet) {
//     final amountStr = bet.amount.replaceAll(RegExp(r'[^0-9]'), '');
//     final amount = int.tryParse(amountStr) ?? 0;

//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       child: Row(
//         children: [
//           Expanded(
//             flex: 3,
//             child: Text(
//               bet.number,
//               style: const TextStyle(fontWeight: FontWeight.w500),
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Text(
//               FormatUtils.formatNumber(amount),
//               style: const TextStyle(
//                 fontWeight: FontWeight.bold,
//                 color: AppColors.primary,
//               ),
//               textAlign: TextAlign.end,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // Filter dialog
//   Future<void> _showFilterDialog(BuildContext context) async {
//     final currentFilterState = ref.read(voucherFilterProvider);
//     final allFighters = await ref.read(allFightersProvider.future);

//     // Create a temporary state for the dialog
//     VoucherSortOrder tempSortOrder = currentFilterState.sortOrder;
//     List<int> tempSelectedFighterIds = List.from(
//       currentFilterState.selectedFighterIds,
//     );
//     bool tempOnlyDealers = currentFilterState.onlyDealers;

//     await showDialog(
//       context: context,
//       builder: (context) {
//         return StatefulBuilder(
//           builder: (context, setState) {
//             return AlertDialog(
//               title: const Text('Filter Vouchers'),
//               content: SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // Sort order
//                     const Text(
//                       'Sort Order',
//                       style: TextStyle(fontWeight: FontWeight.bold),
//                     ),
//                     RadioListTile<VoucherSortOrder>(
//                       title: const Text('Newest First'),
//                       value: VoucherSortOrder.newestFirst,
//                       groupValue: tempSortOrder,
//                       onChanged: (value) {
//                         setState(() {
//                           tempSortOrder = value!;
//                         });
//                       },
//                     ),
//                     RadioListTile<VoucherSortOrder>(
//                       title: const Text('Oldest First'),
//                       value: VoucherSortOrder.oldestFirst,
//                       groupValue: tempSortOrder,
//                       onChanged: (value) {
//                         setState(() {
//                           tempSortOrder = value!;
//                         });
//                       },
//                     ),
//                     const Divider(),

//                     // Only dealers checkbox
//                     CheckboxListTile(
//                       title: const Text('Show Only Dealers'),
//                       value: tempOnlyDealers,
//                       onChanged: (value) {
//                         setState(() {
//                           tempOnlyDealers = value!;
//                         });
//                       },
//                     ),
//                     const Divider(),

//                     // Fighter selection
//                     const Text(
//                       'Filter by Fighters',
//                       style: TextStyle(fontWeight: FontWeight.bold),
//                     ),
//                     const SizedBox(height: 8),

//                     // Select all / clear all
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         TextButton(
//                           onPressed: () {
//                             setState(() {
//                               tempSelectedFighterIds =
//                                   allFighters.map((f) => f.id).toList();
//                             });
//                           },
//                           child: const Text('Select All'),
//                         ),
//                         TextButton(
//                           onPressed: () {
//                             setState(() {
//                               tempSelectedFighterIds = [];
//                             });
//                           },
//                           child: const Text('Clear All'),
//                         ),
//                       ],
//                     ),

//                     // Fighter list
//                     Container(
//                       constraints: const BoxConstraints(maxHeight: 200),
//                       child: ListView.builder(
//                         shrinkWrap: true,
//                         itemCount: allFighters.length,
//                         itemBuilder: (context, index) {
//                           final fighter = allFighters[index];
//                           return CheckboxListTile(
//                             title: Text(
//                               fighter.name,
//                               style: TextStyle(
//                                 color:
//                                     fighter.isDealer ? AppColors.error : null,
//                                 fontWeight:
//                                     fighter.isDealer ? FontWeight.bold : null,
//                               ),
//                             ),
//                             secondary:
//                                 fighter.isDealer
//                                     ? const Icon(
//                                       Icons.person,
//                                       color: AppColors.error,
//                                     )
//                                     : const Icon(Icons.person),
//                             value: tempSelectedFighterIds.contains(fighter.id),
//                             onChanged: (value) {
//                               setState(() {
//                                 if (value!) {
//                                   tempSelectedFighterIds.add(fighter.id);
//                                 } else {
//                                   tempSelectedFighterIds.remove(fighter.id);
//                                 }
//                               });
//                             },
//                           );
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               actions: [
//                 TextButton(
//                   onPressed: () {
//                     Navigator.of(context).pop();
//                   },
//                   child: const Text('Cancel'),
//                 ),
//                 TextButton(
//                   onPressed: () {
//                     // Apply the filters
//                     ref
//                         .read(voucherFilterProvider.notifier)
//                         .state = VoucherFilterState(
//                       sortOrder: tempSortOrder,
//                       selectedFighterIds: tempSelectedFighterIds,
//                       onlyDealers: tempOnlyDealers,
//                     );
//                     Navigator.of(context).pop();
//                   },
//                   child: const Text('Apply'),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//     );
//   }

//   // Active filters bar
//   Widget _buildActiveFiltersBar(VoucherFilterState filterState) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//       color: Colors.grey.shade100,
//       child: Row(
//         children: [
//           const Icon(Icons.filter_list, size: 16),
//           const SizedBox(width: 8),
//           Expanded(
//             child: Text(
//               _getActiveFiltersText(filterState),
//               style: const TextStyle(fontSize: 12),
//             ),
//           ),
//           TextButton(
//             onPressed: () {
//               ref.read(voucherFilterProvider.notifier).state =
//                   const VoucherFilterState();
//             },
//             child: const Text('Clear'),
//             style: TextButton.styleFrom(
//               padding: const EdgeInsets.symmetric(horizontal: 8),
//               minimumSize: const Size(0, 0),
//               tapTargetSize: MaterialTapTargetSize.shrinkWrap,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // Get text description of active filters
//   String _getActiveFiltersText(VoucherFilterState filterState) {
//     final List<String> filters = [];

//     // Sort order
//     filters.add(
//       filterState.sortOrder == VoucherSortOrder.newestFirst
//           ? 'Newest First'
//           : 'Oldest First',
//     );

//     // Fighter filter
//     if (filterState.selectedFighterIds.isNotEmpty) {
//       filters.add('${filterState.selectedFighterIds.length} Fighters');
//     }

//     // Dealer filter
//     if (filterState.onlyDealers) {
//       filters.add('Only Dealers');
//     }

//     return filters.join(' • ');
//   }
// }
