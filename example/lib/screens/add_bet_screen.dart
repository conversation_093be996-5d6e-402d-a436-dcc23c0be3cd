import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rich_text_controller/rich_text_controller.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/util/bet_checker.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/widgets/dialogs/app_dialogs.dart';
import 'package:example/widgets/keyboard/r_keyboard.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

import '../data/providers/bet_notifier.dart';
import '../widgets/keyboard/built_in_keyboard.dart';

class AddBetScreen extends ConsumerStatefulWidget {
  const AddBetScreen({super.key});

  @override
  ConsumerState<AddBetScreen> createState() => _AddBetScreenState();
}

class _AddBetScreenState extends ConsumerState<AddBetScreen> {
  late final RichTextController _controller;
  Fighter? _selectedFighter;
  List<Fighter> _fighters = [];
  bool isLoading = true;

  // Global key for accessing scaffold messenger
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  @override
  void initState() {
    super.initState();
    _loadFighters();

    _controller = RichTextController(
      onMatch: (match) {
        // Use debugPrint instead of print for logging
        debugPrint('Match found: $match');
      },
      targetMatches: [
        MatchTargetItem(
          style: const TextStyle(
            color: Colors.green,
            fontWeight: FontWeight.bold,
          ),
          regex: RegExp(
            r'(\d+00[^0-9]*r[^0-9]*\d+|\d+50[^0-9]*r\d+|\b\d+(?:50|00)\b(?![a-zA-Z])|\b\d+00\b|\b\d+(?:50|00)\b(?![a-zA-Z]))|r[^0-9]*\d+0|=50r\d{2,}0|\b=50\b',
          ),
        ),
        MatchTargetItem(
          style: const TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.bold,
            fontStyle: FontStyle.italic,
          ),
          regex: RegExp(r'\d{3,}'),
        ),
      ],
    );
  }

  Future<void> _loadFighters() async {
    setState(() {
      isLoading = true;
    });

    try {
      final fighterService = await ref.read(fighterServiceProvider.future);
      final fighters = await fighterService.getAllFighters();

      setState(() {
        _fighters = fighters;
        isLoading = false;
        if (fighters.isNotEmpty) {
          // _selectedFighter = fighters.first;
        }
      });
    } catch (e) {
      setState(() {
        isLoading = false;
        _fighters = [];
      });

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load fighters: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bets = ref.watch(betNotifierProvider);
    final betNotifier = ref.read(betNotifierProvider.notifier);

    // Handle animations when bets list changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // _handleBetsListChanges(bets);
    });

    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: AppScaffold(
        title: "Total: ${FormatUtils.formatNumber(bets.total)}",
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear All Bets',
            onPressed: () {
              AppDialogs.showWarningDialog(
                context: context,
                title: 'Clear Bets',
                message: 'Are you sure you want to clear all bets?',
                onConfirm: () {
                  betNotifier.clearBets();
                },
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'Save Bets',
            onPressed:
                bets.bets.isEmpty
                    ? null
                    : () async {
                      // If no fighter is selected, show the fighter selection first
                      if (_selectedFighter == null) {
                        await AppDialogs.showFighterSelectionBottomSheet(
                          context: context,
                          fighters: _fighters,
                          selectedFighter: _selectedFighter,
                          onFighterSelected: (fighter) {
                            setState(() {
                              _selectedFighter = fighter;
                            });
                          },
                        );

                        // If still no fighter selected, return
                      }

                      // Now show confirmation dialog
                      if (mounted &&
                          context.mounted &&
                          _selectedFighter != null) {
                        final currentContext = context;
                        AppDialogs.showConfirmDialog(
                          context: currentContext,
                          title: "Save Bets",
                          message:
                              "Are you sure you want to save bets to ${_selectedFighter!.name}?",
                          onConfirm: () async {
                            if (mounted && currentContext.mounted) {
                              await betNotifier.saveBets(
                                ref,
                                currentContext,
                                _selectedFighter!,
                              );
                              setState(() {
                                _selectedFighter = null;
                              });
                            }
                          },
                          onCancel: () {
                            setState(() {
                              _selectedFighter = null;
                            });
                          },
                        );
                      }
                    },
          ),
        ],
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.max,
            children: [
              // Fighter selection
              // _buildFighterSelection(),

              // Bets list
              Expanded(child: _buildBetsList(bets.bets, betNotifier)),

              if (ref.read(useBuiltInKeyboardProvider)) ...[
                BuiltInKeyboard(),
              ] else ...[
                RKeyboard(
                  controller: _controller,
                  onAddPressed: () async {
                    final text = _controller.text.trim();
                    checkBets(
                      text,
                      context,
                      ref,
                      onSuccess: (b) {
                        _controller.clear();
                      },
                    );
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Build fighter selection display

  // Show fighter selection bottom sheet

  // Note: We're using the bottom sheet version by default
  // but keeping this code as a reference in case we want to switch
  // to the dialog version in the future.

  Widget _buildBetsList(List<RBetModel> bets, BetNotifier betNotifier) {
    if (bets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No bets yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Use the keyboard below to add bets',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: bets.length,
      itemBuilder: (context, index) {
        if (index >= bets.length) return const SizedBox(); // Safety check
        final bet = bets[index];
        return _buildBetCard(bet, betNotifier);
      },
    );
  }

  // No unused methods

  Widget _buildBetCard(RBetModel bet, BetNotifier betNotifier) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Bet number
            Expanded(
              flex: 3,
              child: Text(
                bet.number,
                style: TextStyle(
                  fontSize: bet.number.length > 8 ? 14 : 16,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Amount
            Expanded(
              flex: 2,
              child: Text(
                bet.amount,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
                textAlign: TextAlign.end,
              ),
            ),

            // Delete button
            IconButton(
              icon: const Icon(Icons.delete_outline, size: 20),
              color: AppColors.error,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              visualDensity: VisualDensity.compact,
              onPressed: () {
                // Find the index of the bet to remove

                betNotifier.removeBet(bet);
              },
            ),
          ],
        ),
      ),
    );
  }
}
