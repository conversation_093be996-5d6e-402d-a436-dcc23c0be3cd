import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';

import '../data/models/match.dart';
import '../data/providers/simple_providers.dart';
import '../data/services/data_export_service.dart';
import '../data/services/shared_prefs_service.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../theme/app_colors.dart';
import 'terms_of_service_screen.dart';
import 'privacy_policy_screen.dart';

/// A screen for app settings
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(isDarkModeProvider);

    return AppScaffold(
      title: 'Settings',
      body: ListView(
        children: [
          _buildSection(
            context,
            title: 'Appearance',
            children: [
              _buildSwitchTile(
                context,
                title: 'Dark Mode',
                subtitle: 'Enable dark mode for the app',
                value: isDarkMode,
                onChanged: (value) async {
                  ref.read(isDarkModeProvider.notifier).state = value;

                  // Save to shared preferences
                  final prefsService = await ref.read(
                    sharedPrefsServiceProvider.future,
                  );
                  await prefsService.saveBool(PreferenceKeys.isDarkMode, value);
                },
              ),
              //choose keyboard
              _buildSwitchTile(
                context,
                title: 'Keyboard',
                subtitle: "Enable built-in keyboard",
                value: ref.watch(useBuiltInKeyboardProvider),
                onChanged: (value) async {
                  ref.read(useBuiltInKeyboardProvider.notifier).state = value;

                  // Save to shared preferences
                  final prefsService = await ref.read(
                    sharedPrefsServiceProvider.future,
                  );
                  await prefsService.saveBool(
                    PreferenceKeys.useBuiltInKeyboard,
                    value,
                  );
                },
              ),
            ],
          ),
          _buildSection(
            context,
            title: 'Data',
            children: [
              _buildActionTile(
                context,
                title: 'Clear Cache',
                subtitle: 'Clear temporary data from the app',
                icon: Icons.cleaning_services,
                onTap: () {
                  _showClearCacheConfirmation(context, ref);
                },
              ),
              _buildActionTile(
                context,
                title: 'Export Data',
                subtitle: 'Export all data to a file',
                icon: Icons.upload_file,
                onTap: () async {
                  _showExportDialog(context, ref);
                },
              ),
              _buildActionTile(
                context,
                title: 'Import Data',
                subtitle: 'Import data from a file',
                icon: Icons.download_rounded,
                onTap: () async {
                  _showImportDialog(context, ref);
                },
              ),
            ],
          ),
          _buildSection(
            context,
            title: 'About',
            children: [
              _buildInfoTile(context, title: 'Version', value: '1.0.0'),
              _buildInfoTile(context, title: 'Build Number', value: '1'),
              _buildActionTile(
                context,
                title: 'Terms of Service',
                subtitle: 'View the terms of service',
                icon: Icons.description,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TermsOfServiceScreen(),
                    ),
                  );
                },
              ),
              _buildActionTile(
                context,
                title: 'Privacy Policy',
                subtitle: 'View the privacy policy',
                icon: Icons.privacy_tip,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PrivacyPolicyScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      leading: Icon(icon),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(
    BuildContext context, {
    required String title,
    required String value,
  }) {
    return ListTile(
      title: Text(title),
      trailing: Text(
        value,
        style: TextStyle(color: Colors.grey[600], fontWeight: FontWeight.bold),
      ),
    );
  }

  void _showClearCacheConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Cache'),
            content: const Text(
              'Are you sure you want to clear the cache? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Clear shared preferences
                  try {
                    final prefsService = await ref.read(
                      sharedPrefsServiceProvider.future,
                    );

                    // Save current values that we want to keep
                    final isDarkMode = ref.read(isDarkModeProvider);
                    final useBuiltInKeyboard = ref.read(
                      useBuiltInKeyboardProvider,
                    );

                    // Clear all preferences
                    await prefsService.clear();

                    // Restore values we want to keep
                    await prefsService.saveBool(
                      PreferenceKeys.isDarkMode,
                      isDarkMode,
                    );
                    await prefsService.saveBool(
                      PreferenceKeys.useBuiltInKeyboard,
                      useBuiltInKeyboard,
                    );

                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Cache cleared successfully'),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error clearing cache: $e')),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('Clear'),
              ),
            ],
          ),
    );
  }

  void _showExportDialog(BuildContext context, WidgetRef ref) async {
    // Check for storage permission
    if (!await Permission.storage.request().isGranted) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Storage permission required to export data'),
            action: SnackBarAction(
              label: 'Settings',
              onPressed: () async {
                await openAppSettings();
              },
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
      return;
    }

    // For Android 11+ (API 30+), request manage external storage permission
    try {
      // Check if the permission exists on this device
      final status = await Permission.manageExternalStorage.status;
      if (status != PermissionStatus.granted) {
        await Permission.manageExternalStorage.request();
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Storage permission required for full access',
              ),
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () async {
                  await openAppSettings();
                },
              ),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      // This permission might not be available on all devices
      debugPrint('Manage external storage permission not available: $e');
    }

    // Get matches for the match selection option
    final matchService = await ref.read(matchServiceProvider.future);
    final matches = await matchService.getAllMatches();

    // Show export options dialog
    if (context.mounted) {
      await showDialog(
        context: context,
        builder:
            (dialogContext) => AlertDialog(
              title: const Text('Export Data'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Choose export format:'),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          icon: const Icon(Icons.code),
                          label: const Text('JSON'),
                          onPressed: () {
                            Navigator.pop(dialogContext);
                            _performExport(context, ref, ExportFormat.json);
                          },
                        ),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.table_chart),
                          label: const Text('CSV'),
                          onPressed: () {
                            Navigator.pop(dialogContext);
                            _performExport(context, ref, ExportFormat.csv);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text('Choose data to export:'),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildExportOptionChip(
                          dialogContext,
                          'All Data',
                          ExportDataType.all,
                          ref,
                          ExportFormat.json,
                        ),
                        _buildExportOptionChip(
                          dialogContext,
                          'Matches',
                          ExportDataType.matches,
                          ref,
                          ExportFormat.json,
                        ),
                        _buildExportOptionChip(
                          dialogContext,
                          'Fighters',
                          ExportDataType.fighters,
                          ref,
                          ExportFormat.json,
                        ),
                        _buildExportOptionChip(
                          dialogContext,
                          'Bets',
                          ExportDataType.bets,
                          ref,
                          ExportFormat.json,
                        ),
                      ],
                    ),
                    if (matches.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text('Export specific match:'),
                      const SizedBox(height: 8),
                      Container(
                        height: 150,
                        width: double.maxFinite,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: matches.length,
                          itemBuilder: (context, index) {
                            final match = matches[index];
                            return ListTile(
                              title: Text('${match.date} (ID: ${match.id})'),
                              subtitle: Text('Brake: ${match.brake}'),
                              onTap: () {
                                Navigator.pop(dialogContext);
                                _showMatchExportOptions(context, ref, match);
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(dialogContext),
                  child: const Text('Cancel'),
                ),
              ],
            ),
      );
    }
  }

  void _showMatchExportOptions(
    BuildContext context,
    WidgetRef ref,
    Match match,
  ) {
    showDialog(
      context: context,
      builder:
          (dialogContext) => AlertDialog(
            title: Text('Export Match: ${match.date}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('ID: ${match.id}'),
                Text('Brake: ${match.brake}'),
                const SizedBox(height: 16),
                const Text('Choose export format:'),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      icon: const Icon(Icons.code),
                      label: const Text('JSON'),
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        _performExport(
                          context,
                          ref,
                          ExportFormat.json,
                          dataType: ExportDataType.all,
                          matchId: match.id,
                        );
                      },
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.table_chart),
                      label: const Text('CSV'),
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        _performExport(
                          context,
                          ref,
                          ExportFormat.csv,
                          dataType: ExportDataType.all,
                          matchId: match.id,
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text('Include fighter data?'),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        _performMatchExport(
                          context,
                          ref,
                          match.id,
                          includeFighters: true,
                        );
                      },
                      child: const Text('Yes'),
                    ),
                    OutlinedButton(
                      onPressed: () {
                        Navigator.pop(dialogContext);
                        _performMatchExport(
                          context,
                          ref,
                          match.id,
                          includeFighters: false,
                        );
                      },
                      child: const Text('No'),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  Future<void> _performMatchExport(
    BuildContext context,
    WidgetRef ref,
    int matchId, {
    bool includeFighters = true,
    ExportFormat format = ExportFormat.json,
  }) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Exporting Match Data'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text('Exporting match data as ${format.name.toUpperCase()}...'),
              ],
            ),
          ),
    );

    try {
      // Get the export service
      final exportService = await ref.read(dataExportServiceProvider.future);

      // Export match data
      final result = await exportService.exportMatchData(
        matchId,
        format: format,
        includeFighters: includeFighters,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Show result
      if (context.mounted) {
        if (result.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result.filePath != null
                    ? 'Match data exported successfully to ${result.filePath}'
                    : 'Match data exported successfully',
              ),
              duration: const Duration(seconds: 5),
              action: SnackBarAction(label: 'OK', onPressed: () {}),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to export match data: ${result.errorMessage ?? "Unknown error"}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog on error
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting match data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildExportOptionChip(
    BuildContext context,
    String label,
    ExportDataType dataType,
    WidgetRef ref,
    ExportFormat format,
  ) {
    return ActionChip(
      label: Text(label),
      onPressed: () {
        Navigator.pop(context);
        _performExport(context, ref, format, dataType: dataType);
      },
    );
  }

  Future<void> _performExport(
    BuildContext context,
    WidgetRef ref,
    ExportFormat format, {
    ExportDataType dataType = ExportDataType.all,
    int? matchId,
  }) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Exporting Data'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Exporting ${dataType.name} as ${format.name.toUpperCase()}...',
                ),
              ],
            ),
          ),
    );

    try {
      // Get the export service
      final exportService = await ref.read(dataExportServiceProvider.future);

      // Export data
      final result = await exportService.exportData(
        format: format,
        dataType: dataType,
        matchId: matchId,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Show result
      if (context.mounted) {
        if (result.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result.filePath != null
                    ? 'Data exported successfully to ${result.filePath}'
                    : 'Data exported successfully',
              ),
              duration: const Duration(seconds: 5),
              action: SnackBarAction(label: 'OK', onPressed: () {}),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to export data: ${result.errorMessage ?? "Unknown error"}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog on error
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showImportDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Import Data'),
            content: const Text(
              'This will import data from a file. Existing data will be preserved, but may be overwritten if there are conflicts. Do you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performImport(context, ref);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                ),
                child: const Text('Import'),
              ),
            ],
          ),
    );
  }

  Future<void> _performImport(BuildContext context, WidgetRef ref) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => const AlertDialog(
            title: Text('Importing Data'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Please wait while your data is being imported...'),
              ],
            ),
          ),
    );

    try {
      // Get the export service
      final exportService = await ref.read(dataExportServiceProvider.future);

      // Import data
      final success = await exportService.importData();

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Show result
      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Data imported successfully')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to import data or operation cancelled'),
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog on error
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error importing data: $e')));
      }
    }
  }
}
