import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/fighter.dart';
import '../data/providers/simple_providers.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../widgets/dialogs/app_dialogs.dart';
import '../widgets/drawer/app_drawer.dart';
import 'all_sale_screen.dart';
import 'fighter_form_screen.dart';

/// A screen that displays a list of fighters with CRUD functionality
class FighterListScreen extends ConsumerStatefulWidget {
  const FighterListScreen({super.key});

  @override
  ConsumerState<FighterListScreen> createState() => _FighterListScreenState();
}

class _FighterListScreenState extends ConsumerState<FighterListScreen> {
  List<Fighter> _fighters = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFighters();
  }

  Future<void> _loadFighters() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize database first
      final fighterService = await ref.read(fighterServiceProvider.future);
      final fighters = await fighterService.getAllFighters();

      setState(() {
        _fighters = fighters;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading fighters: $e');

      setState(() {
        _fighters = [];
        _isLoading = false;
      });

      if (mounted) {
        AppDialogs.showErrorDialog(
          context: context,
          title: 'Error',
          message: 'Failed to load fighters: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _deleteFighter(Fighter fighter) async {
    try {
      final fighterService = await ref.read(fighterServiceProvider.future);
      await fighterService.deleteFighter(fighter.id);
      await _loadFighters();

      if (mounted) {
        final theme = Theme.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle_outline, color: Colors.white),
                SizedBox(width: 12),
                Text(
                  'Fighter deleted successfully',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            backgroundColor: theme.colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: EdgeInsets.all(12),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        AppDialogs.showErrorDialog(
          context: context,
          title: 'Error',
          message: 'Failed to delete fighter: ${e.toString()}',
        );
      }
    }
  }

  void _confirmDeleteFighter(Fighter fighter) {
    AppDialogs.showConfirmDialog(
      context: context,
      title: 'Delete Fighter',
      message: 'Are you sure you want to delete ${fighter.name}?',
      confirmText: 'Delete',
      onConfirm: () => _deleteFighter(fighter),
    );
  }

  Future<void> _navigateToFormScreen({Fighter? fighter}) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FighterFormScreen(fighter: fighter),
      ),
    );

    // If result is true, reload fighters
    if (result == true) {
      await _loadFighters();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppScaffold(
      title: 'Fighters',
      drawer: const AppDrawer(),
      actions: [
        IconButton(
          icon: Icon(Icons.search),
          onPressed: () {
            // TODO: Implement search functionality
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Search functionality coming soon'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
        ),
        IconButton(
          icon: Icon(Icons.filter_list),
          onPressed: () {
            // TODO: Implement filter functionality
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Filter functionality coming soon'),
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
        ),
      ],
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: theme.colorScheme.primary),
                    SizedBox(height: 16),
                    Text(
                      'Loading fighters...',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              )
              : _buildFighterList(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToFormScreen(),
        tooltip: 'Add Fighter',
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        icon: const Icon(Icons.add),
        label: const Text('Add Fighter'),
        elevation: 4,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildFighterList() {
    final theme = Theme.of(context);

    if (_fighters.isEmpty) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(24.0),
          padding: const EdgeInsets.all(24.0),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(24.0),
            boxShadow: [
              BoxShadow(
                color: theme.shadowColor.withAlpha(20),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(20),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Container(
                    width: 90,
                    height: 90,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(40),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(20.0),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withAlpha(60),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.people_alt_outlined,
                      size: 40,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              Text(
                'No Fighters Found',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Text(
                  'You haven\'t added any fighters yet. Add your first fighter to get started.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(180),
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => _navigateToFormScreen(),
                icon: const Icon(Icons.add),
                label: const Text('Add Your First Fighter'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24.0,
                    vertical: 16.0,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  elevation: 2,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFighters,
      color: theme.colorScheme.primary,
      backgroundColor: theme.colorScheme.surface,
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(
          16.0,
          8.0,
          16.0,
          80.0,
        ), // Bottom padding for FAB
        physics:
            const AlwaysScrollableScrollPhysics(), // Allows pull-to-refresh even when content doesn't fill screen
        itemCount: _fighters.length,
        itemBuilder: (context, index) {
          final fighter = _fighters[index];
          return _buildFighterCard(fighter);
        },
      ),
    );
  }

  Widget _buildFighterCard(Fighter fighter) {
    final theme = Theme.of(context);

    // Determine colors based on dealer status
    final Color primaryColor =
        fighter.isDealer ? theme.colorScheme.error : theme.colorScheme.primary;

    final Color surfaceColor =
        fighter.isDealer
            ? theme.colorScheme.errorContainer.withAlpha(30)
            : theme.colorScheme.primaryContainer.withAlpha(30);

    // Determine badge text based on dealer status
    final String roleText = fighter.isDealer ? 'Dealer' : 'Fighter';

    // Format commission and odd for display
    final commissionPercent = fighter.commission;
    final oddFormatted = fighter.odd.toStringAsFixed(2);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      elevation: 3.0,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
      child: InkWell(
        onTap:
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AllSaleScreen(selectedFighter: fighter),
              ),
            ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with colored background
            Container(
              color: surfaceColor,
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 12.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: primaryColor.withAlpha(40),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          fighter.isDealer ? Icons.store : Icons.person,
                          color: primaryColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            fighter.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8.0,
                              vertical: 2.0,
                            ),
                            decoration: BoxDecoration(
                              color: primaryColor.withAlpha(30),
                              borderRadius: BorderRadius.circular(12.0),
                              border: Border.all(
                                color: primaryColor.withAlpha(50),
                                width: 1.0,
                              ),
                            ),
                            child: Text(
                              roleText,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  // Action buttons
                  Row(
                    children: [
                      Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () => _navigateToFormScreen(fighter: fighter),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.edit_outlined,
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(20),
                          onTap: () => _confirmDeleteFighter(fighter),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.delete_outline,
                              color: theme.colorScheme.error,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Stats row
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          Icons.percent,
                          'Commission',
                          "$commissionPercent%",
                          theme.colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 12.0),
                      Expanded(
                        child: _buildInfoItem(
                          Icons.trending_up,
                          'Odd',
                          oddFormatted,
                          theme.colorScheme.secondary,
                        ),
                      ),
                    ],
                  ),
                  // View details button
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Center(
                      child: TextButton.icon(
                        onPressed:
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        AllSaleScreen(selectedFighter: fighter),
                              ),
                            ),
                        icon: Icon(Icons.visibility_outlined, size: 18),
                        label: Text('View Details'),
                        style: TextButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 8.0,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    IconData icon,
    String label,
    String value,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 12.0),
      decoration: BoxDecoration(
        color: color.withAlpha(15),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: color.withAlpha(30), width: 1.0),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withAlpha(10),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: color.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface.withAlpha(180),
                  ),
                ),
                const SizedBox(height: 2.0),
                Text(
                  value,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
