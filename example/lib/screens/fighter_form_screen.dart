import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/fighter.dart';
import '../data/providers/simple_providers.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../theme/app_colors.dart';
import '../widgets/dialogs/app_dialogs.dart';

/// A screen for adding or editing a fighter
class FighterFormScreen extends ConsumerStatefulWidget {
  final Fighter? fighter;

  const FighterFormScreen({super.key, this.fighter});

  @override
  ConsumerState<FighterFormScreen> createState() => _FighterFormScreenState();
}

class _FighterFormScreenState extends ConsumerState<FighterFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _commissionController = TextEditingController();
  final _oddController = TextEditingController();
  bool _isDealerValue = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    if (widget.fighter != null) {
      _nameController.text = widget.fighter!.name;

      // Set default commission to 10% if below the minimum
      final commission = widget.fighter!.commission;
      _commissionController.text =
          commission < 10 ? '10' : commission.toString();

      // Set default odd to 80 if below minimum or 85 if above maximum
      final odd = widget.fighter!.odd;
      if (odd < 80) {
        _oddController.text = '80';
      } else if (odd > 85) {
        _oddController.text = '85';
      } else {
        _oddController.text = odd.toString();
      }

      _isDealerValue = widget.fighter!.isDealer;
    } else {
      // Set default values for new fighters
      _commissionController.text = '10';
      _oddController.text = '80';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _commissionController.dispose();
    _oddController.dispose();
    super.dispose();
  }

  Future<void> _saveFighter() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final fighterService = await ref.read(fighterServiceProvider.future);
      final name = _nameController.text;
      final commission = double.parse(_commissionController.text);
      final odd = double.parse(_oddController.text);

      // Double-check constraints before saving
      if (commission < 10 || commission > 100) {
        throw Exception('Commission must be between 10% and 100%');
      }

      if (odd < 80 || odd > 85) {
        throw Exception('Odd must be between 80 and 85');
      }

      if (widget.fighter == null) {
        // Create new fighter
        final id = DateTime.now().millisecondsSinceEpoch % 10000;
        await fighterService.createFighter(
          id: id,
          name: name,
          commission: commission,
          odd: odd,
          isDealer: _isDealerValue,
        );
      } else {
        // Update existing fighter
        await fighterService.updateFighter(
          id: widget.fighter!.id,
          name: name,
          commission: commission,
          odd: odd,
          isDealer: _isDealerValue,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.fighter == null
                  ? 'Fighter created successfully'
                  : 'Fighter updated successfully',
            ),
          ),
        );
        Navigator.pop(context, true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        AppDialogs.showErrorDialog(
          context: context,
          title: 'Error',
          message: 'Failed to save fighter: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: widget.fighter == null ? 'Add Fighter' : 'Edit Fighter',
      showBackButton: true,
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildForm(),
    );
  }

  Widget _buildForm() {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              // Name field
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                  filled: true,
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Commission field
              TextFormField(
                controller: _commissionController,
                decoration: const InputDecoration(
                  labelText: 'Commission (%)',
                  border: OutlineInputBorder(),
                  filled: true,
                  prefixIcon: Icon(Icons.percent),
                  hintText: 'Enter value above 10%',
                  helperText: 'Commission must be 10% or higher',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a commission rate';
                  }
                  final commission = double.tryParse(value);
                  if (commission == null) {
                    return 'Please enter a valid number';
                  }
                  if (commission < 10) {
                    return 'Commission must be 10% or higher';
                  }
                  if (commission > 100) {
                    return 'Commission cannot exceed 100%';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Odd field
              TextFormField(
                controller: _oddController,
                decoration: const InputDecoration(
                  labelText: 'Odd',
                  border: OutlineInputBorder(),
                  filled: true,
                  prefixIcon: Icon(Icons.trending_up),
                  hintText: 'Enter value between 80-85',
                  helperText: 'Odd must be between 80 and 85',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an odd';
                  }
                  final odd = double.tryParse(value);
                  if (odd == null) {
                    return 'Please enter a valid number';
                  }
                  if (odd < 80) {
                    return 'Odd must be at least 80';
                  }
                  if (odd > 85) {
                    return 'Odd cannot exceed 85';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Is Dealer checkbox
              Card(
                color:
                    _isDealerValue
                        ? AppColors.error.withAlpha(30)
                        : AppColors.success.withAlpha(30),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Checkbox(
                        value: _isDealerValue,
                        activeColor: AppColors.error,
                        onChanged: (value) {
                          setState(() {
                            _isDealerValue = value ?? false;
                          });
                        },
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Is Dealer',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    _isDealerValue
                                        ? AppColors.error
                                        : AppColors.success,
                              ),
                            ),
                            Text(
                              _isDealerValue
                                  ? 'This fighter is a dealer and will be highlighted in the list'
                                  : 'This fighter is not a dealer',
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    _isDealerValue
                                        ? AppColors.error
                                        : AppColors.success,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        _isDealerValue ? Icons.store : Icons.person,
                        color:
                            _isDealerValue
                                ? AppColors.error
                                : AppColors.success,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Save button
              ElevatedButton(
                onPressed: _saveFighter,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                ),
                child: Text(
                  widget.fighter == null ? 'Create Fighter' : 'Update Fighter',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(height: 8),

              // Cancel button
              OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                ),
                child: const Text('Cancel', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
