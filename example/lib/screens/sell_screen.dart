import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_grid_list/responsive_grid_list.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

import '../data/models/fighter.dart';
import '../data/providers/simple_providers.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import '../util/format_utils.dart';
import '../widgets/dialogs/app_dialogs.dart';

class SellScreen extends ConsumerWidget {
  const SellScreen(this.bets, this.fighters, {super.key});
  final List<IBetModel> bets;
  final List<Fighter> fighters;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      title: 'Over',
      actions: [
        TextButton.icon(
          icon: const Icon(Icons.person),
          label: const Text('Sell'),
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all(Colors.white),
            backgroundColor: WidgetStateProperty.all(AppColors.error),
          ),
          onPressed: () async {
            //Select Fighter

            await AppDialogs.showFighterSelectionBottomSheet(
              context: context,
              fighters: fighters.where((element) => element.isDealer).toList(),
              selectedFighter: null,
              onFighterSelected: (fighter) async {
                List<RBetModel> betList = [];
                final betService = await ref.read(betServiceProvider.future);
                for (IBetModel b in bets) {
                  betList.add(
                    RBetModel(
                      number: b.number,
                      amount: b.amount.toString(),
                      bets: [b],
                      total: b.amount,
                    ),
                  );
                }
                await betService.createBatchBets(
                  numbers: betList,
                  fighterId: fighter.id,
                  matchId: ref.read(selectedMatchProvider)?.id ?? 0,
                );
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Over selled successfully')),
                  );
                  Navigator.pop(context);
                }
              },
            );
          },
        ),
        SizedBox(width: 8),
      ],
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Expanded(
              child: ResponsiveGridList(
                horizontalGridSpacing: 2,
                verticalGridSpacing: 2,
                minItemWidth: 100,
                minItemsPerRow: 3,
                maxItemsPerRow: 10,
                listViewBuilderOptions: ListViewBuilderOptions(),
                children:
                    bets
                        .map(
                          (e) => Card.filled(
                            clipBehavior: Clip.antiAlias,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(5),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 5,
                                horizontal: 5,
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    e.number,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    e.amount.toString(),
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ),
            Divider(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Total",
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    FormatUtils.formatNumber(
                      bets.fold(0, (p, c) => p + c.amount),
                    ),
                    style: AppTypography.amountMedium,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
