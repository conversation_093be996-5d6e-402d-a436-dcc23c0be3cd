import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_grid_list/responsive_grid_list.dart';
import 'package:example/data/models/bet.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';
import 'package:example/services/pdf_service.dart';
import 'package:example/services/image_export_service.dart';
import 'package:file_saver/file_saver.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import 'sell_screen.dart';

class LedgerScreen extends ConsumerWidget {
  const LedgerScreen({super.key});

  List<IBetModel> _calculateOverBrakeList(
    Map<String, int> ledger,
    int brakeAmount,
  ) {
    return ledger.entries
        .map((e) => IBetModel(number: e.key, amount: e.value))
        .where((e) => e.amount > brakeAmount)
        .map((e) => IBetModel(number: e.number, amount: e.amount - brakeAmount))
        .toList();
  }

  Future<void> _exportToPdf(
    Map<String, int> ledger,
    String matchTitle,
    int brakeAmount,
  ) async {
    try {
      final total = ledger.values.fold(0, (p, c) => p + c);
      final overBrakeList = _calculateOverBrakeList(ledger, brakeAmount);

      final pdfBytes = await PdfService.generateLedgerPdf(
        ledger: ledger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
        overBrakeList: overBrakeList,
      );

      final fileName =
          'ledger_${matchTitle.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: pdfBytes,
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );

      Fluttertoast.showToast(
        msg: 'PDF exported successfully',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'Failed to export PDF: $e',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  Future<void> _exportToImage(
    Map<String, int> ledger,
    String matchTitle,
    int brakeAmount,
    BuildContext context,
  ) async {
    try {
      final total = ledger.values.fold(0, (p, c) => p + c);
      final overBrakeList = _calculateOverBrakeList(ledger, brakeAmount);

      // Generate complete image with all data
      final imageBytes = await ImageExportService.captureFromWidgetTree(
        ledger: ledger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
        overBrakeList: overBrakeList,
        context: context,
      );

      if (imageBytes == null) {
        throw Exception('Failed to generate ledger image');
      }

      final fileName =
          'ledger_${matchTitle.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}';

      // Save to gallery using ImageGallerySaverPlus
      final success = await ImageExportService.saveImageToGallery(
        imageBytes,
        fileName,
      );

      if (success) {
        Fluttertoast.showToast(
          msg: 'Image saved to gallery successfully',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      } else {
        // Fallback to file saver if gallery save fails
        await FileSaver.instance.saveFile(
          name: '$fileName.png',
          bytes: imageBytes,
          ext: 'png',
          mimeType: MimeType.png,
        );

        Fluttertoast.showToast(
          msg: 'Image exported successfully',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
        );
      }
    } catch (e) {
      Fluttertoast.showToast(
        msg: 'Failed to export image: $e',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
      );
    }
  }

  void _showExportOptions(
    BuildContext context,
    Map<String, int> ledger,
    String matchTitle,
    int brakeAmount,
  ) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'Export Ledger',
                  style: AppTypography.headline4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
                  title: const Text('Export as PDF'),
                  subtitle: const Text('Portable document format'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _exportToPdf(ledger, matchTitle, brakeAmount);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.image, color: Colors.blue),
                  title: const Text('Export as Image'),
                  subtitle: const Text('PNG image format'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _exportToImage(
                      ledger,
                      matchTitle,
                      brakeAmount,
                      context,
                    );
                  },
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final betsStream = ref.watch(currentBetsProvider);
    final fighersStream = ref.watch(allFightersProvider);
    final selectedMatch = ref.watch(selectedMatchProvider);

    return selectedMatch == null
        ? _buildNoMatchSelectedState()
        : fighersStream.when(
          data:
              (fighters) => betsStream.when(
                data: (bets) {
                  Map<String, int> ledger = {};
                  for (Bet b in bets) {
                    RBetModel betRaw = generateTwo("${b.number}=${b.amount}");
                    bool isDealer =
                        fighters.firstWhere((f) => f.id == b.fighter).isDealer;
                    for (IBetModel bet in betRaw.bets) {
                      ledger.containsKey(bet.number)
                          ? ledger[bet.number] =
                              ledger[bet.number]! +
                              (isDealer ? -bet.amount : bet.amount)
                          : ledger[bet.number] =
                              isDealer ? -bet.amount : bet.amount;
                    }
                  }
                  //sort
                  ledger = Map.fromEntries(
                    ledger.entries.toList()
                      ..sort((a, b) => a.key.compareTo(b.key)),
                  );

                  return AppScaffold(
                    title: 'Ledger',
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.download),
                        tooltip: 'Export Ledger',
                        onPressed: () {
                          _showExportOptions(
                            context,
                            ledger,
                            selectedMatch.date,
                            int.parse(selectedMatch.brake),
                          );
                        },
                      ),
                      TextButton.icon(
                        icon: const Icon(Icons.arrow_forward),
                        label: const Text('Over'),
                        style: ButtonStyle(
                          foregroundColor: WidgetStateProperty.all(
                            Colors.white,
                          ),
                          backgroundColor: WidgetStateProperty.all(
                            AppColors.error,
                          ),
                        ),
                        onPressed: () async {
                          await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => SellScreen(
                                    ledger.entries
                                        .map(
                                          (e) => IBetModel(
                                            number: e.key,
                                            amount: e.value,
                                          ),
                                        )
                                        .where(
                                          (e) =>
                                              e.amount >
                                              int.parse(selectedMatch.brake),
                                        )
                                        .map(
                                          (e) => IBetModel(
                                            number: e.number,
                                            amount:
                                                e.amount -
                                                int.parse(selectedMatch.brake),
                                          ),
                                        )
                                        .toList(),
                                    fighters,
                                  ),
                            ),
                          );
                          await Future.delayed(
                            const Duration(milliseconds: 500),
                          );
                          ref.invalidate(currentBetsProvider);
                        },
                      ),
                      SizedBox(width: 8),
                    ],
                    body: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Expanded(
                            child: ResponsiveGridList(
                              horizontalGridSpacing: 2,
                              verticalGridSpacing: 2,
                              minItemWidth: 100,
                              minItemsPerRow: 3,
                              maxItemsPerRow: 10,
                              listViewBuilderOptions: ListViewBuilderOptions(),
                              children:
                                  ledger.entries
                                      .map(
                                        (e) => IBetModel(
                                          number: e.key,
                                          amount: e.value,
                                        ),
                                      )
                                      .map(
                                        (e) => Card.filled(
                                          clipBehavior: Clip.antiAlias,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              5,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 5,
                                              horizontal: 5,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  e.number,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                                Text(
                                                  e.amount.toString(),
                                                  style: TextStyle(
                                                    color:
                                                        (e.amount >
                                                                int.parse(
                                                                  selectedMatch
                                                                      .brake,
                                                                ))
                                                            ? Colors.red
                                                            : Colors.green,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      )
                                      .toList(),
                            ),
                          ),
                          Divider(),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Total",
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                Text(
                                  FormatUtils.formatNumber(
                                    ledger.values.fold(0, (p, c) => p + c),
                                  ),
                                  style: AppTypography.amountMedium,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                error:
                    (error, stackTrace) => Text('Error: ${error.toString()}'),
                loading: () => const Center(child: CircularProgressIndicator()),
              ),
          error: (error, stackTrace) => Text('Error: ${error.toString()}'),
          loading: () => const Center(child: CircularProgressIndicator()),
        );
  }

  Widget _buildNoMatchSelectedState() {
    return AppScaffold(
      title: 'Ledger',
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_month_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Please select a match',
              style: AppTypography.headline4.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Go to Matches screen to select a match',
              style: AppTypography.bodyMedium.copyWith(color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }
}
