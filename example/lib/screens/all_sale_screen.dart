import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/theme/app_typography.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

class AllSaleScreen extends ConsumerWidget {
  const AllSaleScreen({super.key, this.selectedFighter});
  final Fighter? selectedFighter;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allSale = ref.watch(currentBetsProvider);
    final selectedMatch = ref.watch(selectedMatchProvider);
    final fighetersStream = ref.watch(allFightersProvider);

    return AppScaffold(
      title: selectedFighter == null ? 'All Sale' : selectedFighter?.name,
      body: fighetersStream.when(
        data:
            (fighters) => allSale.when(
              data: (data) {
                if (selectedMatch == null) {
                  return _buildNoMatchSelectedState();
                }

                final BetRaw betRaw = multiGenerateTwo(
                  data
                      .where(
                        (e) =>
                            selectedFighter == null
                                ? (!fighters
                                    .firstWhere((f) => f.id == e.fighter)
                                    .isDealer)
                                : e.fighter == selectedFighter?.id,
                      )
                      .map((e) => "${e.number}=${e.amount}")
                      .join('\n')
                      .trimRight(),
                );

                Map<String, int> allMap = {};
                for (IBetModel b in betRaw.bets
                    .map((e) => e.bets)
                    .expand((element) => element)) {
                  allMap.containsKey(b.number)
                      ? allMap[b.number] = allMap[b.number]! + b.amount
                      : allMap[b.number] = b.amount;
                }

                List<IBetModel> allBets =
                    allMap.entries
                        .map((e) => IBetModel(number: e.key, amount: e.value))
                        .toList();

                allBets.sort((a, b) => a.number.compareTo(b.number));

                // Calculate total amount
                int totalAmount = allBets.fold(
                  0,
                  (sum, bet) => sum + bet.amount,
                );

                return _buildBetsGridView(context, allBets, totalAmount);
              },
              loading: () => _buildLoadingState(),
              error: (error, stackTrace) => _buildErrorState(error),
            ),
        loading: () => _buildLoadingState(),
        error: (error, stackTrace) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildNoMatchSelectedState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calendar_month_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Please select a match',
            style: AppTypography.headline4.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Go to Matches screen to select a match',
            style: AppTypography.bodyMedium.copyWith(color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text('Loading data...', style: AppTypography.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: AppTypography.headline4.copyWith(color: AppColors.error),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(
              error.toString(),
              style: AppTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBetsGridView(
    BuildContext context,
    List<IBetModel> allBets,
    int totalAmount,
  ) {
    return Column(
      children: [
        // Summary card at the top
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Total Bets', style: AppTypography.subtitle),
                      Text(
                        allBets.length.toString(),
                        style: AppTypography.subtitle.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Total Amount', style: AppTypography.title),
                      Text(
                        FormatUtils.formatNumber(totalAmount),
                        style: AppTypography.amountMedium.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // Grid of bet cards
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(11.0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 3.5,
              // crossAxisSpacing: 6,
              // mainAxisSpacing: 6,
            ),
            itemCount: allBets.length,
            itemBuilder: (context, index) {
              final bet = allBets[index];
              return _buildBetCard(bet);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBetCard(IBetModel bet) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Number
            Expanded(
              child: Text(
                bet.number,
                style: AppTypography.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Amount
            Text(
              FormatUtils.formatNumber(bet.amount),
              style: AppTypography.amountSmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
