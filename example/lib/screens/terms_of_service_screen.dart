import 'package:flutter/material.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../theme/app_colors.dart';

/// Screen displaying the Terms of Service
class TermsOfServiceScreen extends StatelessWidget {
  const TermsOfServiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'Terms of Service',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Acceptance of Terms',
              'By downloading, installing, or using this 2D lottery data management application ("the App"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, do not use the App.',
            ),
            _buildSection(
              'Description of Service',
              'This App provides a data management platform for organizing Thai 2D lottery information, including but not limited to:\n\n• Recording and managing lottery match data\n• Organizing participant (fighter) information and odds\n• Tracking betting records and statistics\n• Managing user accounts and profiles\n• Processing payment verification documents\n• Exporting and importing lottery data\n• Generating reports and analytics',
            ),
            _buildSection(
              'User Eligibility and Legal Compliance',
              'You must be at least 18 years old to use this App. This App is intended for data management purposes only and does not facilitate actual betting or gambling activities.\n\nBy using this App, you confirm that:\n• You comply with all applicable laws in Myanmar regarding data management\n• You understand and comply with Thai laws regarding 2D lottery data\n• You are legally permitted to access and manage such data in your jurisdiction\n• You will not use this App for any illegal gambling activities',
            ),
            _buildSection(
              'Account Registration',
              'To use certain features of the App, you must register for an account. You agree to:\n\n• Provide accurate and complete information\n• Maintain the security of your account credentials\n• Notify us immediately of any unauthorized use\n• Accept responsibility for all activities under your account',
            ),
            _buildSection(
              'Data Management Responsibilities',
              'When using the data management features:\n\n• You are responsible for the accuracy of data entered\n• Data records should be verified before saving\n• You must comply with local laws regarding data collection\n• The App is for record-keeping and analysis purposes only\n• We are not responsible for any betting decisions made based on this data',
            ),
            _buildSection(
              'Prohibited Activities',
              'You agree not to:\n\n• Use the App for any illegal activities under Myanmar or Thai law\n• Attempt to manipulate or interfere with the App\'s operation\n• Share your account credentials with others\n• Enter false or misleading data\n• Use automated systems or bots\n• Violate any applicable laws or regulations in Myanmar, Thailand, or your jurisdiction\n• Use the App to facilitate illegal gambling or betting activities\n• Transfer data in violation of cross-border data protection laws',
            ),
            _buildSection(
              'Payment Verification',
              'The payment verification feature is for record-keeping purposes only. We reserve the right to:\n\n• Request additional documentation for verification\n• Suspend accounts pending verification\n• Refuse verification requests that appear fraudulent\n• This feature does not process actual payments or transactions',
            ),
            _buildSection(
              'Regulatory Compliance',
              'This App complies with applicable data protection and technology laws, including:\n\n• Myanmar Computer Science Development Law (2013)\n• Myanmar Electronic Transactions Law (2004)\n• Thai Personal Data Protection Act (PDPA) 2019\n• Cross-border data transfer regulations\n\nUsers are responsible for ensuring their use of the App complies with all applicable local laws and regulations regarding data management and gambling activities.',
            ),
            _buildSection(
              'Data and Privacy',
              'Your use of the App is also governed by our Privacy Policy, which is incorporated into these Terms by reference. We collect and use your information as described in the Privacy Policy.',
            ),
            _buildSection(
              'Intellectual Property',
              'The App and its content are protected by intellectual property laws. You may not copy, modify, distribute, or create derivative works without our express written permission.',
            ),
            _buildSection(
              'Disclaimers',
              'THE APP IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND. WE DISCLAIM ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT. THIS APP IS FOR DATA MANAGEMENT ONLY AND DOES NOT PROVIDE BETTING SERVICES OR GUARANTEE ANY OUTCOMES.',
            ),
            _buildSection(
              'Limitation of Liability',
              'TO THE MAXIMUM EXTENT PERMITTED BY LAW, WE SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES ARISING FROM YOUR USE OF THE APP. WE ARE NOT RESPONSIBLE FOR ANY BETTING LOSSES OR DECISIONS MADE BASED ON DATA IN THIS APP.',
            ),
            _buildSection(
              'Termination',
              'We may terminate or suspend your account at any time for violation of these Terms. Upon termination, your right to use the App ceases immediately.',
            ),
            _buildSection(
              'Changes to Terms',
              'We reserve the right to modify these Terms at any time. Changes will be effective upon posting in the App. Your continued use constitutes acceptance of the modified Terms.',
            ),
            _buildSection(
              'Governing Law and Jurisdiction',
              'These Terms are governed by the laws of the Republic of the Union of Myanmar. Any disputes arising from these Terms or your use of the App shall be resolved in the competent courts of Myanmar.\n\nAs this App manages Thai 2D lottery data, users must also comply with applicable Thai laws regarding data handling and cross-border data transfer.',
            ),
            _buildSection(
              'Contact Information',
              'For questions about these Terms, please contact us at:\n\nEmail: [Your Email]\nAddress: [Your Address]',
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(content, style: const TextStyle(fontSize: 14, height: 1.5)),
        ],
      ),
    );
  }
}
