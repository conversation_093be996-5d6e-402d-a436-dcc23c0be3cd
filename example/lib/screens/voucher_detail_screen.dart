import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:example/data/models/bet.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/util/format_utils.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/scaffold/app_scaffold.dart';

/// Screen that displays detailed information about a voucher with edit functionality
class VoucherDetailScreen extends ConsumerStatefulWidget {
  final String timestamp;
  final List<Bet> vouchers;

  const VoucherDetailScreen({
    super.key,
    required this.timestamp,
    required this.vouchers,
  });

  @override
  ConsumerState<VoucherDetailScreen> createState() =>
      _VoucherDetailScreenState();
}

class _VoucherDetailScreenState extends ConsumerState<VoucherDetailScreen> {
  bool _isEditMode = false;
  bool _isLoading = false;
  late List<Bet> _editableVouchers;
  final Map<int, TextEditingController> _numberControllers = {};
  final Map<int, TextEditingController> _amountControllers = {};
  final Map<int, bool> _hasChanges = {};

  @override
  void initState() {
    super.initState();
    _editableVouchers = List.from(widget.vouchers);
    _initializeControllers();
  }

  void _initializeControllers() {
    for (final voucher in _editableVouchers) {
      _numberControllers[voucher.id] = TextEditingController(
        text: voucher.number,
      );
      _amountControllers[voucher.id] = TextEditingController(
        text: voucher.amount,
      );
      _hasChanges[voucher.id] = false;

      // Add listeners to track changes
      _numberControllers[voucher.id]!.addListener(
        () => _markAsChanged(voucher.id),
      );
      _amountControllers[voucher.id]!.addListener(
        () => _markAsChanged(voucher.id),
      );
    }
  }

  void _markAsChanged(int id) {
    final originalVoucher = widget.vouchers.firstWhere((v) => v.id == id);
    final hasNumberChanged =
        _numberControllers[id]!.text != originalVoucher.number;
    final hasAmountChanged =
        _amountControllers[id]!.text != originalVoucher.amount;

    setState(() {
      _hasChanges[id] = hasNumberChanged || hasAmountChanged;
    });
  }

  @override
  void dispose() {
    for (final controller in _numberControllers.values) {
      controller.dispose();
    }
    for (final controller in _amountControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Calculate total amount
    int totalAmount =
        multiGenerateTwo(
          _editableVouchers.map((e) => "${e.number}=${e.amount}").join('\n'),
        ).total;

    // Get fighter details
    final fighterId = widget.vouchers.first.fighter;
    final fighterDetails = ref.watch(fighterDetailsProvider(fighterId));

    // Format timestamp for display
    DateTime dateTime;
    try {
      dateTime = DateTime.parse(widget.timestamp);
    } catch (e) {
      // Handle invalid timestamp format
      dateTime = DateTime.now();
    }
    final String formattedTime = DateFormat('h:mm a').format(dateTime);
    final String shortDate = DateFormat('MMM dd').format(dateTime);

    return AppScaffold(
      title: _isEditMode ? 'Edit Voucher' : 'Voucher Details',
      actions: [
        if (_isEditMode) ...[
          // Cancel button
          IconButton(
            icon: const Icon(Icons.close_rounded),
            tooltip: 'Cancel',
            onPressed: _cancelEdit,
          ),
          // Save button
          IconButton(
            icon:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Icon(Icons.check_rounded),
            tooltip: 'Save Changes',
            onPressed: _isLoading ? null : _saveChanges,
          ),
        ] else ...[
          // Copy button
          IconButton(
            icon: const Icon(Icons.content_copy_rounded),
            tooltip: 'Copy Voucher',
            onPressed: () => _copyVoucher(context),
          ),
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit_rounded),
            tooltip: 'Edit Voucher',
            onPressed: _toggleEditMode,
          ),
        ],
      ],
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Header card with voucher info
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              colorScheme.primaryContainer.withValues(
                                alpha: 0.1,
                              ),
                              colorScheme.surface,
                            ],
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Header row with voucher ID and total amount
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Voucher #${widget.vouchers.first.id}',
                                        style: theme.textTheme.headlineSmall
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: colorScheme.onSurface,
                                            ),
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.access_time_rounded,
                                            size: 16,
                                            color: colorScheme.onSurface
                                                .withValues(alpha: 0.6),
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '$shortDate • $formattedTime',
                                            style: theme.textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: colorScheme.onSurface
                                                      .withValues(alpha: 0.7),
                                                ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          colorScheme.primary,
                                          colorScheme.primary.withValues(
                                            alpha: 0.8,
                                          ),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: colorScheme.primary.withValues(
                                            alpha: 0.3,
                                          ),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      FormatUtils.formatNumber(totalAmount),
                                      style: theme.textTheme.titleMedium
                                          ?.copyWith(
                                            color: colorScheme.onPrimary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              fighterDetails.when(
                                data:
                                    (fighter) =>
                                        fighter != null
                                            ? Row(
                                              children: [
                                                Icon(
                                                  fighter.isDealer
                                                      ? Icons.store_rounded
                                                      : Icons.person_rounded,
                                                  size: 20,
                                                  color:
                                                      fighter.isDealer
                                                          ? colorScheme.error
                                                          : colorScheme
                                                              .onSurface
                                                              .withValues(
                                                                alpha: 0.7,
                                                              ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  fighter.name,
                                                  style: theme
                                                      .textTheme
                                                      .bodyLarge
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color:
                                                            fighter.isDealer
                                                                ? colorScheme
                                                                    .error
                                                                : colorScheme
                                                                    .onSurface,
                                                      ),
                                                ),
                                                if (fighter.isDealer) ...[
                                                  const SizedBox(width: 8),
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 8,
                                                          vertical: 2,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          colorScheme
                                                              .errorContainer,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                      border: Border.all(
                                                        color: colorScheme.error
                                                            .withValues(
                                                              alpha: 0.5,
                                                            ),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: Text(
                                                      'DEALER',
                                                      style: theme
                                                          .textTheme
                                                          .bodySmall
                                                          ?.copyWith(
                                                            color:
                                                                colorScheme
                                                                    .onErrorContainer,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            )
                                            : Text(
                                              'Unknown Fighter',
                                              style: theme.textTheme.bodyMedium
                                                  ?.copyWith(
                                                    color: colorScheme.onSurface
                                                        .withValues(alpha: 0.5),
                                                  ),
                                            ),
                                loading:
                                    () => Row(
                                      children: [
                                        SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: colorScheme.onSurface
                                                .withValues(alpha: 0.5),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Loading fighter...',
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(
                                                color: colorScheme.onSurface
                                                    .withValues(alpha: 0.5),
                                              ),
                                        ),
                                      ],
                                    ),
                                error:
                                    (error, _) => Text(
                                      'Error loading fighter',
                                      style: theme.textTheme.bodyMedium
                                          ?.copyWith(color: colorScheme.error),
                                    ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Icon(
                                    Icons.receipt_long_rounded,
                                    size: 16,
                                    color: colorScheme.onSurface.withValues(
                                      alpha: 0.6,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'Total Bets: ${_editableVouchers.length}',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface.withValues(
                                        alpha: 0.7,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Section header
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.list_alt_rounded,
                            size: 20,
                            color: colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Bet Details',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const Spacer(),
                          if (_isEditMode)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'Edit Mode',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onPrimaryContainer,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    // Bet list
                    Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        itemCount: _editableVouchers.length,
                        separatorBuilder:
                            (context, index) => const SizedBox(height: 8),
                        itemBuilder: (context, index) {
                          final bet = _editableVouchers[index];
                          return _buildBetItem(bet, index);
                        },
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildBetItem(Bet bet, int index) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final hasChanges = _hasChanges[bet.id] ?? false;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: hasChanges ? 2 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            hasChanges
                ? BorderSide(
                  color: colorScheme.primary.withValues(alpha: 0.5),
                  width: 1,
                )
                : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient:
              hasChanges
                  ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      colorScheme.primaryContainer.withValues(alpha: 0.1),
                      colorScheme.surface,
                    ],
                  )
                  : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Number field
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Number',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    _isEditMode
                        ? TextFormField(
                          controller: _numberControllers[bet.id],
                          decoration: InputDecoration(
                            isDense: true,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: colorScheme.outline.withValues(
                                  alpha: 0.5,
                                ),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          keyboardType: TextInputType.text,
                        )
                        : Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.primaryContainer.withValues(
                              alpha: 0.3,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            bet.number,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: colorScheme.primary,
                            ),
                          ),
                        ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Amount field
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Amount',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    _isEditMode
                        ? TextFormField(
                          controller: _amountControllers[bet.id],
                          decoration: InputDecoration(
                            isDense: true,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: colorScheme.outline.withValues(
                                  alpha: 0.5,
                                ),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          keyboardType: TextInputType.number,
                        )
                        : Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.success.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            bet.amount,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.success,
                            ),
                          ),
                        ),
                  ],
                ),
              ),

              // Actions
              if (_isEditMode) ...[
                const SizedBox(width: 8),
                Column(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.delete_outline_rounded,
                        color: colorScheme.error,
                        size: 20,
                      ),
                      onPressed: () => _deleteBet(bet.id),
                      tooltip: 'Delete bet',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Copy voucher to clipboard
  void _copyVoucher(BuildContext context) {
    // Build a formatted string with all bet information
    final StringBuffer buffer = StringBuffer();

    // Add header
    buffer.writeln('📋 Voucher Details');
    buffer.writeln('═══════════════════');

    // Add timestamp
    try {
      final dateTime = DateTime.parse(widget.timestamp);
      final formattedDate = DateFormat('MMMM dd, yyyy').format(dateTime);
      final formattedTime = DateFormat('h:mm a').format(dateTime);
      buffer.writeln('📅 Date: $formattedDate');
      buffer.writeln('🕐 Time: $formattedTime');
    } catch (e) {
      // Handle invalid timestamp
    }

    buffer.writeln('🎯 Voucher ID: ${widget.vouchers.first.id}');
    buffer.writeln('');

    // Calculate total using the same method as UI
    final totalAmount =
        multiGenerateTwo(
          _editableVouchers.map((e) => "${e.number}=${e.amount}").join('\n'),
        ).total;

    // Add bets
    buffer.writeln('📊 Bet Details:');
    buffer.writeln('───────────────');
    for (final bet in _editableVouchers) {
      buffer.writeln('${bet.number} → ${bet.amount}');
    }

    buffer.writeln('');
    buffer.writeln('💰 Total Amount: ${FormatUtils.formatNumber(totalAmount)}');
    buffer.writeln('📈 Total Bets: ${_editableVouchers.length}');

    // Copy to clipboard
    Clipboard.setData(ClipboardData(text: buffer.toString()));

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Voucher details copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Toggle edit mode
  void _toggleEditMode() {
    setState(() {
      _isEditMode = !_isEditMode;
    });
  }

  // Cancel edit mode
  void _cancelEdit() {
    setState(() {
      _isEditMode = false;
      // Reset all controllers to original values
      for (final voucher in widget.vouchers) {
        _numberControllers[voucher.id]?.text = voucher.number;
        _amountControllers[voucher.id]?.text = voucher.amount;
        _hasChanges[voucher.id] = false;
      }
      _editableVouchers = List.from(widget.vouchers);
    });
  }

  // Save changes
  Future<void> _saveChanges() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final betService = await ref.read(betServiceProvider.future);
      final selectedMatch = ref.read(selectedMatchProvider);

      if (selectedMatch == null) {
        throw Exception('No match selected');
      }

      // Validate and save each changed bet
      for (final bet in _editableVouchers) {
        if (_hasChanges[bet.id] == true) {
          final newNumber = _numberControllers[bet.id]?.text ?? bet.number;
          final newAmount = _amountControllers[bet.id]?.text ?? bet.amount;

          // Validate number is not empty
          if (newNumber.trim().isEmpty) {
            throw Exception('Number cannot be empty');
          }

          // Validate amount is not empty and is a valid number
          if (newAmount.trim().isEmpty) {
            throw Exception('Amount cannot be empty');
          }

          try {
            int.parse(newAmount);
          } catch (e) {
            throw Exception('Amount must be a valid number');
          }

          // Update the bet
          await betService.updateBet(
            id: bet.id,
            number: newNumber != bet.number ? newNumber : null,
            amount: newAmount != bet.amount ? newAmount : null,
          );
        }
      }

      // Refresh the bet service to get updated data
      final _ = ref.refresh(betServiceProvider);

      setState(() {
        _isEditMode = false;
        _isLoading = false;
        // Clear all change flags
        for (final key in _hasChanges.keys) {
          _hasChanges[key] = false;
        }
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Changes saved successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving changes: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  // Delete a bet
  Future<void> _deleteBet(int betId) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Bet'),
            content: const Text('Are you sure you want to delete this bet?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      try {
        final betService = await ref.read(betServiceProvider.future);
        await betService.deleteBet(betId);

        // Remove from local list
        setState(() {
          _editableVouchers.removeWhere((bet) => bet.id == betId);
          _numberControllers[betId]?.dispose();
          _amountControllers[betId]?.dispose();
          _numberControllers.remove(betId);
          _amountControllers.remove(betId);
          _hasChanges.remove(betId);
        });

        // Refresh the bet service
        final _ = ref.refresh(betServiceProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Bet deleted successfully'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting bet: ${e.toString()}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }
}
