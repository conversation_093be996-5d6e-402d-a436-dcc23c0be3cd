import 'package:flutter/material.dart';
import '../widgets/scaffold/app_scaffold.dart';
import '../theme/app_colors.dart';

/// Screen displaying the Privacy Policy
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'Privacy Policy',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Introduction',
              'This Privacy Policy describes how we collect, use, and protect your personal information when you use our 2D lottery data management application ("the App"). We are committed to protecting your privacy and ensuring the security of your personal data in compliance with Myanmar and Thai data protection laws.',
            ),
            _buildSection(
              'Information We Collect',
              'We collect the following types of information:\n\n• Personal Information: Name, email address, phone number\n• Account Information: Username, password, profile picture\n• Device Information: Device ID, operating system, app version\n• Usage Data: App interactions, data entry patterns, feature usage\n• Data Records: Thai 2D lottery match data, participant information, statistics\n• Verification Documents: Payment verification images for record-keeping\n• Location Data: General location for compliance purposes',
            ),
            _buildSection(
              'How We Collect Information',
              'We collect information through:\n\n• Account registration and profile setup\n• Your use of the App\'s data management features\n• Payment verification document uploads\n• Device sensors and identifiers\n• Cookies and similar technologies\n• Communications with our support team',
            ),
            _buildSection(
              'How We Use Your Information',
              'We use your information to:\n\n• Provide and maintain the App\'s data management functionality\n• Store and organize your 2D lottery data records\n• Verify and process payment documentation\n• Manage your account and profile\n• Ensure security and prevent fraud\n• Comply with legal and regulatory requirements\n• Improve our services and user experience\n• Send important notifications and updates',
            ),
            _buildSection(
              'Data Storage and Security',
              'We implement appropriate security measures to protect your data:\n\n• Data is stored on secure cloud servers (Firebase)\n• Encryption is used for sensitive information\n• Access is restricted to authorized personnel only\n• Regular security audits and updates are performed\n• Device-specific identifiers help prevent unauthorized access',
            ),
            _buildSection(
              'Information Sharing',
              'We do not sell your personal information. We may share data only in these circumstances:\n\n• With your explicit consent\n• To comply with legal obligations\n• To protect our rights and prevent fraud\n• With service providers who assist in app operations\n• In case of business transfer or merger',
            ),
            _buildSection(
              'Data Retention',
              'We retain your information for as long as:\n\n• Your account remains active\n• Required by law or regulation\n• Necessary for legitimate business purposes\n• You can request deletion of your data at any time',
            ),
            _buildSection(
              'Your Rights Under Myanmar and Thai Law',
              'Under applicable data protection laws, you have the right to:\n\n• Access your personal information\n• Correct inaccurate data\n• Request deletion of your data (subject to legal retention requirements)\n• Object to data processing\n• Data portability (export your data)\n• Withdraw consent at any time\n• File complaints with relevant data protection authorities\n• Receive information about data breaches that may affect you',
            ),
            _buildSection(
              'Children\'s Privacy',
              'The App is not intended for users under 18 years of age. We do not knowingly collect personal information from children. If we become aware of such collection, we will delete the information immediately.',
            ),
            _buildSection(
              'Data Management Disclaimer',
              'This App is designed for data management and record-keeping purposes only. We do not facilitate actual betting or gambling activities. Users are responsible for ensuring their use of the App complies with local laws and regulations.',
            ),
            _buildSection(
              'Legal Compliance Framework',
              'Our data processing practices comply with:\n\n• Myanmar Computer Science Development Law (2013)\n• Myanmar Electronic Transactions Law (2004)\n• Thai Personal Data Protection Act (PDPA) 2019\n• ASEAN Data Protection frameworks\n• International best practices for data security',
            ),
            _buildSection(
              'Cross-Border Data Transfers',
              'As this App manages Thai 2D lottery data for users in Myanmar, data may be transferred between:\n\n• Myanmar (user location)\n• Thailand (data source country)\n• Cloud servers (Firebase/Google Cloud)\n\nWe ensure appropriate safeguards are in place for all cross-border transfers in compliance with both Myanmar and Thai data protection laws.',
            ),
            _buildSection(
              'Cookies and Tracking',
              'We use cookies and similar technologies to:\n\n• Remember your preferences\n• Analyze app usage patterns\n• Improve functionality and performance\n• Provide personalized experiences',
            ),
            _buildSection(
              'Third-Party Services',
              'The App integrates with third-party services:\n\n• Firebase (Google) for authentication and data storage\n• ImgBB for image hosting\n• Payment processors for financial transactions\n\nThese services have their own privacy policies.',
            ),
            _buildSection(
              'Changes to Privacy Policy',
              'We may update this Privacy Policy periodically. We will notify you of significant changes through the App or by email. Your continued use constitutes acceptance of the updated policy.',
            ),
            _buildSection(
              'Governing Law',
              'This Privacy Policy is governed by the laws of the Republic of the Union of Myanmar. For matters related to Thai 2D lottery data, we also comply with applicable Thai data protection laws.',
            ),
            _buildSection(
              'Contact Us',
              'For privacy-related questions, data requests, or complaints, contact us at:\n\nEmail: [Your Email]\nAddress: [Your Address]\nPhone: [Your Phone]\n\nFor data protection matters specifically related to Thai law compliance, you may also contact the relevant Thai data protection authorities.',
            ),
            const SizedBox(height: 16),
            Text(
              'Last updated: ${DateTime.now().toString().split(' ')[0]}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(content, style: const TextStyle(fontSize: 14, height: 1.5)),
        ],
      ),
    );
  }
}
