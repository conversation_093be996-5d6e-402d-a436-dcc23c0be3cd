// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:example/data/models/bet_raw.dart';

// import 'package:example/util/rfix.dart';
// import 'package:example/widgets/dialogs/app_dialogs.dart';

// import 'util.dart';
// import '../data/providers/bet_notifier.dart';

// Future<bool> check(String input, BuildContext context, WidgetRef ref) async {
//   if (input.isEmpty) {
//     ScaffoldMessenger.of(
//       context,
//     ).showSnackBar(const SnackBar(content: Text('Please enter a number')));
//     return Future.value(false);
//   }
//   try {
//     String fixed = fix(input);
//     BetRaw betRaw = multiGenerateTwo(fixed);
//     AppDialogs.showConfirmDialog(
//       context: context,
//       onConfirm: () async {
//         for (RBetModel bet in betRaw.bets) {
//           await ref
//               .read(betNotifierProvider.notifier)
//               .addBet(bet.number, bet.amount);
//         }
//       },

//       title: 'Confirm',
//       message:
//           betRaw.bets
//               .map((e) => "${e.number} = ${e.amount} = ${e.total}")
//               .join('\n') +
//           '\nTotal = ${betRaw.total}',
//     );
//   } on ArgumentError catch (e) {
//     ScaffoldMessenger.of(
//       context,
//     ).showSnackBar(SnackBar(content: Text(e.message)));
//     return Future.value(false);
//   } catch (e) {
//     ScaffoldMessenger.of(
//       context,
//     ).showSnackBar(SnackBar(content: Text(e.toString())));
//     return Future.value(false);
//   }

//   return Future.value(true);
// }
