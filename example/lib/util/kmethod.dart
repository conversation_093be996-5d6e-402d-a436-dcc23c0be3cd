List<String> getAhpu = [
  '00',
  '11',
  '22',
  '33',
  '44',
  '55',
  '66',
  '77',
  '88',
  '99',
];
List<String> getPower = ["05", "16", "27", "38", "49", "94", "83", "72", "61", "50"];
List<String> getNatkat = ["07", "18", "24", "35", "96", "69", "42", "81", "70", "53"];
List<String> getNyiko = [
  "01",
  "10",
  "12",
  "21",
  "23",
  "32",
  "34",
  "43",
  "45",
  "54",
  "56",
  "65",
  "67",
  "76",
  "78",
  "87",
  "89",
  "98",
  "90",
  "09",
];
List<String> getMama = [
  "11",
  "13",
  "15",
  "17",
  "19",
  "31",
  "33",
  "35",
  "37",
  "39",
  "51",
  "53",
  "55",
  "57",
  "59",
  "71",
  "73",
  "75",
  "77",
  "79",
  "91",
  "93",
  "95",
  "97",
  "99",
];
List<String> getSonesone = [
  "00",
  "02",
  "04",
  "06",
  "08",
  "20",
  "22",
  "24",
  "26",
  "28",
  "40",
  "42",
  "44",
  "46",
  "48",
  "60",
  "62",
  "64",
  "66",
  "68",
  "80",
  "82",
  "84",
  "86",
  "88",
];
List<String> getSonema = [
  "01",
  "03",
  "05",
  "07",
  "09",
  "21",
  "23",
  "25",
  "27",
  "29",
  "41",
  "43",
  "45",
  "47",
  "49",
  "61",
  "63",
  "65",
  "67",
  "69",
  "81",
  "83",
  "85",
  "87",
  "89",
];
List<String> getMasone = [
  "10",
  "12",
  "14",
  "16",
  "18",
  "30",
  "32",
  "34",
  "36",
  "38",
  "50",
  "52",
  "54",
  "56",
  "58",
  "70",
  "72",
  "74",
  "76",
  "78",
  "90",
  "92",
  "94",
  "96",
  "98",
];


  List<String> getZ(String s) {
    List<String> nb = [];

    if (s.isNotEmpty) {
      List<int> n =
          List<int>.generate(s.length, (int index) => int.parse(s[index]));
      List<int> n2 =
          List<int>.generate(s.length, (int index) => int.parse(s[index]));

      for (int j in n) {
        for (int i in n2) {
          if (j != i) {
            nb.add('$j$i');
          }
        }
      }
    }

    return nb;
  }

   List<String> getZz(String s) {
    List<String> nb = [];

    if (s.isNotEmpty) {
      List<int> n =
          List<int>.generate(s.length, (int index) => int.parse(s[index]));
      List<int> n2 =
          List<int>.generate(s.length, (int index) => int.parse(s[index]));

      for (int j in n) {
        for (int i in n2) {
          nb.add('$j$i');
        }
      }
    }

    return nb;
  }

   List<String> getK(String first, String last) {
    List<int> num =
        first.runes.map((rune) => rune - '0'.codeUnitAt(0)).toList();
    List<int> num2 =
        last.runes.map((rune) => rune - '0'.codeUnitAt(0)).toList();
    List<String> Klist = [];

    for (int j in num) {
      for (int a in num2) {
        String num1 = '$j$a';
        String num2 = '$a$j';
        if (num1.length == 2) {
          Klist.add(num1);
        }
        if (num2.length == 2) {
          Klist.add(num2);
        }
      }
    }

    return Klist;
  }

   List<String> getBk(String num) {
    List<String> result = [];

    List<String> numbers = [
      "00",
      "91",
      "19",
      "28",
      "82",
      "37",
      "73",
      "64",
      "46",
      "55",
      "01",
      "92",
      "29",
      "83",
      "38",
      "74",
      "47",
      "56",
      "65",
      "10",
      "20",
      "02",
      "93",
      "39",
      "48",
      "84",
      "57",
      "75",
      "66",
      "11",
      "30",
      "03",
      "94",
      "49",
      "85",
      "58",
      "76",
      "67",
      "12",
      "21",
      "40",
      "04",
      "95",
      "59",
      "86",
      "68",
      "77",
      "22",
      "13",
      "31",
      "50",
      "05",
      "96",
      "69",
      "87",
      "78",
      "23",
      "32",
      "41",
      "14",
      "60",
      "06",
      "97",
      "79",
      "88",
      "33",
      "42",
      "24",
      "51",
      "15",
      "70",
      "07",
      "98",
      "89",
      "34",
      "43",
      "25",
      "52",
      "16",
      "61",
      "80",
      "08",
      "99",
      "44",
      "62",
      "26",
      "71",
      "17",
      "53",
      "35",
      "90",
      "09",
      "81",
      "18",
      "72",
      "27",
      "63",
      "45",
      "54",
      "36"
    ];

    for (var c in num.split(" ")[0].runes) {
      String number = String.fromCharCode(c);
      switch (number) {
        case "0":
          result.addAll(numbers.sublist(0, 10));
          break;
        case "1":
          result.addAll(numbers.sublist(10, 20));
          break;
        case "2":
          result.addAll(numbers.sublist(20, 30));
          break;
        case "3":
          result.addAll(numbers.sublist(30, 40));
          break;
        case "4":
          result.addAll(numbers.sublist(40, 50));
          break;
        case "5":
          result.addAll(numbers.sublist(50, 60));
          break;
        case "6":
          result.addAll(numbers.sublist(60, 70));
          break;
        case "7":
          result.addAll(numbers.sublist(70, 80));
          break;
        case "8":
          result.addAll(numbers.sublist(80, 90));
          break;
        case "9":
          result.addAll(numbers.sublist(90, 100));
          break;
      }
    }
    return result;
  }

  bool isContains(String str, String sub) {
    String s = str.split(" ")[0].replaceAll(RegExp(r'[ 0-9]'), '').trim();
    return sub == s;
  }

   List<String> getBadayThar() {
    List<String> l = [];
    var n =
        '02,03,04,06,08,13,14,15,17,19,25,26,28,29,36,37,39,46,47,48,57,58,59,68,79';
    n.split(',').forEach((e) {
      l.add(getR(e));
      l.add(e);
    });
    return l;
  }

   String getR(String input) {
    String reversed = '';
    for (int i = input.length - 1; i >= 0; i--) {
      reversed += input[i];
    }
    return reversed;
  }