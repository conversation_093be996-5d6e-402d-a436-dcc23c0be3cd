const String phone = '09251651757';
const String telegram = 'https://t.me/jayythecracker';
const String facebook =
    'https://www.facebook.com/j4ckgonnaruinurhappiness?mibextid=ZbWKwL';

int odd = 80;
int comission = 10;
int limit = 10000;
int tableRow = 3;
double textSize = 16;
int rowSpacing = 3;
int refreshCount = 3;
String overColor = 'Red';

RegExp onlyNum = RegExp(r'[^0-9]');

bool share = false;

List<String> pricing = [];
String helper =
    'ဒဲ့ = 12 Enter 1000\n'
    'အာ = 12 Enter +1000\n'
    'ထိပ် = 1/ Enter 1000\n'
    'ဘိတ် = 1- Enter 1000\n'
    'ပါဝါ = // Enter 1000\n'
    'ခွေ = 12345/-- Enter 1000\n'
    'ခွေပူး = 12345/++ Enter 1000\n'
    'အပူး = ++ Enter 1000\n'
    'အပါ = 1* Enter 1000\n'
    'ညီကို = *** Enter 1000\n'
    'နက္ခက် = ** Enter 1000\n'
    'စုံစုံ = ++++ Enter 1000\n'
    'မမ = ---- Enter 1000\n'
    'စုံမ = ++-- Enter 1000\n'
    'မစုံ = --++ Enter 10000\n'
    'ဘရိတ် = 1-- Enter 1000\n'
    'အကပ် = 123//456 Enter 1000';
List<String> formats = [
  'ထိပ်',
  'ဘိတ်',
  'ပါဝါ',
  'အခွေ',
  'ခွေပူး',
  'အပူး',
  'အပါ',
  'ညီကို',
  'နက္ခက်',
  'စုံစုံ',
  'မမ',
  'စုံမ',
  'မစုံ',
  'ဘရိတ်',
  'ပဒေသာ',
  'ကပ်',
  'မကပ်',
  'စုံကပ်',
];

String r = 'အာ',
    htate = 'ထိပ်',
    bate = 'ဘိတ်',
    power = 'ပါဝါ',
    khway = 'ခွေ',
    khwaypu = 'ခွေပူး',
    ahpu = 'အပူး',
    ahpar = 'အပါ',
    nyiko = 'ညီကို',
    nakhat = 'နက္ခက်',
    sonesone = 'စုံစုံ',
    mama = 'မမ',
    sonema = 'စုံမ',
    masone = 'မစုံ',
    bk = 'ဘရိတ်',
    badatha = 'ပဒေသာ',
    ahkat = 'အကပ်';


Map replacement = {
  " r": "r",
  "+": "r",
  "---": "--",
  "----": "--",
  "-----": "--",
  "10r": "10=r",
  "1r": "1=r",
  "20r": "20=r",
  "2r": "2=r",
  "30r": "30=r",
  "50r": "50=r",
  "3r": "3=r",
  "40r": "40=r",
  "4r": "4=r",
  "5r": "5=r",
  "60r": "60=r",
  "6r": "6=r",
  "70r": "70=r",
  "7r": "7=r",
  "80r": "80=r",
  "8r": "8=r",
  "90r": "90=r",
  "9r": "9=r",
  "@": "r",
  "X": "အခွေ",
  "Z": "ခွေပူး",
  "b": "ဘရိတ်",
  "nk": "နက္ခက်",
  "နတ်ခတ်": "နက္ခက်",
  "p": "အပါ",
  "r ": "r",
  "r-": "r",
  "rr": "r",
  "ဒဲ့": "=",
  "နောက်": "ဘိတ်",
  "ပတ်": "အပါ",
  "ဘရိတ်-": "ဘရိတ်",
  "အပတ်": "အပါ",
  "အပီ": "ခွေပူး",
  "အပြီ": "ခွေပူး",
  "အပြီး": "ခွေပူး",
  "၀": "0",
  "၁": "1",
  "၂": "2",
  "၃": "3",
  "၄": "4",
  "၅": "5",
  "၆": "6",
  "၇": "7",
  "၈": "8",
  "၉": "9",
};
