import 'package:intl/intl.dart';

/// Utility functions for formatting values in the app
class FormatUtils {
  /// Format a number as currency without the currency symbol
  /// 
  /// Examples:
  /// - 100 -> "100"
  /// - 1000 -> "1K"
  /// - 1500 -> "1.5K"
  /// - 10000 -> "10K"
  /// - 1000000 -> "1M"
  static String formatAmount(int amount) {
    if (amount < 1000) {
      return amount.toString();
    } else if (amount < 10000) {
      // For 1,000 to 9,999, show as X.YK
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else if (amount < 1000000) {
      // For 10,000 to 999,999, show as XK
      return '${(amount / 1000).round()}K';
    } else {
      // For 1,000,000 and above, show as X.YM
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    }
  }
  
  /// Format a number as currency with the currency symbol
  /// 
  /// Examples:
  /// - 100 -> "฿100"
  /// - 1000 -> "฿1,000"
  /// - 1000000 -> "฿1,000,000"
  static String formatCurrency(int amount) {
    final formatter = NumberFormat.currency(
      symbol: '฿',
      decimalDigits: 0,
    );
    return formatter.format(amount);
  }
  
  /// Format a number as compact currency
  /// 
  /// Examples:
  /// - 100 -> "฿100"
  /// - 1000 -> "฿1K"
  /// - 1000000 -> "฿1M"
  static String formatCompactCurrency(int amount) {
    final formatter = NumberFormat.compactCurrency(
      symbol: '฿',
      decimalDigits: 0,
    );
    return formatter.format(amount);
  }

  /// Format a number as ,
  /// 
  /// Examples:
  /// - 100 -> "100"
  /// - 1000 -> "1,000"
  /// - 1000000 -> "1,000,000"
  static String formatNumber(int amount) {
    final formatter = NumberFormat('#,###');
    return formatter.format(amount);
  }
}
