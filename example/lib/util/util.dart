import '../data/models/bet_raw.dart';
import 'kconst.dart';
import 'kmethod.dart';

BetRaw multiGenerateTwo(String input) {
  List<RBetModel> betModels = [];
  // input = fix(input);

  // Use regular for loop instead of Future.forEach
  for (var slug in input.split("\n")) {
    betModels.add(generateTwo(slug));
  }

  BetRaw betRaws = BetRaw(
    total: betModels.fold(
      0,
      (previousValue, element) => previousValue + element.total,
    ),
    bets: betModels,
  );
  return betRaws;
}

RBetModel generateTwo(String slug) {
  slug = slug.toLowerCase();
  print("Current Slug : $slug");
  List<IBetModel> twoList = [];
  String rawNum = slug.split('=').first;
  String rawAmount = slug.split('=').last;
  int amount = int.tryParse(slug.split('=').last.replaceAll(onlyNum, '')) ?? 0;

  if (slug.contains('အပူး')) {
    for (var element in getAhpu) {
      twoList.add(IBetModel(number: element, amount: amount));
    }
  } else if (slug.contains("အပါ")) {
    String n = slug.split("=")[0];
    n = n.replaceAll(onlyNum, '');

    for (int i = 0; i < n.length; i++) {
      List<String> nList = [];
      String sN = n[i];
      for (int j = 0; j < 10; j++) {
        String n1 = '$j$sN';
        String nn = '$sN$j';
        nList.add(n1);
        nList.add(nn);
      }

      nList = nList.toSet().toList();

      for (String num in nList) {
        twoList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else if (rawNum.contains("နက္ခက်")) {
    for (String num in getNatkat) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("ပါဝါ")) {
    for (String num in getPower) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("ညီကို")) {
    for (String num in getNyiko) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("မမ")) {
    for (String num in getMama) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("စုံစုံ")) {
    for (String num in getSonesone) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("မစုံ")) {
    for (String num in getMasone) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("စုံမ")) {
    for (String num in getSonema) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains(badatha)) {
    for (String num in getBadayThar()) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("စုံကပ်")) {
    for (var c in rawNum.split(" ")[0].replaceAll(onlyNum, '').runes) {
      String number = c.toString();
      for (int i = 0; i < 10; i++) {
        if (i % 2 == 0) {
          String n = '$number$i';
          twoList.add(IBetModel(number: n, amount: amount));
          twoList.add(IBetModel(number: getR(n), amount: amount));
        }
      }
    }
  } else if (rawNum.contains("မကပ်")) {
    for (var c in rawNum.split(" ")[0].replaceAll(onlyNum, '').split('')) {
      String number = c.toString();
      for (int i = 0; i < 10; i++) {
        if (i % 2 != 0) {
          String n = "$number$i";
          twoList.add(IBetModel(number: n, amount: amount));
          twoList.add(IBetModel(number: getR(n), amount: amount));
        }
      }
    }
  } else if (rawNum.contains("အခွေ")) {
    String number = rawNum.split("=")[0].replaceAll(onlyNum, '');
    // Use regular for loop instead of Future.forEach
    for (var num in getZ(number)) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("ခွေပူး")) {
    String number = rawNum.split(" ")[0].replaceAll(onlyNum, '');
    // Use regular for loop instead of Future.forEach
    for (var num in getZz(number)) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("ကပ်") &&
      !rawNum.contains("မကပ်") &&
      !rawNum.contains("စုံကပ်")) {
    String first = rawNum.split("ကပ်")[0];
    String last = rawNum.split("ကပ်")[1].replaceAll("[^0-9]", "");
    // Use regular for loop instead of Future.forEach
    for (var num in getK(first, last)) {
      twoList.add(IBetModel(number: num, amount: amount));
    }
  } else if (rawNum.contains("ဘရိတ်")) {
    String n = rawNum.split(" ")[0].replaceAll(onlyNum, '');
    for (var c in n.runes) {
      String number = String.fromCharCode(c);
      // Use regular for loop instead of Future.forEach
      for (var num in getBk(number)) {
        twoList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else if (rawNum.contains("ထိပ်")) {
    String n = rawNum.split(" ")[0].replaceAll(onlyNum, '');
    for (var c in n.runes) {
      String number = String.fromCharCode(c);
      for (int i = 0; i < 10; i++) {
        String num = "$number$i";
        twoList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else if (rawNum.contains("ဘိတ်")) {
    String n = rawNum.split(" ")[0].replaceAll(onlyNum, '');
    for (var c in n.runes) {
      String number = String.fromCharCode(c);
      for (int i = 0; i < 10; i++) {
        String num = "$i$number";
        twoList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else {
    if (rawNum.contains(",")) {
      for (String num in rawNum.split(",")) {
        if (rawAmount.contains("r")) {
          String am1 = rawAmount.split("r")[0].replaceAll(RegExp("[^0-9]"), "");
          String am2 = rawAmount.split("r")[1].replaceAll(RegExp("[^0-9]"), "");
          if (am1.isEmpty) {
            twoList.add(IBetModel(number: num, amount: int.parse(am2)));
            twoList.add(IBetModel(number: getR(num), amount: int.parse(am2)));
          } else {
            twoList.add(IBetModel(number: num, amount: int.parse(am1)));
            twoList.add(IBetModel(number: getR(num), amount: int.parse(am2)));
          }
        } else {
          twoList.add(IBetModel(number: num, amount: amount));
        }
      }
    } else {
      if (rawAmount.contains("r")) {
        String am1 = rawAmount.split("r")[0].replaceAll(RegExp("[^0-9]"), "");
        String am2 = rawAmount.split("r")[1].replaceAll(RegExp("[^0-9]"), "");
        if (am1.isEmpty) {
          twoList.add(IBetModel(number: rawNum, amount: int.parse(am2)));
          twoList.add(IBetModel(number: getR(rawNum), amount: int.parse(am2)));
        } else {
          twoList.add(IBetModel(number: rawNum, amount: int.parse(am1)));
          twoList.add(IBetModel(number: getR(rawNum), amount: int.parse(am2)));
        }
      } else {
        twoList.add(IBetModel(number: rawNum, amount: amount));
      }
    }
  }

  int total = twoList.fold(0, (total, bet) => total + bet.amount);
  return RBetModel(
    total: total,
    bets: twoList,
    number: rawNum,
    amount: rawAmount,
  );
}

Future<List<IBetModel>> generateThree(String slug) async {
  List<IBetModel> threeList = [];
  String rawNum = slug.split('=')[0];
  String rawAmount = slug.split('=')[1];
  int amount = int.parse(slug.split('=')[1].replaceAll(RegExp(r'[^0-9]'), ''));

  if (slug.contains('အပူး')) {
    // Generate triplets (000, 111, 222, ..., 999)
    for (int i = 0; i < 10; i++) {
      String num = '$i$i$i';
      threeList.add(IBetModel(number: num, amount: amount));
    }
  } else if (slug.contains('ထိပ်')) {
    // Generate numbers with the specified digit at the beginning (e.g., 1xx)
    String n = rawNum.split(' ')[0];
    for (var c in n.runes) {
      String digit = String.fromCharCode(c);
      for (int i = 0; i < 100; i++) {
        String num = '$digit${i.toString().padLeft(2, '0')}';
        threeList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else if (slug.contains('ဘိတ်')) {
    // Generate numbers with the specified digit at the end (e.g., xx1)
    String n = rawNum.split(' ')[0];
    for (var c in n.runes) {
      String digit = String.fromCharCode(c);
      for (int i = 0; i < 100; i++) {
        String num = '${i.toString().padLeft(2, '0')}$digit';
        threeList.add(IBetModel(number: num, amount: amount));
      }
    }
  } else if (slug.contains('လယ်')) {
    // Generate numbers with the specified digit in the middle (e.g., x1x)
    String n = rawNum.split(' ')[0];
    for (var c in n.runes) {
      String digit = String.fromCharCode(c);
      for (int i = 0; i < 10; i++) {
        for (int j = 0; j < 10; j++) {
          String num = '$i$digit$j';
          threeList.add(IBetModel(number: num, amount: amount));
        }
      }
    }
  } else if (slug.contains('ပတ်လည်')) {
    // Generate all permutations of the given number
    String number = rawNum.split(' ')[0];
    if (number.length == 3) {
      Set<String> permutations = generatePermutations(number);
      for (String perm in permutations) {
        threeList.add(IBetModel(number: perm, amount: amount));
      }
    }
  } else {
    if (rawNum.contains(',')) {
      for (String num in rawNum.split(',')) {
        if (rawAmount.contains('r')) {
          String am1 = rawAmount
              .split('r')[0]
              .replaceAll(RegExp(r'[^0-9]'), '');
          String am2 = rawAmount
              .split('r')[1]
              .replaceAll(RegExp(r'[^0-9]'), '');
          if (am1.isEmpty) {
            threeList.add(IBetModel(number: num, amount: int.parse(am2)));
            threeList.add(IBetModel(number: getR(num), amount: int.parse(am2)));
          } else {
            threeList.add(IBetModel(number: num, amount: int.parse(am1)));
            threeList.add(IBetModel(number: getR(num), amount: int.parse(am2)));
          }
        } else {
          threeList.add(IBetModel(number: num, amount: amount));
        }
      }
    } else {
      if (rawAmount.contains('r')) {
        String am1 = rawAmount.split('r')[0].replaceAll(RegExp(r'[^0-9]'), '');
        String am2 = rawAmount.split('r')[1].replaceAll(RegExp(r'[^0-9]'), '');
        if (am1.isEmpty) {
          threeList.add(IBetModel(number: rawNum, amount: int.parse(am2)));
          threeList.add(
            IBetModel(number: getR(rawNum), amount: int.parse(am2)),
          );
        } else {
          threeList.add(IBetModel(number: rawNum, amount: int.parse(am1)));
          threeList.add(
            IBetModel(number: getR(rawNum), amount: int.parse(am2)),
          );
        }
      } else {
        threeList.add(IBetModel(number: rawNum, amount: amount));
      }
    }
  }

  return threeList;
}

// Helper function to generate all permutations of a string
Set<String> generatePermutations(String str) {
  Set<String> result = {};

  if (str.length <= 1) {
    result.add(str);
    return result;
  }

  for (int i = 0; i < str.length; i++) {
    String current = str[i];
    String remaining = str.substring(0, i) + str.substring(i + 1);

    for (String perm in generatePermutations(remaining)) {
      result.add(current + perm);
    }
  }

  return result;
}


// Set<IBetModel> convertListToSet(BetRaw betRaw) {
//   final Map<String, int> dataMap = {};

//   for (var d in betRaw.bets) {
//     final data = d.rawModel;
    
//     bool isAgent = d.bets
//     // Convert the amount to an integer
//     int amount = isAgent ? int.parse(data.amount) : -int.parse(data.amount);

//     // If the number exists in the map, add to the amount
//     if (dataMap.containsKey(data.number)) {
//       dataMap[data.number] = dataMap[data.number]! + amount;
//     } else {
//       // Otherwise, add the number and amount to the map
//       dataMap[data.number] = amount;
//     }
//   }

//   // Convert the map to a set of RawModel objects, converting amounts back to strings
//   return dataMap.entries
//       .map(
//         (entry) => RawModel(number: entry.key, amount: entry.value.toString()),
//       )
//       .toSet();
// }
