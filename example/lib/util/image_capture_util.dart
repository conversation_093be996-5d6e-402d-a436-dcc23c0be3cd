import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';

import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

/// Utility class for capturing widgets as images and saving them
class ImageCaptureUtil {
  /// Check if the device is running Android 13 (API level 33) or higher
  static Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;

    try {
      // Use device_info_plus to get Android SDK version
      // This is a simplified check - in a real app, you'd use device_info_plus
      // to get the actual SDK version
      return Platform.version.contains('Android 13') ||
          Platform.version.contains('Android 14') ||
          Platform.version.contains('Android 15');
    } catch (e) {
      debugPrint('Error checking Android version: $e');
      return false;
    }
  }

  /// Capture a widget as an image using a RepaintBoundary
  static Future<Uint8List?> captureWidget(GlobalKey boundaryKey) async {
    try {
      // Find the render object
      final RenderRepaintBoundary boundary =
          boundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      // Render the widget as an image
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);

      // Convert to PNG bytes
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        return byteData.buffer.asUint8List();
      }
    } catch (e) {
      debugPrint('Error capturing widget as image: $e');
    }

    return null;
  }

  /// Request storage permissions
  static Future<bool> _requestStoragePermissions() async {
    if (kIsWeb) return true; // Web doesn't need permissions

    if (Platform.isAndroid) {
      // For Android, we'll try a simpler approach first with just the essential permissions
      // Start with the most basic permission
      debugPrint('Requesting storage permission...');
      var storageStatus = await Permission.storage.request();
      debugPrint('Storage permission status: $storageStatus');

      if (storageStatus.isGranted) {
        debugPrint('Storage permission granted');
        return true;
      }

      // If storage permission is denied, try external storage permission
      if (!storageStatus.isGranted && !storageStatus.isPermanentlyDenied) {
        debugPrint('Requesting external storage permission...');
        var externalStatus = await Permission.manageExternalStorage.request();
        debugPrint('External storage permission status: $externalStatus');

        if (externalStatus.isGranted) {
          debugPrint('External storage permission granted');
          return true;
        }
      }

      // If permissions are permanently denied, suggest opening app settings
      if (storageStatus.isPermanentlyDenied) {
        debugPrint(
          'Storage permission permanently denied, opening app settings',
        );
        bool opened = await openAppSettings();
        debugPrint('App settings opened: $opened');
        return false;
      }

      // If we get here, permissions were denied but not permanently
      debugPrint('Storage permissions were denied');
      return false;
    } else if (Platform.isIOS) {
      // For iOS, we need photos permission
      debugPrint('Requesting iOS photos permission...');
      final status = await Permission.photos.request();
      debugPrint('iOS photos permission status: $status');

      if (!status.isGranted && status.isPermanentlyDenied) {
        debugPrint(
          'iOS photos permission permanently denied, opening app settings',
        );
        await openAppSettings();
      }
      return status.isGranted;
    }

    return true; // Default for other platforms
  }

  /// Save image to gallery
  static Future<bool> saveImageToGallery(
    Uint8List imageBytes, {
    String? name,
  }) async {
    try {
      // Request permissions first
      bool hasPermission = await _requestStoragePermissions();
      if (!hasPermission) {
        debugPrint('Storage permissions denied');
        return false;
      }

      // Save to gallery
      final result = await ImageGallerySaverPlus.saveImage(
        imageBytes,
        name:
            name ?? 'fighter_card_${DateTime.now().millisecondsSinceEpoch}.png',
        quality: 100,
      );

      return result['isSuccess'] ?? false;
    } catch (e) {
      debugPrint('Error saving image to gallery: $e');
      return false;
    }
  }

  /// Save image to file
  static Future<String?> saveImageToFile(
    Uint8List imageBytes, {
    String? name,
  }) async {
    try {
      if (kIsWeb) {
        // Web doesn't support direct file saving
        return null;
      }

      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          name ?? 'fighter_card_${DateTime.now().millisecondsSinceEpoch}.png';
      final filePath = '${directory.path}/$fileName';

      // Write to file
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);

      return filePath;
    } catch (e) {
      debugPrint('Error saving image to file: $e');
      return null;
    }
  }

  /// Capture and save a widget as an image
  static Future<bool> captureAndSaveWidget(
    GlobalKey boundaryKey,
    BuildContext context, {
    String? name,
    bool showResult = true,
  }) async {
    try {
      // Show loading indicator
      if (showResult && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Capturing image...')));
      }

      // Log for debugging
      debugPrint('Starting image capture process');

      // Request permissions first to avoid issues later
      bool hasPermission = await _requestStoragePermissions();
      if (!hasPermission) {
        debugPrint('Storage permissions denied');
        if (showResult && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Storage permissions required to save images',
              ),
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () async {
                  // Open app settings when the user taps the action button
                  await openAppSettings();
                },
              ),
              duration: const Duration(seconds: 5),
              backgroundColor: Colors.red.shade700,
            ),
          );
        }
        return false;
      }

      // Capture the widget
      debugPrint('Capturing widget as image');
      final imageBytes = await captureWidget(boundaryKey);

      if (imageBytes == null) {
        debugPrint('Failed to capture image - null bytes returned');
        if (showResult && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to capture image')),
          );
        }
        return false;
      }

      debugPrint(
        'Image captured successfully, size: ${imageBytes.length} bytes',
      );

      // Save to gallery
      debugPrint('Saving image to gallery');
      final success = await saveImageToGallery(imageBytes, name: name);
      debugPrint('Save result: $success');

      // Show result
      if (showResult && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Image saved to gallery'
                  : 'Failed to save image to gallery',
            ),
          ),
        );
      }

      return success;
    } catch (e) {
      debugPrint('Error in captureAndSaveWidget: $e');
      debugPrint('Stack trace: ${StackTrace.current}');

      if (showResult && context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
      }

      return false;
    }
  }
}
