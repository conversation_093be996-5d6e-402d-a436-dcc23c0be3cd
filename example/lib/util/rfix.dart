import 'kconst.dart';

String fix(String input) {
  input = input.toLowerCase();
  input = input.replaceAllMapped(
    RegExp(r'(?<!အ)(?<=\s|\d)(ပါ\d+)'),
    (match) => 'အ${match.group(1)}',
  );
   input = input.replaceAllMapped(
    RegExp(
      r'.*(?=(ထိပ်|ဘိတ်|ပါဝါ|အခွေ|ခွေပူး|အပူး|အပါ|ညီကို|နက္ခက်|စုံစုံ|မမ|စုံမ|မစုံ|ဘရိတ်|ပဒေသာ|ကပ်|မကပ်|စုံကပ်))',
    ),
    (match) {
      return match.group(0).toString().replaceAll(RegExp("[^0-9]"), '');
    },
  );

   input = input.replaceAll('/', '\n');

   //remove line that contains Total/Tt
   input = input.split('\n').where((line) => !line.contains('total') && !line.contains('tt')).join('\n');

  try {
    input = transform(input);


  } catch (e) {
    throw ArgumentError("Input is not valid");
  }
  input = input.replaceAll(RegExp(r'r=+'), 'r');
  RegExp regex = RegExp(
    r'(\d+00[^0-9]*r[^0-9]*\d+|\d+50[^0-9]*r\d+|\b\d+(?:50|00)\b(?![a-zA-Z])|\b\d+00\b|\b\d+(?:50|00)\b(?![a-zA-Z]))|r[^0-9]*\d+0|=50r\d{2,}0|=50\b',
  );
  input = input.replaceAllMapped(regex, (m) {
    return "=${m.group(0)}\n";
  });
  input = input.replaceAll(RegExp(" "), ',');
  input = input.replaceAll(RegExp(r"=+"), '=');
  input = input.replaceAll(RegExp(r",+"), ',');
  input = input.replaceAll(RegExp(r"[ \t]+", multiLine: true), '');
  input = input.replaceAll(",=", '=');
  List<String> lines =
      input.split('\n').where((line) => line.trim().isNotEmpty).toList();
  for (int index = 0; index < lines.length; index++) {
    String e = lines[index];
    List<String> parts = e.split('=');
    if (parts.length < 2) {
      if (e.length == 2 && index != 0) {
        lines[index] = "$e=${lines[index - 1].split('=').last}";
      } else {
        throw ArgumentError("Line ${index + 1}: '$e' doesn't contain amount");
      }
    } else {
      String number = parts.first;
      String amount = parts.last;
  
      if (number.length < 2 ) {
        
        throw ArgumentError(
          'Line ${index + 1}: Number length is less than 2: $number',
        );
      }
      if (amount.length < 2) {
        throw ArgumentError(
          'Line ${index + 1}: Amount length is less than 2: $amount',
        );
      }
      if (number.contains(',') && number.split(',').length > 1) {
        for (String n in number.split(',')) {
          if (n.length != 2) {
            throw ArgumentError("Line ${index + 1}: $n is not valid in $e");
          }
        }
      }
      if (!formats.contains(number.replaceAll(RegExp(r'\d'), '')) &&
          number.length != 2 &&
          !number.contains(',')) {
        throw ArgumentError(
          "Line ${index + 1}: Number length is less than 2: $number",
        );
      }
    }
  }
  return (lines.map((e) => e.replaceAll(RegExp(r"=+"), "=")).join('\n'));
}

String transform(String input, {bool mergeTrailing = false}) {
  // Remove lines without any digits
  final filteredLines =
      input.split('\n').where((line) => RegExp(r'\d').hasMatch(line)).toList();
  input = filteredLines.join('\n');

  // 1. Normalize
  input = input.toLowerCase();
  input = input.replaceAll(RegExp(r'[\.\s]'), ',');

  // 2. Tokenize on commas or newlines
  final tokens =
      input.split(RegExp(r'[,\\n-]+')).where((t) => t.isNotEmpty).toList();

  // Regex for amount tokens (user-provided)
  final amountRegex = RegExp(
    r'(\d+00[^0-9]*r[^0-9]*\d+|\d+50[^0-9]*r\d+|\b\d+(?:50|00)\b(?![a-zA-Z])|\b\d+00\b|\b\d+(?:50|00)\b(?![a-zA-Z]))|r[^0-9]*\d+0|=50r\d{2,}0|=50\b',
  );

  final result = <String>[];
  final buffer = <String>[];

  for (var token in tokens) {
    // explicit '=' assignments
    if (token.contains('=')) {
      final parts = token.split('=');
      buffer.add(parts[0]);
      result.add('${buffer.join(',')}=${parts[1]}');
      buffer.clear();
    }
    // amount tokens as per regex
    else if (amountRegex.hasMatch(token)) {
      final lhs = buffer.join(',');
      result.add(lhs.isEmpty ? token : '$lhs=$token');
      buffer.clear();
    }
    // any other 'r<digits>' or '<digits>r<digits>' fallback to split
    else if (RegExp(r'^\d+r\d+\\$|^r\d+\\$').hasMatch(token)) {
      final m = RegExp(r'^(\d+)?(r\d+)\$').firstMatch(token)!;
      final numPart = m.group(1);
      final rPart = m.group(2)!;
      final left =
          buffer.isEmpty
              ? (numPart ?? '')
              : '${buffer.join(',')}${numPart != null ? ',' + numPart : ''}';
      result.add('$left=$rPart');
      buffer.clear();
    }
    // otherwise collect
    else {
      buffer.add(token);
    }
  }

  // handle trailing buffer
  if (buffer.isNotEmpty) {
    if (!mergeTrailing) {
      throw ArgumentError('Unmatched trailing numbers: ${buffer.join(',')}');
    }
    if (result.isEmpty) {
      throw ArgumentError(
        'Nothing to merge into, but trailing numbers: ${buffer.join(',')}',
      );
    }
    final last = result.removeLast();
    final parts = last.split('=');
    final nums = parts[0].split(',')..addAll(buffer);
    final amt = parts[1];
    result.add('${nums.join(',')}=$amt');
  }

  return result.map((e) => fixString(e)).join('\n');
}

/// Fix strings containing Burmese-specific markers.
///
/// If [input] contains any of:
///   "ထိပ်နောက်", "ထိပ်ပိတ်", "ထိပ်ဘိတ်", or "ထိပ်r",
/// this method will remove any '/' in the numeric prefix,
/// split the prefix and suffix, map to the appropriate label,
/// and return "<prefix><label>=<suffix>".
/// Fix strings containing Burmese-specific markers, emitting both ထိပ် and ဘိတ် labels.
///
/// If [input] contains any of:
///   "ထိပ်နောက်", "ထိပ်ပိတ်", "ထိပ်ဘိတ်", or "ထိပ်r",
/// this method will remove any '/' in the numeric prefix,
/// split the prefix and suffix, and return two lines:
///   "<prefix>ထိပ်=<suffix>"
///   "<prefix>ဘိတ်=<suffix>"
String fixString(String input) {
  // Patterns to detect
  const markers = ['ထိပ်နောက်', 'ထိပ်ပိတ်', 'ထိပ်ဘိတ်', 'ထိပ်r'];

  for (var marker in markers) {
    if (input.contains(marker)) {
      // Split on the marker
      final parts = input.split(marker);
      // Remove slash(es) from the numeric prefix
      final prefix = parts[0].replaceAll('/', '');
      // The suffix is whatever follows the marker
      final suffix = parts[1];
      // Emit both labels
      return '${prefix}ထိပ်=$suffix\n${prefix}ဘိတ်=$suffix';
    }
  }

  // If no markers matched, return input unchanged
  return input;
}
