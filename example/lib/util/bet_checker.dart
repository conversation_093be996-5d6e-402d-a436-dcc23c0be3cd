import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/util/rfix.dart';
import 'package:example/util/util.dart';
import 'package:example/widgets/dialogs/bet_check_dialog.dart';

import '../data/providers/simple_providers.dart';

/// Checks the input string for valid bets and displays a dialog with the results.
///
/// Returns true if the bets were confirmed and added, false otherwise.
Future<bool> checkBets(
  String input,
  BuildContext context,
  WidgetRef ref, {
  Function(BetRaw betRaw)? onSuccess,
}) async {
  if (input.isEmpty) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Please enter some bets')));
    return false;
  }

  // List to store any errors encountered during processing
  List<String> errors = [];
  BetRaw betRaw = BetRaw.empty();

  try {
    // Fix the input format
    String fixedInput = fix(input);

    // Generate bets from the fixed input (now synchronous)
    betRaw = multiGenerateTwo(fixedInput);

    // Validate the generated bets
    if (betRaw.bets.isEmpty) {
      errors.add('No valid bets found in the input');
    }

    // Check for specific errors in each bet
    for (var bet in betRaw.bets) {
      // Check if the number is valid
      if (bet.number.isEmpty) {
        errors.add('Empty bet number found');
      }

      // Check if the amount is valid
      if (bet.amount.isEmpty ||
          int.tryParse(bet.amount.replaceAll(RegExp(r'[^0-9]'), '')) == null) {
        errors.add('Invalid amount for bet: ${bet.number}');
      }

      // Check if any individual bets were generated
      if (bet.bets.isEmpty) {
        errors.add('No valid bets generated for: ${bet.number}=${bet.amount}');
      }
    }
  } catch (e) {
    // Handle any exceptions during processing
    errors.add('Error processing bets: ${e.toString()}');
  }

  // Show the dialog with the results
  bool useBuiltInKeyboard = ref.read(useBuiltInKeyboardProvider);
  if (useBuiltInKeyboard) {
    if (errors.isEmpty && onSuccess != null) {
      onSuccess(betRaw);
    }
    return errors.isEmpty;
  }

  final result = await BetCheckDialog.show(
    context: context,
    betRaw: betRaw,
    errors: errors,
    onClose: () {
      if (errors.isEmpty && onSuccess != null) {
        onSuccess(betRaw);

        print('betRaw: $betRaw');
      }
    },
  );

  return (result ?? false);
}

/// Checks if a single bet is valid.
///
/// Returns a tuple of (isValid, errorMessage).
(bool, String?) isValidBet(String bet) {
  if (bet.isEmpty) {
    return (false, 'Bet cannot be empty');
  }

  // Check if the bet has the correct format (number=amount)
  if (!bet.contains('=')) {
    return (false, 'Bet must be in the format "number=amount"');
  }

  final parts = bet.split('=');
  if (parts.length != 2) {
    return (false, 'Bet must have exactly one "=" character');
  }

  final number = parts[0].trim();
  final amount = parts[1].trim();

  // Check if the number is valid
  if (number.isEmpty) {
    return (false, 'Bet number cannot be empty');
  }

  // Check if the amount is valid
  if (amount.isEmpty) {
    return (false, 'Bet amount cannot be empty');
  }

  // If the amount contains 'r', check both amounts
  if (amount.contains('r')) {
    final amountParts = amount.split('r');
    if (amountParts.length != 2) {
      return (false, 'Reversed bet must have exactly one "r" character');
    }

    final amount1 = amountParts[0].replaceAll(RegExp(r'[^0-9]'), '');
    final amount2 = amountParts[1].replaceAll(RegExp(r'[^0-9]'), '');

    if (amount1.isEmpty && amount2.isEmpty) {
      return (false, 'At least one amount must be specified for reversed bet');
    }

    if (amount1.isNotEmpty && int.tryParse(amount1) == null) {
      return (false, 'Invalid first amount for reversed bet');
    }

    if (amount2.isNotEmpty && int.tryParse(amount2) == null) {
      return (false, 'Invalid second amount for reversed bet');
    }
  } else {
    // Regular amount (not reversed)
    final cleanAmount = amount.replaceAll(RegExp(r'[^0-9]'), '');
    if (cleanAmount.isEmpty || int.tryParse(cleanAmount) == null) {
      return (false, 'Invalid bet amount');
    }
  }

  return (true, null);
}
