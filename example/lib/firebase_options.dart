// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD-XORjaCQUL3MV11CXOR0VxX_Nu3zQsjg',
    appId: '1:527087383210:web:c3e39c33119d180a6696d3',
    messagingSenderId: '527087383210',
    projectId: 'rpos-admin',
    authDomain: 'rpos-admin.firebaseapp.com',
    storageBucket: 'rpos-admin.firebasestorage.app',
    measurementId: 'G-FRPQVM6FTP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDua3EtYlB_oM6y1S2Qo4IAC6m2GKdOMcc',
    appId: '1:527087383210:android:e15ec09753e64ed36696d3',
    messagingSenderId: '527087383210',
    projectId: 'rpos-admin',
    storageBucket: 'rpos-admin.firebasestorage.app',
  );

}