import '../models/match.dart';
import '../repositories/match_repository.dart';

/// Service for Match operations
class MatchService {
  /// Repository for matches
  final MatchRepository _matchRepository;

  /// Constructor
  MatchService(this._matchRepository);

  /// Get all matches
  Future<List<Match>> getAllMatches() async {
    return await _matchRepository.getAllMatches();
  }

  /// Get a match by id
  Future<Match?> getMatchById(int id) async {
    return await _matchRepository.getMatchById(id);
  }

  /// Get matches by date
  Future<List<Match>> getMatchesByDate(String date) async {
    return await _matchRepository.getMatchesByDate(date);
  }

  /// Get last match
  Future<Match?> getLastMatch() async {
    return await _matchRepository.getLastMatch();
  }

  /// Create a new match
  Future<void> createMatch({
    required int id,
    required String brake,
    required String date,
    required String? luckyNo,
    required String bannedNo,
  }) async {
    final match = Match(
      id: id,
      brake: brake,
      date: date,
      luckyNo: luckyNo,
      bannedNo: bannedNo,
    );
    await _matchRepository.saveMatch(match);
  }

  /// Update a match
  Future<void> updateMatch({
    required int id,
    String? brake,
    String? date,
    String? luckyNo,
    String? bannedNo,
  }) async {
    final existingMatch = await _matchRepository.getMatchById(id);
    if (existingMatch == null) {
      throw Exception('Match not found');
    }

    final updatedMatch = existingMatch.copyWith(
      brake: brake,
      date: date,
      luckyNo: luckyNo,
      bannedNo: bannedNo,
    );

    await _matchRepository.saveMatch(updatedMatch);
  }

  /// Delete a match (soft delete)
  Future<void> deleteMatch(int id) async {
    final timestamp = DateTime.now().toIso8601String();
    await _matchRepository.softDeleteMatch(id, timestamp);
  }

  /// Parse banned numbers from string
  List<int> parseBannedNumbers(String bannedNoString) {
    if (bannedNoString.isEmpty) {
      return [];
    }
    
    return bannedNoString
        .split(',')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .map((s) => int.tryParse(s) ?? 0)
        .where((n) => n > 0)
        .toList();
  }
}
