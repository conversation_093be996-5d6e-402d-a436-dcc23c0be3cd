import '../models/fighter.dart';
import '../repositories/fighter_repository.dart';

/// Service for Fighter operations
class FighterService {
  /// Repository for fighters
  final FighterRepository _fighterRepository;

  /// Constructor
  FighterService(this._fighterRepository);

  /// Get all fighters
  Future<List<Fighter>> getAllFighters() async {
    return await _fighterRepository.getAllFighters();
  }

  /// Get a fighter by id
  Future<Fighter?> getFighterById(int id) async {
    return await _fighterRepository.getFighterById(id);
  }

  /// Search fighters by name
  Future<List<Fighter>> searchFightersByName(String name) async {
    return await _fighterRepository.getFightersByName(name);
  }

  /// Create a new fighter
  Future<void> createFighter({
    required int id,
    required String name,
    required double commission,
    required double odd,
    bool isDealer = false,
  }) async {
    final fighter = Fighter(
      id: id,
      name: name,
      commission: commission,
      odd: odd,
      isDealer: isDealer,
    );
    await _fighterRepository.saveFighter(fighter);
  }

  /// Update a fighter
  Future<void> updateFighter({
    required int id,
    String? name,
    double? commission,
    double? odd,
    bool? isDealer,
  }) async {
    final existingFighter = await _fighterRepository.getFighterById(id);
    if (existingFighter == null) {
      throw Exception('Fighter not found');
    }

    final updatedFighter = existingFighter.copyWith(
      name: name,
      commission: commission,
      odd: odd,
      isDealer: isDealer,
    );

    await _fighterRepository.saveFighter(updatedFighter);
  }

  /// Delete a fighter (soft delete)
  Future<void> deleteFighter(int id) async {
    final timestamp = DateTime.now().toIso8601String();
    await _fighterRepository.softDeleteFighter(id, timestamp);
  }
}
