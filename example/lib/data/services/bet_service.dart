import 'package:example/data/models/bet_raw.dart';

import '../models/bet.dart';
import '../repositories/bet_repository.dart';
import '../repositories/fighter_repository.dart';
import '../repositories/match_repository.dart';

/// Service for Bet operations
class BetService {
  /// Repository for bets
  final BetRepository _betRepository;

  /// Repository for fighters
  final FighterRepository _fighterRepository;

  /// Repository for matches
  final MatchRepository _matchRepository;

  /// Constructor
  BetService(
    this._betRepository,
    this._fighterRepository,
    this._matchRepository,
  );

  /// Get all bets
  Future<List<Bet>> getAllBets() async {
    return await _betRepository.getAllBets();
  }

  /// Get a bet by id
  Future<Bet?> getBetById(int id) async {
    return await _betRepository.getBetById(id);
  }

  /// Get bets by fighter
  Future<List<Bet>> getBetsByFighter(int fighterId) async {
    return await _betRepository.getBetsByFighter(fighterId);
  }

  /// Get bets by match
  Future<List<Bet>> getBetsByMatch(int matchId) async {
    return await _betRepository.getBetsByMatch(matchId);
  }

  /// Get bets by fighter and match
  Future<List<Bet>> getBetsByFighterAndMatch(int fighterId, int matchId) async {
    return await _betRepository.getBetsByFighterAndMatch(fighterId, matchId);
  }

  /// Create a new bet
  Future<void> createBet({
    required int id,
    required String number,
    required String amount,
    required int fighterId,
    required int matchId,
  }) async {
    // Validate fighter exists
    final fighter = await _fighterRepository.getFighterById(fighterId);
    if (fighter == null) {
      throw Exception('Fighter not found');
    }

    // Validate match exists
    final match = await _matchRepository.getMatchById(matchId);
    if (match == null) {
      throw Exception('Match not found');
    }

    // Validate number is not banned
    final bannedNumbers =
        match.bannedNo
            .split(',')
            .map((s) => s.trim())
            .where((s) => s.isNotEmpty)
            .toList();

    if (bannedNumbers.contains(number)) {
      throw Exception('Number $number is banned for this match');
    }

    // Create the bet
    final bet = Bet(
      id: id,
      number: number,
      amount: amount,
      fighter: fighterId,
      match: matchId,
      timestamp: DateTime.now().toIso8601String(),
    );

    await _betRepository.saveBet(bet);
  }

  /// Update a bet
  Future<void> updateBet({
    required int id,
    String? number,
    String? amount,
  }) async {
    final existingBet = await _betRepository.getBetById(id);
    if (existingBet == null) {
      throw Exception('Bet not found');
    }

    // If number is changing, validate it's not banned
    if (number != null && number != existingBet.number) {
      final match = await _matchRepository.getMatchById(existingBet.match);
      if (match != null) {
        final bannedNumbers =
            match.bannedNo
                .split(',')
                .map((s) => s.trim())
                .where((s) => s.isNotEmpty)
                .toList();

        if (bannedNumbers.contains(number)) {
          throw Exception('Number $number is banned for this match');
        }
      }
    }

    final updatedBet = existingBet.copyWith(
      number: number,
      amount: amount,
      timestamp: DateTime.now().toIso8601String(),
    );

    await _betRepository.saveBet(updatedBet);
  }

  /// Delete a bet (soft delete)
  Future<void> deleteBet(int id) async {
    final timestamp = DateTime.now().toIso8601String();

    await _betRepository.softDeleteBet(id, timestamp);
  }

  /// Get all bets for a specific match
  Future<List<Bet>> getBetsByMatchId(int matchId) async {
    return _betRepository.getBetsByMatch(matchId);
  }

  /// Create multiple bets at once
  Future<List<String>> createBatchBets({
    required List<RBetModel> numbers,

    required int fighterId,
    required int matchId,
  }) async {
    // Validate fighter exists
    final fighter = await _fighterRepository.getFighterById(fighterId);
    if (fighter == null) {
      throw Exception('Fighter not found');
    }

    // Validate match exists
    final match = await _matchRepository.getMatchById(matchId);
    if (match == null) {
      throw Exception('Match not found');
    }

    // Get banned numbers for validation
    final bannedNumbers =
        match.bannedNo
            .split(',')
            .map((s) => s.trim())
            .where((s) => s.isNotEmpty)
            .toList();

    // Track errors for each number
    final List<String> errors = [];
    final timestamp = DateTime.now().toIso8601String();

    // Process each number
    for (final number in numbers) {
      try {
        // Skip empty numbers
        if (number.total == 0) {
          continue;
        }

        for (IBetModel iBetModel in number.bets) {
          if (bannedNumbers.contains(iBetModel.number)) {
            errors.add('Number ${iBetModel.number} is banned for this match');
            continue;
          }
        }

        // Create a unique ID for the bet
        final id =
            DateTime.now().millisecondsSinceEpoch % 10000 +
            numbers.indexOf(number);

        // Create the bet
        final bet = Bet(
          id: id,
          number: number.number,
          amount: number.amount,
          fighter: fighterId,
          match: matchId,
          timestamp: timestamp,
        );

        await _betRepository.saveBet(bet);
      } catch (e) {
        errors.add('Error creating bet for number $number: ${e.toString()}');
      }
    }

    return errors;
  }

  /// Get total amount bet on a fighter in a match
  Future<double> getTotalBetAmountByFighterAndMatch(
    int fighterId,
    int matchId,
  ) async {
    return await _betRepository.getTotalBetAmountByFighterAndMatch(
      fighterId,
      matchId,
    );
  }

  /// Get count of bets on a fighter in a match
  Future<int> getBetCountByFighterAndMatch(int fighterId, int matchId) async {
    return await _betRepository.getBetCountByFighterAndMatch(
      fighterId,
      matchId,
    );
  }

  /// Get bets by group timestamp
  Future<List<Bet>> getBetsByGroupTimestamp(String groupTimestamp) async {
    return await _betRepository.getBetsByGroupTimestamp(groupTimestamp);
  }
}
