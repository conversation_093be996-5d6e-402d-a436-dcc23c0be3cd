import 'dart:convert';
import 'dart:io' as io;
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../models/match.dart';
import '../models/fighter.dart';
import '../models/bet.dart';
import '../repositories/match_repository.dart';
import '../repositories/fighter_repository.dart';
import '../repositories/bet_repository.dart';
import '../providers/simple_providers.dart';

/// Export format options
enum ExportFormat {
  /// JSON format
  json,

  /// CSV format
  csv,
}

/// Export data type options
enum ExportDataType {
  /// All data
  all,

  /// Only matches
  matches,

  /// Only fighters
  fighters,

  /// Only bets
  bets,
}

/// Result of an export operation
class ExportResult {
  /// Whether the export was successful
  final bool success;

  /// Error message if the export failed
  final String? errorMessage;

  /// Path to the exported file if successful
  final String? filePath;

  /// Constructor
  ExportResult({required this.success, this.errorMessage, this.filePath});
}

/// Service for exporting and importing data
class DataExportService {
  final MatchRepository _matchRepository;
  final FighterRepository _fighterRepository;
  final BetRepository _betRepository;

  /// Constructor
  DataExportService(
    this._matchRepository,
    this._fighterRepository,
    this._betRepository,
  );

  /// Export data to a file
  ///
  /// [format] - The format to export to (JSON or CSV)
  /// [dataType] - The type of data to export (all, matches, fighters, bets)
  /// [matchId] - Optional match ID to export data for a specific match
  Future<ExportResult> exportData({
    ExportFormat format = ExportFormat.json,
    ExportDataType dataType = ExportDataType.all,
    int? matchId,
  }) async {
    try {
      // If a specific match ID is provided and dataType is not matches or bets,
      // use the exportMatchData method instead
      if (matchId != null &&
          dataType != ExportDataType.matches &&
          dataType != ExportDataType.bets) {
        return await exportMatchData(
          matchId,
          format: format,
          includeFighters:
              dataType == ExportDataType.all ||
              dataType == ExportDataType.fighters,
        );
      }

      // Get data based on the requested type
      final Map<String, dynamic> exportData = await _getExportData(
        dataType,
        matchId: matchId,
      );

      // Generate filename with current date
      final now = DateTime.now();
      final dateStr =
          '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';

      // Add match ID to filename if specified
      final filenameDateStr =
          matchId != null ? '${dateStr}_match_$matchId' : dateStr;

      // Export based on the requested format
      switch (format) {
        case ExportFormat.json:
          return await _exportToJson(exportData, filenameDateStr, dataType);
        case ExportFormat.csv:
          return await _exportToCsv(exportData, filenameDateStr, dataType);
      }
    } catch (e) {
      debugPrint('Error exporting data: $e');
      return ExportResult(
        success: false,
        errorMessage: 'Error exporting data: $e',
      );
    }
  }

  /// Get export data based on the requested type
  Future<Map<String, dynamic>> _getExportData(
    ExportDataType dataType, {
    int? matchId,
  }) async {
    final Map<String, dynamic> exportData = {
      'version': 1,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Add requested data types
    switch (dataType) {
      case ExportDataType.all:
        final matches = await _matchRepository.getAllMatches();
        final fighters = await _fighterRepository.getAllFighters();
        final bets = await _betRepository.getAllBets();

        exportData['matches'] = matches.map((match) => match.toMap()).toList();
        exportData['fighters'] =
            fighters.map((fighter) => fighter.toMap()).toList();
        exportData['bets'] = bets.map((bet) => bet.toMap()).toList();
        break;

      case ExportDataType.matches:
        if (matchId != null) {
          final match = await _matchRepository.getMatchById(matchId);
          if (match != null) {
            exportData['matches'] = [match.toMap()];
          } else {
            exportData['matches'] = [];
          }
        } else {
          final matches = await _matchRepository.getAllMatches();
          exportData['matches'] =
              matches.map((match) => match.toMap()).toList();
        }
        break;

      case ExportDataType.fighters:
        final fighters = await _fighterRepository.getAllFighters();
        exportData['fighters'] =
            fighters.map((fighter) => fighter.toMap()).toList();
        break;

      case ExportDataType.bets:
        if (matchId != null) {
          // Get bets for a specific match
          final bets = await _betRepository.getBetsByMatch(matchId);
          exportData['bets'] = bets.map((bet) => bet.toMap()).toList();
        } else {
          final bets = await _betRepository.getAllBets();
          exportData['bets'] = bets.map((bet) => bet.toMap()).toList();
        }
        break;
    }

    return exportData;
  }

  /// Export data for a specific match
  Future<ExportResult> exportMatchData(
    int matchId, {
    ExportFormat format = ExportFormat.json,
    bool includeFighters = true,
  }) async {
    try {
      // Get the match data
      final match = await _matchRepository.getMatchById(matchId);
      if (match == null) {
        return ExportResult(success: false, errorMessage: 'Match not found');
      }

      // Get bets for this match
      final bets = await _betRepository.getBetsByMatch(matchId);

      // Create export data structure
      final Map<String, dynamic> exportData = {
        'version': 1,
        'timestamp': DateTime.now().toIso8601String(),
        'matches': [match.toMap()],
        'bets': bets.map((bet) => bet.toMap()).toList(),
      };

      // Add fighters if requested
      if (includeFighters) {
        final fighters = await _fighterRepository.getAllFighters();
        exportData['fighters'] =
            fighters.map((fighter) => fighter.toMap()).toList();
      }

      // Generate filename with match info
      final now = DateTime.now();
      final dateStr =
          '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
      final matchStr = 'match_${match.id}_${match.date.replaceAll(' ', '_')}';

      // Export based on the requested format
      switch (format) {
        case ExportFormat.json:
          return await _exportToJson(
            exportData,
            '${dateStr}_$matchStr',
            ExportDataType.all,
          );
        case ExportFormat.csv:
          // For CSV, we'll export separate files for match and bets
          final matchResult = await _exportToCsv(
            {
              'matches': [match.toMap()],
            },
            '${dateStr}_$matchStr',
            ExportDataType.matches,
          );

          final betsResult = await _exportToCsv(
            {'bets': bets.map((bet) => bet.toMap()).toList()},
            '${dateStr}_${matchStr}_bets',
            ExportDataType.bets,
          );

          // Return success if both exports succeeded
          return ExportResult(
            success: matchResult.success && betsResult.success,
            errorMessage:
                !matchResult.success
                    ? matchResult.errorMessage
                    : !betsResult.success
                    ? betsResult.errorMessage
                    : null,
          );
      }
    } catch (e) {
      debugPrint('Error exporting match data: $e');
      return ExportResult(
        success: false,
        errorMessage: 'Error exporting match data: $e',
      );
    }
  }

  /// Generate a filename based on data type, date, and extension
  String _generateFilename(
    ExportDataType dataType,
    String dateStr,
    String extension,
  ) {
    String prefix;

    switch (dataType) {
      case ExportDataType.all:
        prefix = 'ledger_export';
        break;
      case ExportDataType.matches:
        prefix = 'matches_export';
        break;
      case ExportDataType.fighters:
        prefix = 'fighters_export';
        break;
      case ExportDataType.bets:
        prefix = 'bets_export';
        break;
    }

    return '${prefix}_$dateStr.$extension';
  }

  /// Save file to downloads directory (mobile only)
  Future<String?> _saveToDownloads(
    String filename,
    List<int> bytes,
    String mimeType,
  ) async {
    try {
      // For Android 13+ (SDK 33+), we need to request specific media permissions
      if (io.Platform.isAndroid) {
        // Check and request permissions based on Android version
        final status = await Permission.storage.status;
        if (!status.isGranted) {
          final result = await Permission.storage.request();
          if (!result.isGranted) {
            debugPrint('Storage permission denied');
            return null;
          }
        }
      }

      // Web platform is handled in the calling methods
      if (kIsWeb) {
        return null;
      }

      // Try to save using ImageGallerySaver first (works better on Android 10+)
      try {
        if (!kIsWeb && io.Platform.isAndroid) {
          // Create a temporary file first
          final tempDir = await getTemporaryDirectory();
          final tempFile = io.File('${tempDir.path}/$filename');
          await tempFile.writeAsBytes(bytes);

          // Save the file using ImageGallerySaverPlus
          final result = await ImageGallerySaverPlus.saveFile(
            tempFile.path,
            name: filename,
          );

          if (result['isSuccess'] == true) {
            final filePath = result['filePath'] as String?;
            debugPrint('File saved to gallery: $filePath');
            return filePath;
          }
        }
      } catch (e) {
        debugPrint('Error saving to gallery: $e');
        // Fall back to other methods
      }

      // Different approach for different platforms
      if (!kIsWeb && io.Platform.isAndroid) {
        // For Android, use the downloads directory
        try {
          // First try to use the downloads directory
          final directory = await getExternalStorageDirectory();
          if (directory != null) {
            // Create downloads directory if it doesn't exist
            final downloadsDir = io.Directory('${directory.path}/Download');
            if (!await downloadsDir.exists()) {
              await downloadsDir.create(recursive: true);
            }

            // Create file path
            final filePath = '${downloadsDir.path}/$filename';

            // Write file
            final file = io.File(filePath);
            await file.writeAsBytes(bytes);

            debugPrint('File saved to: $filePath');
            return filePath;
          }
        } catch (e) {
          debugPrint('Error saving to external storage: $e');
          // Fall back to application documents directory
        }

        // Fall back to application documents directory
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$filename';
        final file = io.File(filePath);
        await file.writeAsBytes(bytes);

        debugPrint('File saved to app documents: $filePath');
        return filePath;
      } else if (!kIsWeb && io.Platform.isIOS) {
        // For iOS, use the application documents directory
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$filename';
        final file = io.File(filePath);
        await file.writeAsBytes(bytes);

        debugPrint('File saved to: $filePath');
        return filePath;
      } else if (!kIsWeb) {
        // For other platforms, use the application documents directory
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$filename';
        final file = io.File(filePath);
        await file.writeAsBytes(bytes);

        debugPrint('File saved to: $filePath');
        return filePath;
      }

      // If we get here, we couldn't save the file
      return null;
    } catch (e) {
      debugPrint('Error saving to downloads: $e');
      return null;
    }
  }

  /// Convert matches to CSV format
  String _convertMatchesToCsv(List matches) {
    // CSV header
    final buffer = StringBuffer('ID,Date,Brake,Lucky Number,Banned Numbers\n');

    // Add rows
    for (final matchMap in matches) {
      final match = Match.fromMap(matchMap);
      buffer.write('${match.id},');
      buffer.write('"${match.date}",');
      buffer.write('"${match.brake}",');
      buffer.write('${match.luckyNo},');
      buffer.write('"${match.bannedNo}"\n');
    }

    return buffer.toString();
  }

  /// Convert fighters to CSV format
  String _convertFightersToCsv(List fighters) {
    // CSV header
    final buffer = StringBuffer('ID,Name,Commission,Odd,Is Dealer\n');

    // Add rows
    for (final fighterMap in fighters) {
      final fighter = Fighter.fromMap(fighterMap);
      buffer.write('${fighter.id},');
      buffer.write('"${fighter.name}",');
      buffer.write('${fighter.commission},');
      buffer.write('${fighter.odd},');
      buffer.write('${fighter.isDealer}\n');
    }

    return buffer.toString();
  }

  /// Convert bets to CSV format
  String _convertBetsToCsv(List bets) {
    // CSV header
    final buffer = StringBuffer(
      'ID,Number,Amount,Fighter ID,Match ID,Timestamp\n',
    );

    // Add rows
    for (final betMap in bets) {
      final bet = Bet.fromMap(betMap);
      buffer.write('${bet.id},');
      buffer.write('"${bet.number}",');
      buffer.write('"${bet.amount}",');
      buffer.write('${bet.fighter},');
      buffer.write('${bet.match},');
      buffer.write('"${bet.timestamp}"\n');
    }

    return buffer.toString();
  }

  /// Export data to CSV format
  Future<ExportResult> _exportToCsv(
    Map<String, dynamic> exportData,
    String dateStr,
    ExportDataType dataType,
  ) async {
    try {
      // Generate CSV content based on data type
      String csvData = '';

      switch (dataType) {
        case ExportDataType.all:
          // For 'all', export each data type to a separate file
          final matchesResult = await _exportToCsv(
            {'matches': exportData['matches']},
            dateStr,
            ExportDataType.matches,
          );

          final fightersResult = await _exportToCsv(
            {'fighters': exportData['fighters']},
            dateStr,
            ExportDataType.fighters,
          );

          final betsResult = await _exportToCsv(
            {'bets': exportData['bets']},
            dateStr,
            ExportDataType.bets,
          );

          // Return success if all exports succeeded
          return ExportResult(
            success:
                matchesResult.success &&
                fightersResult.success &&
                betsResult.success,
            errorMessage:
                !matchesResult.success
                    ? matchesResult.errorMessage
                    : !fightersResult.success
                    ? fightersResult.errorMessage
                    : !betsResult.success
                    ? betsResult.errorMessage
                    : null,
          );

        case ExportDataType.matches:
          csvData = _convertMatchesToCsv(exportData['matches'] as List);
          break;

        case ExportDataType.fighters:
          csvData = _convertFightersToCsv(exportData['fighters'] as List);
          break;

        case ExportDataType.bets:
          csvData = _convertBetsToCsv(exportData['bets'] as List);
          break;
      }

      // Generate filename
      final filename = _generateFilename(dataType, dateStr, 'csv');

      // Save the file
      if (kIsWeb) {
        // For web, use FileSaver
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(utf8.encode(csvData)),
          ext: 'csv',
          mimeType: MimeType.csv,
        );
      } else {
        // For mobile, save to downloads directory
        final filePath = await _saveToDownloads(
          filename,
          utf8.encode(csvData),
          'text/csv',
        );

        if (filePath == null) {
          return ExportResult(
            success: false,
            errorMessage: 'Failed to save file to downloads directory',
          );
        }

        return ExportResult(success: true, filePath: filePath);
      }

      return ExportResult(success: true);
    } catch (e) {
      debugPrint('Error exporting to CSV: $e');
      return ExportResult(
        success: false,
        errorMessage: 'Error exporting to CSV: $e',
      );
    }
  }

  /// Export data to JSON format
  Future<ExportResult> _exportToJson(
    Map<String, dynamic> exportData,
    String dateStr,
    ExportDataType dataType,
  ) async {
    try {
      // Convert to JSON
      final jsonData = jsonEncode(exportData);

      // Generate filename
      final filename = _generateFilename(dataType, dateStr, 'json');

      // Save the file
      if (kIsWeb) {
        // For web, use FileSaver
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(utf8.encode(jsonData)),
          ext: 'json',
          mimeType: MimeType.json,
        );
      } else {
        // For mobile, save to downloads directory
        final filePath = await _saveToDownloads(
          filename,
          utf8.encode(jsonData),
          'application/json',
        );

        if (filePath == null) {
          return ExportResult(
            success: false,
            errorMessage: 'Failed to save file to downloads directory',
          );
        }

        return ExportResult(success: true, filePath: filePath);
      }

      return ExportResult(success: true);
    } catch (e) {
      debugPrint('Error exporting to JSON: $e');
      return ExportResult(
        success: false,
        errorMessage: 'Error exporting to JSON: $e',
      );
    }
  }

  /// Import data from a JSON file
  Future<bool> importData() async {
    try {
      // Pick a file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) {
        return false;
      }

      // Read file content
      final file = result.files.first;
      String jsonData;

      if (kIsWeb) {
        // For web, the bytes are already available
        if (file.bytes == null) {
          return false;
        }
        jsonData = utf8.decode(file.bytes!);
      } else {
        // For other platforms, read from the path
        if (file.path == null) {
          return false;
        }
        jsonData = await io.File(file.path!).readAsString();
      }

      // Parse JSON
      final Map<String, dynamic> importData = jsonDecode(jsonData);

      // Validate data structure
      if (!_validateImportData(importData)) {
        return false;
      }

      // Import matches
      final matchesList =
          (importData['matches'] as List)
              .map((matchMap) => Match.fromMap(matchMap))
              .toList();

      // Import fighters
      final fightersList =
          (importData['fighters'] as List)
              .map((fighterMap) => Fighter.fromMap(fighterMap))
              .toList();

      // Import bets
      final betsList =
          (importData['bets'] as List)
              .map((betMap) => Bet.fromMap(betMap))
              .toList();

      // Save to database
      for (final match in matchesList) {
        await _matchRepository.saveMatch(match);
      }

      for (final fighter in fightersList) {
        await _fighterRepository.saveFighter(fighter);
      }

      for (final bet in betsList) {
        await _betRepository.saveBet(bet);
      }

      return true;
    } catch (e) {
      debugPrint('Error importing data: $e');
      return false;
    }
  }

  /// Validate the import data structure
  bool _validateImportData(Map<String, dynamic> data) {
    // Check required fields
    if (!data.containsKey('version') ||
        !data.containsKey('matches') ||
        !data.containsKey('fighters') ||
        !data.containsKey('bets')) {
      return false;
    }

    // Check data types
    if (data['matches'] is! List ||
        data['fighters'] is! List ||
        data['bets'] is! List) {
      return false;
    }

    return true;
  }
}

/// Provider for the data export service
final dataExportServiceProvider = FutureProvider<DataExportService>((
  ref,
) async {
  final matchRepository = await ref.watch(matchRepositoryProvider.future);
  final fighterRepository = await ref.watch(fighterRepositoryProvider.future);
  final betRepository = await ref.watch(betRepositoryProvider.future);

  return DataExportService(matchRepository, fighterRepository, betRepository);
});
