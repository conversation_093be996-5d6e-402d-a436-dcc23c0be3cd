import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Keys for shared preferences
class PreferenceKeys {
  /// Key for dark mode setting
  static const String isDarkMode = 'is_dark_mode';
  
  /// Key for built-in keyboard setting
  static const String useBuiltInKeyboard = 'use_built_in_keyboard';
  
  // Add more keys as needed
}

/// Service for handling shared preferences
class SharedPrefsService {
  final SharedPreferences _prefs;

  /// Constructor
  SharedPrefsService(this._prefs);

  /// Initialize the shared preferences service
  static Future<SharedPrefsService> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    return SharedPrefsService(prefs);
  }

  /// Save a boolean value
  Future<bool> saveBool(String key, bool value) async {
    try {
      final result = await _prefs.setBool(key, value);
      debugPrint('Saved bool $key: $value');
      return result;
    } catch (e) {
      debugPrint('Error saving bool $key: $e');
      return false;
    }
  }

  /// Get a boolean value
  bool? getBool(String key) {
    try {
      final value = _prefs.getBool(key);
      debugPrint('Retrieved bool $key: $value');
      return value;
    } catch (e) {
      debugPrint('Error retrieving bool $key: $e');
      return null;
    }
  }

  /// Save a string value
  Future<bool> saveString(String key, String value) async {
    try {
      final result = await _prefs.setString(key, value);
      debugPrint('Saved string $key: $value');
      return result;
    } catch (e) {
      debugPrint('Error saving string $key: $e');
      return false;
    }
  }

  /// Get a string value
  String? getString(String key) {
    try {
      final value = _prefs.getString(key);
      debugPrint('Retrieved string $key: $value');
      return value;
    } catch (e) {
      debugPrint('Error retrieving string $key: $e');
      return null;
    }
  }

  /// Save an integer value
  Future<bool> saveInt(String key, int value) async {
    try {
      final result = await _prefs.setInt(key, value);
      debugPrint('Saved int $key: $value');
      return result;
    } catch (e) {
      debugPrint('Error saving int $key: $e');
      return false;
    }
  }

  /// Get an integer value
  int? getInt(String key) {
    try {
      final value = _prefs.getInt(key);
      debugPrint('Retrieved int $key: $value');
      return value;
    } catch (e) {
      debugPrint('Error retrieving int $key: $e');
      return null;
    }
  }

  /// Save a double value
  Future<bool> saveDouble(String key, double value) async {
    try {
      final result = await _prefs.setDouble(key, value);
      debugPrint('Saved double $key: $value');
      return result;
    } catch (e) {
      debugPrint('Error saving double $key: $e');
      return false;
    }
  }

  /// Get a double value
  double? getDouble(String key) {
    try {
      final value = _prefs.getDouble(key);
      debugPrint('Retrieved double $key: $value');
      return value;
    } catch (e) {
      debugPrint('Error retrieving double $key: $e');
      return null;
    }
  }

  /// Save a list of strings
  Future<bool> saveStringList(String key, List<String> value) async {
    try {
      final result = await _prefs.setStringList(key, value);
      debugPrint('Saved string list $key: $value');
      return result;
    } catch (e) {
      debugPrint('Error saving string list $key: $e');
      return false;
    }
  }

  /// Get a list of strings
  List<String>? getStringList(String key) {
    try {
      final value = _prefs.getStringList(key);
      debugPrint('Retrieved string list $key: $value');
      return value;
    } catch (e) {
      debugPrint('Error retrieving string list $key: $e');
      return null;
    }
  }

  /// Remove a value
  Future<bool> remove(String key) async {
    try {
      final result = await _prefs.remove(key);
      debugPrint('Removed $key');
      return result;
    } catch (e) {
      debugPrint('Error removing $key: $e');
      return false;
    }
  }

  /// Clear all values
  Future<bool> clear() async {
    try {
      final result = await _prefs.clear();
      debugPrint('Cleared all preferences');
      return result;
    } catch (e) {
      debugPrint('Error clearing preferences: $e');
      return false;
    }
  }
}

/// Provider for the shared preferences service
final sharedPrefsServiceProvider = FutureProvider<SharedPrefsService>((ref) async {
  return SharedPrefsService.initialize();
});
