import 'package:floor/floor.dart';
import '../models/fighter.dart';

/// Data Access Object for the Fighter entity.
@dao
abstract class FighterDao {
  /// Get all fighters
  @Query('SELECT * FROM fighters WHERE deletedAt IS NULL')
  Future<List<Fighter>> findAllFighters();

  /// Get a fighter by id
  @Query('SELECT * FROM fighters WHERE id = :id AND deletedAt IS NULL')
  Future<Fighter?> findFighterById(int id);

  /// Get fighters by name
  @Query('SELECT * FROM fighters WHERE name LIKE :name AND deletedAt IS NULL')
  Future<List<Fighter>> findFightersByName(String name);

  /// Insert a fighter
  @insert
  Future<void> insertFighter(Fighter fighter);

  /// Insert multiple fighters
  @insert
  Future<void> insertFighters(List<Fighter> fighters);

  /// Update a fighter
  @update
  Future<void> updateFighter(Fighter fighter);

  /// Delete a fighter (soft delete)
  @Query('UPDATE fighters SET deletedAt = :timestamp WHERE id = :id')
  Future<void> softDeleteFighter(int id, String timestamp);

  /// Hard delete a fighter
  @delete
  Future<void> deleteFighter(Fighter fighter);
}
