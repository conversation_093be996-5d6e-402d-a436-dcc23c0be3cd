import 'package:floor/floor.dart';
import '../models/match.dart';

/// Data Access Object for the Match entity.
@dao
abstract class MatchDao {
  /// Get all matches
  @Query('SELECT * FROM matches WHERE deletedAt IS NULL')
  Future<List<Match>> findAllMatches();

  /// Get a match by id
  @Query('SELECT * FROM matches WHERE id = :id AND deletedAt IS NULL')
  Future<Match?> findMatchById(int id);

  /// Get matches by date
  @Query('SELECT * FROM matches WHERE date = :date AND deletedAt IS NULL')
  Future<List<Match>> findMatchesByDate(String date);

  /// Insert a match
  @insert
  Future<void> insertMatch(Match match);

  /// Insert multiple matches
  @insert
  Future<void> insertMatches(List<Match> matches);

  /// Update a match
  @update
  Future<void> updateMatch(Match match);

  /// Delete a match (soft delete)
  @Query('UPDATE matches SET deletedAt = :timestamp WHERE id = :id')
  Future<void> softDeleteMatch(int id, String timestamp);

  /// Hard delete a match
  @delete
  Future<void> deleteMatch(Match match);

  ///find last match
  @Query('SELECT * FROM matches WHERE deletedAt IS NULL ORDER BY id DESC LIMIT 1')
  Future<Match?> findLastMatch();
}
