import 'package:floor/floor.dart';
import '../models/bet.dart';

/// Data Access Object for the Bet entity.
@dao
abstract class BetDao {
  /// Get all bets
  @Query('SELECT * FROM bets WHERE deletedAt IS NULL')
  Future<List<Bet>> findAllBets();

  /// Get a bet by id
  @Query('SELECT * FROM bets WHERE id = :id AND deletedAt IS NULL')
  Future<Bet?> findBetById(int id);

  /// Get bets by fighter id
  @Query('SELECT * FROM bets WHERE fighter = :fighterId AND deletedAt IS NULL')
  Future<List<Bet>> findBetsByFighter(int fighterId);

  /// Get bets by match id
  @Query('SELECT * FROM bets WHERE "match" = :matchId AND deletedAt IS NULL')
  Future<List<Bet>> findBetsByMatch(int matchId);

  /// Get bets by fighter and match
  @Query(
    'SELECT * FROM bets WHERE fighter = :fighterId AND "match" = :matchId AND deletedAt IS NULL',
  )
  Future<List<Bet>> findBetsByFighterAndMatch(int fighterId, int matchId);

  /// Get bets by number
  @Query('SELECT * FROM bets WHERE number = :number AND deletedAt IS NULL')
  Future<List<Bet>> findBetsByNumber(String number);

  /// Insert a bet
  @insert
  Future<void> insertBet(Bet bet);

  /// Insert multiple bets
  @insert
  Future<void> insertBets(List<Bet> bets);

  /// Update a bet
  @update
  Future<void> updateBet(Bet bet);

  /// Delete a bet (soft delete)
  @Query('UPDATE bets SET deletedAt = :timestamp WHERE id = :id')
  Future<void> softDeleteBet(int id, String timestamp);

  /// Hard delete a bet
  @delete
  Future<void> deleteBet(Bet bet);

  /// Get total amount bet on a fighter in a match
  @Query(
    'SELECT SUM(CAST(amount AS REAL)) FROM bets WHERE fighter = :fighterId AND "match" = :matchId AND deletedAt IS NULL',
  )
  Future<double?> getTotalBetAmountByFighterAndMatch(
    int fighterId,
    int matchId,
  );

  /// Get count of bets on a fighter in a match
  @Query(
    'SELECT COUNT(*) FROM bets WHERE fighter = :fighterId AND "match" = :matchId AND deletedAt IS NULL',
  )
  Future<int?> getBetCountByFighterAndMatch(int fighterId, int matchId);

  ///Get bets by date (using the date part of the timestamp)
  @Query(
    "SELECT * FROM bets WHERE timestamp LIKE :datePrefix || '%' AND deletedAt IS NULL",
  )
  Future<List<Bet>> getBetsByGroupTimestamp(String datePrefix);
}
