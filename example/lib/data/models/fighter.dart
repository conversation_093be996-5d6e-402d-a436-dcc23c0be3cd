import 'package:floor/floor.dart';

/// Fighter entity representing a fighter in the system.
@Entity(tableName: 'fighters')
class Fighter {
  /// Primary key for the fighter
  @PrimaryKey()
  final int id;

  /// Name of the fighter
  final String name;

  /// Commission rate for the fighter
  final double commission;

  /// Odds for the fighter
  final double odd;

  /// Whether the fighter is a dealer
  final bool isDealer;

  /// Deletion timestamp (null if not deleted)
  final String? deletedAt;

  /// Constructor for Fighter
  Fighter({
    required this.id,
    required this.name,
    required this.commission,
    required this.odd,
    this.isDealer = false,
    this.deletedAt,
  });

  /// Create a copy of this Fighter with the given fields replaced with new values
  Fighter copyWith({
    int? id,
    String? name,
    double? commission,
    double? odd,
    bool? isDealer,
    String? deletedAt,
  }) {
    return Fighter(
      id: id ?? this.id,
      name: name ?? this.name,
      commission: commission ?? this.commission,
      odd: odd ?? this.odd,
      isDealer: isDealer ?? this.isDealer,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  /// Convert Fighter to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'commission': commission,
      'odd': odd,
      'isDealer': isDealer,
      'deleted_at': deletedAt,
    };
  }

  /// Create Fighter from a Map
  factory Fighter.fromMap(Map<String, dynamic> map) {
    return Fighter(
      id: map['id'] as int,
      name: map['name'] as String,
      commission: map['commission'] as double,
      odd: map['odd'] as double,
      isDealer: map['isDealer'] as bool? ?? false,
      deletedAt: map['deleted_at'] as String?,
    );
  }
}
