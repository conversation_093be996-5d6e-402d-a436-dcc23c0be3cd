/// User model representing a user in the system
class User {
  /// Device ID of the user
  final String deviceId;
  
  /// Email of the user
  final String email;
  
  /// Last used timestamp
  final DateTime lastUsed;
  
  /// Name of the user
  final String name;
  
  /// Phone number of the user
  final String phone;
  
  /// Role of the user (admin, user, etc.)
  final String role;
  
  /// Tester expiration timestamp
  final DateTime? testerExpiration;
  
  /// Constructor for User
  User({
    required this.deviceId,
    required this.email,
    required this.lastUsed,
    required this.name,
    required this.phone,
    required this.role,
    this.testerExpiration,
  });
  
  /// Create a copy of this User with the given fields replaced with new values
  User copyWith({
    String? deviceId,
    String? email,
    DateTime? lastUsed,
    String? name,
    String? phone,
    String? role,
    DateTime? testerExpiration,
  }) {
    return User(
      deviceId: deviceId ?? this.deviceId,
      email: email ?? this.email,
      lastUsed: lastUsed ?? this.lastUsed,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      testerExpiration: testerExpiration ?? this.testerExpiration,
    );
  }
  
  /// Convert User to a Map
  Map<String, dynamic> toMap() {
    return {
      'deviceId': deviceId,
      'email': email,
      'lastUsed': lastUsed.toIso8601String(),
      'name': name,
      'phone': phone,
      'role': role,
      'testerExpiration': testerExpiration?.toIso8601String(),
    };
  }
  
  /// Create User from a Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      deviceId: map['deviceId'] as String,
      email: map['email'] as String,
      lastUsed: DateTime.parse(map['lastUsed'] as String),
      name: map['name'] as String,
      phone: map['phone'] as String,
      role: map['role'] as String,
      testerExpiration: map['testerExpiration'] != null 
          ? DateTime.parse(map['testerExpiration'] as String) 
          : null,
    );
  }
  
  /// Create a mock user for development
  factory User.mock() {
    return User(
      deviceId: "AQ3A.241006.001",
      email: "<EMAIL>",
      lastUsed: DateTime.parse("2025-05-01T01:14:48+06:30"),
      name: "ruki",
      phone: "09251651757",
      role: "admin",
      testerExpiration: DateTime.parse("2025-04-19T05:33:55+06:30"),
    );
  }
}
