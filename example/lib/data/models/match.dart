import 'package:floor/floor.dart';

/// Match entity representing a match in the system.
@Entity(tableName: 'matches')
class Match {
  /// Primary key for the match
  @PrimaryKey()
  final int id;

  /// Brake information for the match
  final String brake;

  /// Date of the match (stored as string)
  final String date;

  /// Deletion timestamp (null if not deleted)
  final String? deletedAt;

  /// Lucky number for the match
  final String? luckyNo;

  /// Banned numbers for the match (comma-separated string)
  final String bannedNo;

  /// Constructor for Match
  Match({
    required this.id,
    required this.brake,
    required this.date,
    this.deletedAt,
    required this.luckyNo,
    required this.bannedNo,
  });

  /// Create a copy of this Match with the given fields replaced with new values
  Match copyWith({
    int? id,
    String? brake,
    String? date,
    String? deletedAt,
    String? luckyNo,
    String? bannedNo,
  }) {
    return Match(
      id: id ?? this.id,
      brake: brake ?? this.brake,
      date: date ?? this.date,
      deletedAt: deletedAt ?? this.deletedAt,
      luckyNo: luckyNo ?? this.luckyNo,
      bannedNo: bannedNo ?? this.bannedNo,
    );
  }

  /// Convert Match to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'brake': brake,
      'date': date,
      'deleted_at': deletedAt,
      'lucky_no': luckyNo,
      'banned_no': bannedNo,
    };
  }

  /// Create Match from a Map
  factory Match.fromMap(Map<String, dynamic> map) {
    return Match(
      id: map['id'] as int,
      brake: map['brake'] as String,
      date: map['date'] as String,
      deletedAt: map['deleted_at'] as String?,
      luckyNo: map['lucky_no'].toString(),
      bannedNo: map['banned_no'] as String,
    );
  }
}
