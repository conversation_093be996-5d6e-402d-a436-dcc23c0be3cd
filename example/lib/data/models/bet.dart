import 'package:floor/floor.dart';

import 'fighter.dart';
import 'match.dart';

/// Bet entity representing a bet in the system.
@Entity(
  tableName: 'bets',
  foreignKeys: [
    ForeignKey(
      childColumns: ['fighter'],
      parentColumns: ['id'],
      entity: Fighter,
      onDelete: ForeignKeyAction.cascade,
    ),
    ForeignKey(
      childColumns: ['match'],
      parentColumns: ['id'],
      entity: Match,
      onDelete: ForeignKeyAction.cascade,
    ),
  ],
  indices: [
    Index(value: ['fighter']),
    Index(value: ['match']),
  ],
)
class Bet {
  /// Primary key for the bet
  @PrimaryKey()
  final int id;

  /// Number selected for the bet
  final String number;

  /// Amount of the bet
  final String amount;

  /// Fighter ID associated with this bet
  final int fighter;

  /// Match ID associated with this bet
  final int match;

  /// Deletion timestamp (null if not deleted)
  final String? deletedAt;

  /// Timestamp when the bet was placed
  final String timestamp;

  /// Constructor for Bet
  Bet({
    required this.id,
    required this.number,
    required this.amount,
    required this.fighter,
    required this.match,
    this.deletedAt,
    required this.timestamp,
  });

  /// Create a copy of this Bet with the given fields replaced with new values
  Bet copyWith({
    int? id,
    String? number,
    String? amount,
    int? fighter,
    int? match,
    String? deletedAt,
    String? timestamp,
  }) {
    return Bet(
      id: id ?? this.id,
      number: number ?? this.number,
      amount: amount ?? this.amount,
      fighter: fighter ?? this.fighter,
      match: match ?? this.match,
      deletedAt: deletedAt ?? this.deletedAt,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Convert Bet to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'number': number,
      'amount': amount,
      'fighter': fighter,
      'match': match,
      'deleted_at': deletedAt,
      'timestamp': timestamp,
    };
  }

  /// Create Bet from a Map
  factory Bet.fromMap(Map<String, dynamic> map) {
    return Bet(
      id: map['id'] as int,
      number: map['number'] as String,
      amount: map['amount'] as String,
      fighter: map['fighter'] as int,
      match: map['match'] as int,
      deletedAt: map['deleted_at'] as String?,
      timestamp: map['timestamp'] as String,
    );
  }
}
