import 'dart:convert';

import 'package:flutter/foundation.dart';

class BetRaw {
  int total;
  List<RBetModel> bets;
  BetRaw({
    required this.total,
    required this.bets,
  });
  factory BetRaw.empty() {
    return BetRaw(
      total: 0,
      bets: [],
    );
  }

  BetRaw copyWith({
    int? total,
    List<RBetModel>? bets,
  }) {
    return BetRaw(
      total: total ?? this.total,
      bets: bets ?? this.bets,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};
  
    result.addAll({'total': total});
    result.addAll({'bets': bets.map((x) => x.toMap()).toList()});
  
    return result;
  }

  factory BetRaw.fromMap(Map<String, dynamic> map) {
    return BetRaw(
      total: map['total']?.toInt() ?? 0,
      bets: List<RBetModel>.from(map['bets']?.map((x) => RBetModel.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory BetRaw.fromJson(String source) => BetRaw.fromMap(json.decode(source));

  @override
  String toString() => 'BetRaw(total: $total, bets: $bets)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is BetRaw &&
      other.total == total &&
      listEquals(other.bets, bets);
  }

  @override
  int get hashCode => total.hashCode ^ bets.hashCode;
}

class RBetModel {
  String number;
  String amount;
  List<IBetModel> bets;
  int total;
  
  RBetModel({
    required this.number,
    required this.amount,
    required this.bets,
    required this.total,
  });

  RBetModel copyWith({
    String? number,
    String? amount,
    List<IBetModel>? bets,
    int? total,
  }) {
    return RBetModel(
      number: number ?? this.number,
      amount: amount ?? this.amount,
      bets: bets ?? this.bets,
      total: total ?? this.total,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};
  
    result.addAll({'number': number});
    result.addAll({'amount': amount});
    result.addAll({'bets': bets.map((x) => x.toMap()).toList()});
    result.addAll({'total': total});
  
    return result;
  }

  factory RBetModel.fromMap(Map<String, dynamic> map) {
    return RBetModel(
      number: map['number'] ?? '',
      amount: map['amount'] ?? '',
      bets: List<IBetModel>.from(map['bets']?.map((x) => IBetModel.fromMap(x))),
      total: map['total']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory RBetModel.fromJson(String source) => RBetModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'RBetModel(number: $number, amount: $amount, bets: $bets, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is RBetModel &&
      other.number == number &&
      other.amount == amount &&
      listEquals(other.bets, bets) &&
      other.total == total;
  }

  @override
  int get hashCode {
    return number.hashCode ^
      amount.hashCode ^
      bets.hashCode ^
      total.hashCode;
  }
}

class IBetModel {
  String number;
  int amount;
  IBetModel({
    required this.number,
    required this.amount,
  });

  IBetModel copyWith({
    String? number,
    int? amount,
  }) {
    return IBetModel(
      number: number ?? this.number,
      amount: amount ?? this.amount,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};
  
    result.addAll({'number': number});
    result.addAll({'amount': amount});
  
    return result;
  }

  factory IBetModel.fromMap(Map<String, dynamic> map) {
    return IBetModel(
      number: map['number'] ?? '',
      amount: map['amount']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory IBetModel.fromJson(String source) => IBetModel.fromMap(json.decode(source));

  @override
  String toString() => 'IBetModel(number: $number, amount: $amount)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is IBetModel &&
      other.number == number &&
      other.amount == amount;
  }

  @override
  int get hashCode => number.hashCode ^ amount.hashCode;
}
