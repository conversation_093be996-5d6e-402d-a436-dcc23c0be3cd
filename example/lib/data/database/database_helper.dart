import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'app_database.dart';

/// Helper class for database operations with web support
class DatabaseHelper {
  /// Initialize the database with web support
  static Future<AppDatabase> initializeDatabase() async {
    try {
      // Create and return the database
      return await AppDatabase.createDatabase();
    } catch (e) {
      debugPrint('Error in DatabaseHelper.initializeDatabase: $e');
      rethrow;
    }
  }

  /// Get the database file path
  static Future<String> getDatabasePath() async {
    if (kIsWeb) {
      // Web storage is handled by the browser
      return 'app_database.db';
    } else {
      // For mobile platforms, use the app documents directory
      final documentsDirectory = await getApplicationDocumentsDirectory();
      return join(documentsDirectory.path, 'app_database.db');
    }
  }

  /// Delete the database (for testing or reset purposes)
  static Future<void> deleteDatabase() async {
    if (!kIsWeb) {
      final path = await getDatabasePath();
      await databaseFactory.deleteDatabase(path);
    }
    // For web, we would need to clear IndexedDB, but that's handled differently
  }
}
