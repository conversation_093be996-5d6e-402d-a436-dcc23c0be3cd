import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';
import 'app_database.dart';


/// Class to initialize the database
class DatabaseInitializer {
  /// Initialize the database
  static Future<AppDatabase> initialize() async {
    // Configure for web platform
    if (kIsWeb) {
      // Initialize FFI for web
      databaseFactory = databaseFactoryFfiWeb;
    }
    
    // Create and return the database
    return await AppDatabase.createDatabase();
  }
}
