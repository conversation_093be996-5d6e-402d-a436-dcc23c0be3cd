import 'package:floor/floor.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite_common/sqlite_api.dart' as db;
/// A safe database factory that works on all platforms including web
class SafeDatabaseFactory {
  /// Get the appropriate database factory for the current platform
  static DatabaseFactory get databaseFactory {
    if (kIsWeb) {
      return databaseFactoryFfiWeb;
    } else {
      return sqfliteDatabaseFactory;
    }
  }
  
  /// Open a database with the given path and options
  static Future<db.Database> openDatabase(String path, {required OpenDatabaseOptions options}) async {
    return await databaseFactory.openDatabase(path, options: options);
  }

  
  /// Get the database path for the given name
  static Future<String> getDatabasePath(String name) async {
    if (kIsWeb) {
      // For web, we use a simple path
      return name;
    } else {
      // For other platforms, use the sqflite database factory
      return await sqfliteDatabaseFactory.getDatabasePath(name);
    }
  }
  
  /// Delete the database at the given path
  static Future<void> deleteDatabase(String path) async {
    await databaseFactory.deleteDatabase(path);
  }
}
