import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';



import '../daos/bet_dao.dart';
import '../daos/fighter_dao.dart';
import '../daos/match_dao.dart';
import 'app_database.dart';
import 'web_database_initializer.dart';

/// A global singleton to manage database access
class DatabaseManager {
  static final DatabaseManager _instance = DatabaseManager._internal();

  factory DatabaseManager() => _instance;

  DatabaseManager._internal();

  AppDatabase? _database;
  bool _isInitializing = false;

  /// Initialize the database
  Future<AppDatabase> initializeDatabase() async {
    if (_database != null) {
      return _database!;
    }

    if (_isInitializing) {
      // Wait for initialization to complete
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      if (_database != null) {
        return _database!;
      }
    }

    _isInitializing = true;

    try {
      // Configure for web platform
      if (kIsWeb) {
        debugPrint('Initializing database for web in DatabaseManager');
        // Initialize web database
        await WebDatabaseInitializer.initialize();
      }

      // Create the database
      _database = await AppDatabase.createDatabase();
      debugPrint('Database initialized successfully');
      return _database!;
    } catch (e) {
      debugPrint('Error initializing database: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// Get the database instance
  Future<AppDatabase> getDatabase() async {
    return await initializeDatabase();
  }

  /// Get the match DAO
  Future<MatchDao> getMatchDao() async {
    final db = await getDatabase();
    return db.matchDao;
  }

  /// Get the fighter DAO
  Future<FighterDao> getFighterDao() async {
    final db = await getDatabase();
    return db.fighterDao;
  }

  /// Get the bet DAO
  Future<BetDao> getBetDao() async {
    final db = await getDatabase();
    return db.betDao;
  }
}

/// Global instance of the database manager
final databaseManager = DatabaseManager();

/// Provider for the database
final databaseProvider = FutureProvider<AppDatabase>((ref) async {
  return await databaseManager.getDatabase();
});

/// Provider for the match DAO
final matchDaoProvider = FutureProvider<MatchDao>((ref) async {
  return await databaseManager.getMatchDao();
});

/// Provider for the fighter DAO
final fighterDaoProvider = FutureProvider<FighterDao>((ref) async {
  return await databaseManager.getFighterDao();
});

/// Provider for the bet DAO
final betDaoProvider = FutureProvider<BetDao>((ref) async {
  return await databaseManager.getBetDao();
});
