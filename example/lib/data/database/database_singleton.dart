import 'package:flutter/foundation.dart';
import 'app_database.dart';
import 'database_helper.dart';

/// A singleton class to hold the database instance
class DatabaseSingleton {
  static DatabaseSingleton? _instance;
  AppDatabase? _database;
  bool _isInitializing = false;
  
  /// Private constructor
  DatabaseSingleton._();
  
  /// Get the singleton instance
  static DatabaseSingleton get instance {
    _instance ??= DatabaseSingleton._();
    return _instance!;
  }
  
  /// Get the database instance
  Future<AppDatabase> getDatabase() async {
    if (_database != null) {
      return _database!;
    }
    
    if (_isInitializing) {
      // Wait for initialization to complete
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      if (_database != null) {
        return _database!;
      }
    }
    
    _isInitializing = true;
    try {
      _database = await DatabaseHelper.initializeDatabase();
      return _database!;
    } catch (e) {
      debugPrint('Error initializing database: $e');
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }
  
  /// Check if the database is initialized
  bool get isInitialized => _database != null;
}
