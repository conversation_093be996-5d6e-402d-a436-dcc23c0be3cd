import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';

/// Class to initialize the database for web
class WebDatabaseInitializer {
  static bool _initialized = false;

  /// Initialize the database for web
  static Future<void> initialize() async {
    if (kIsWeb && !_initialized) {
      try {
        debugPrint('Starting web database initialization');

        // Set the database factory to use the web implementation
        final factory = databaseFactoryFfiWeb;
        databaseFactory = factory;

        // Set the web worker paths
        // This is a workaround for the web worker initialization issue
        // The paths are relative to the web directory
        factory.setDatabasesPath('sqlite3');

        // Verify initialization by opening and closing a test database
        final db = await factory.openDatabase(
          ':memory:',
          options: OpenDatabaseOptions(version: 1),
        );
        await db.close();

        _initialized = true;
        debugPrint('Web database initialized successfully');
      } catch (e) {
        debugPrint('Error initializing web database: $e');
        // Print more detailed error information
        debugPrint('Error details: ${e.toString()}');
        rethrow;
      }
    } else if (_initialized) {
      debugPrint('Web database already initialized');
    }
  }
}
