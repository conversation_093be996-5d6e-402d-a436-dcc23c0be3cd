import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../repositories/match_repository.dart';
import '../repositories/fighter_repository.dart';
import '../repositories/bet_repository.dart';
import 'database_providers.dart';

/// Provider for the MatchRepository
final matchRepositoryProvider = Provider<MatchRepository>((ref) {
  // Initialize the database first
  ref.watch(databaseProvider.selectAsync((db) => db));

  final matchDao = ref.watch(matchDaoProvider);
  return MatchRepository(matchDao);
});

/// Provider for the FighterRepository
final fighterRepositoryProvider = Provider<FighterRepository>((ref) {
  // Initialize the database first
  ref.watch(databaseProvider.selectAsync((db) => db));

  final fighterDao = ref.watch(fighterDaoProvider);
  return FighterRepository(fighterDao);
});

/// Provider for the BetRepository
final betRepositoryProvider = Provider<BetRepository>((ref) {
  // Initialize the database first
  ref.watch(databaseProvider.selectAsync((db) => db));

  final betDao = ref.watch(betDaoProvider);
  return BetRepository(betDao);
});
