import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/match_service.dart';
import '../services/fighter_service.dart';
import '../services/bet_service.dart';
import 'repository_providers.dart';

/// Provider for the MatchService
final matchServiceProvider = Provider<MatchService>((ref) {
  // Database initialization is handled by the repository providers
  final matchRepository = ref.watch(matchRepositoryProvider);
  return MatchService(matchRepository);
});

/// Provider for the FighterService
final fighterServiceProvider = Provider<FighterService>((ref) {
  // Database initialization is handled by the repository providers
  final fighterRepository = ref.watch(fighterRepositoryProvider);
  return FighterService(fighterRepository);
});

/// Provider for the BetService
final betServiceProvider = Provider<BetService>((ref) {
  // Database initialization is handled by the repository providers
  final betRepository = ref.watch(betRepositoryProvider);
  final fighterRepository = ref.watch(fighterRepositoryProvider);
  final matchRepository = ref.watch(matchRepositoryProvider);
  return BetService(betRepository, fighterRepository, matchRepository);
});
