import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../database/app_database.dart';
import '../database/database_singleton.dart';
import '../database/web_database_initializer.dart';
import '../daos/match_dao.dart';
import '../daos/fighter_dao.dart';
import '../daos/bet_dao.dart';

// Global variable to track database initialization
bool _isDatabaseInitialized = false;
AppDatabase? _cachedDatabase;

/// Provider for the database instance
final databaseProvider = FutureProvider<AppDatabase>((ref) async {
  if (_cachedDatabase != null) {
    return _cachedDatabase!;
  }

  try {
    // Initialize web database if running on web
    if (kIsWeb) {
      await WebDatabaseInitializer.initialize();
    }

    // Get the database instance
    final db = await DatabaseSingleton.instance.getDatabase();
    _cachedDatabase = db;
    _isDatabaseInitialized = true;
    debugPrint('Database provider initialized successfully');
    return db;
  } catch (e) {
    debugPrint('Error in database provider: $e');
    rethrow;
  }
});

/// Provider for the MatchDao
final matchDaoProvider = Provider<MatchDao>((ref) {
  final database = ref.watch(databaseProvider).valueOrNull;
  if (database == null) {
    throw Exception('Database not initialized');
  }
  return database.matchDao;
});

/// Provider for the FighterDao
final fighterDaoProvider = Provider<FighterDao>((ref) {
  final database = ref.watch(databaseProvider).valueOrNull;
  if (database == null) {
    throw Exception('Database not initialized');
  }
  return database.fighterDao;
});

/// Provider for the BetDao
final betDaoProvider = Provider<BetDao>((ref) {
  final database = ref.watch(databaseProvider).valueOrNull;
  if (database == null) {
    throw Exception('Database not initialized');
  }
  return database.betDao;
});

/// Function to ensure the database is initialized
Future<void> ensureDatabaseInitialized() async {
  if (!_isDatabaseInitialized) {
    try {
      if (kIsWeb) {
        await WebDatabaseInitializer.initialize();
      }

      _cachedDatabase = await DatabaseSingleton.instance.getDatabase();
      _isDatabaseInitialized = true;
      debugPrint('Database initialized manually');
    } catch (e) {
      debugPrint('Error in ensureDatabaseInitialized: $e');
      rethrow;
    }
  }
}
