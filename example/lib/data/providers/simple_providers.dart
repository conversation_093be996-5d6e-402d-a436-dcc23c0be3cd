import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../database/database_manager.dart';
import '../models/fighter.dart';
import '../repositories/bet_repository.dart';
import '../repositories/fighter_repository.dart';
import '../repositories/match_repository.dart';
import '../services/bet_service.dart';
import '../services/fighter_service.dart';
import '../services/match_service.dart';
import '../services/shared_prefs_service.dart';
import '../models/user.dart';
import '../models/bet.dart';
import '../models/match.dart' as match_model;

/// Provider for the match repository
final matchRepositoryProvider = FutureProvider<MatchRepository>((ref) async {
  final matchDao = await databaseManager.getMatchDao();
  return MatchRepository(matchDao);
});

/// Provider for the fighter repository
final fighterRepositoryProvider = FutureProvider<FighterRepository>((
  ref,
) async {
  final fighterDao = await databaseManager.getFighterDao();
  return FighterRepository(fighterDao);
});

/// Provider for the bet repository
final betRepositoryProvider = FutureProvider<BetRepository>((ref) async {
  final betDao = await databaseManager.getBetDao();
  return BetRepository(betDao);
});

/// Provider for the match service
final matchServiceProvider = FutureProvider<MatchService>((ref) async {
  final matchRepository = await ref.watch(matchRepositoryProvider.future);
  return MatchService(matchRepository);
});

/// Provider for the fighter service
final fighterServiceProvider = FutureProvider<FighterService>((ref) async {
  final fighterRepository = await ref.watch(fighterRepositoryProvider.future);
  return FighterService(fighterRepository);
});

/// Provider for the bet service
final betServiceProvider = FutureProvider<BetService>((ref) async {
  final betRepository = await ref.watch(betRepositoryProvider.future);
  final fighterRepository = await ref.watch(fighterRepositoryProvider.future);
  final matchRepository = await ref.watch(matchRepositoryProvider.future);
  return BetService(betRepository, fighterRepository, matchRepository);
});

/// Provider for the current user
final userProvider = StateProvider<User>((ref) {
  // Return a mock user for now
  return User.mock();
});

/// Provider for the theme mode
final isDarkModeProvider = StateProvider<bool>((ref) {
  final prefsService = ref.watch(sharedPrefsServiceProvider).valueOrNull;
  final savedValue = prefsService?.getBool(PreferenceKeys.isDarkMode);
  return savedValue ?? false;
});

/// Provider for the selected match
final selectedMatchProvider = StateProvider<match_model.Match?>((ref) => null);

/// Provider for the current bets
final currentBetsProvider = AutoDisposeFutureProvider<List<Bet>>((ref) async {
  final selectedMatch = ref.watch(selectedMatchProvider);
  if (selectedMatch == null) {
    return [];
  }

  final betService = await ref.watch(betServiceProvider.future);
  return betService.getBetsByMatchId(selectedMatch.id);
});

/// Provider for voucher filter state
final voucherFilterProvider = AutoDisposeStateProvider<VoucherFilterState>((
  ref,
) {
  return const VoucherFilterState();
});

/// A provider for all fighters
final allFightersProvider = AutoDisposeFutureProvider<List<Fighter>>((
  ref,
) async {
  final fighterService = await ref.watch(fighterServiceProvider.future);
  return fighterService.getAllFighters();
});

/// A provider for fighter details
final fighterDetailsProvider = AutoDisposeFutureProvider.family<Fighter?, int>((
  ref,
  fighterId,
) async {
  final fighterService = await ref.watch(fighterServiceProvider.future);
  return fighterService.getFighterById(fighterId);
});

/// A provider for filtered vouchers
final filteredVouchersProvider = AutoDisposeFutureProvider<List<Bet>>((
  ref,
) async {
  final selectedMatch = ref.watch(selectedMatchProvider);
  if (selectedMatch == null) {
    return [];
  }

  // Get all vouchers for the selected match
  final betService = await ref.watch(betServiceProvider.future);
  final vouchers = await betService.getBetsByMatchId(selectedMatch.id);

  // Get filter state
  final filterState = ref.watch(voucherFilterProvider);

  // Apply fighter filter if needed
  List<Bet> filteredVouchers = vouchers;
  if (filterState.selectedFighterIds.isNotEmpty) {
    filteredVouchers =
        vouchers
            .where(
              (bet) => filterState.selectedFighterIds.contains(bet.fighter),
            )
            .toList();
  }

  // Apply dealer filter if needed
  if (filterState.onlyDealers) {
    final allFighters = await ref.watch(allFightersProvider.future);
    final dealerIds =
        allFighters
            .where((fighter) => fighter.isDealer)
            .map((fighter) => fighter.id)
            .toList();

    filteredVouchers =
        filteredVouchers
            .where((bet) => dealerIds.contains(bet.fighter))
            .toList();
  }

  return filteredVouchers;
});

enum VoucherSortOrder {
  /// Newest first
  newestFirst,

  /// Oldest first
  oldestFirst,
}

/// Filter state for vouchers
class VoucherFilterState {
  /// Sort order
  final VoucherSortOrder sortOrder;

  /// Selected fighter IDs (empty means all)
  final List<int> selectedFighterIds;

  /// Show only dealers
  final bool onlyDealers;

  /// Constructor
  const VoucherFilterState({
    this.sortOrder = VoucherSortOrder.newestFirst,
    this.selectedFighterIds = const [],
    this.onlyDealers = false,
  });

  /// Create a copy with updated values
  VoucherFilterState copyWith({
    VoucherSortOrder? sortOrder,
    List<int>? selectedFighterIds,
    bool? onlyDealers,
  }) {
    return VoucherFilterState(
      sortOrder: sortOrder ?? this.sortOrder,
      selectedFighterIds: selectedFighterIds ?? this.selectedFighterIds,
      onlyDealers: onlyDealers ?? this.onlyDealers,
    );
  }
}

/// Provider for the use built-in keyboard setting
final useBuiltInKeyboardProvider = StateProvider<bool>((ref) {
  final prefsService = ref.watch(sharedPrefsServiceProvider).valueOrNull;
  final savedValue = prefsService?.getBool(PreferenceKeys.useBuiltInKeyboard);
  return savedValue ?? false;
});
