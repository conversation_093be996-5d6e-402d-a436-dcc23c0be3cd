import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/util/util.dart';

import 'simple_providers.dart';

class BetNotifier extends StateNotifier<BetRaw> {
  BetNotifier() : super(BetRaw.empty());

  // Add a single bet
  Future<void> addBet(String number, String amount) async {
    try {
      // Generate the bet (now synchronous)
      final value = generateTwo("$number=$amount");

      // Update state with new bet
      state = state.copyWith(
        total: state.total + value.total,
        bets: [...state.bets, value],
      );
    } catch (e) {
      debugPrint('Error adding bet: $e');
      // You could also add error handling here, such as showing a snackbar
    }
  }

  // Add multiple bets
  Future<void> addBets(BetRaw betRaw) async {
    try {
      state = state.copyWith(
        total: state.total + betRaw.total,
        bets: [...state.bets, ...betRaw.bets],
      );
      log(state.toString());
    } catch (e) {
      debugPrint('Error adding bet: $e');
      // You could also add error handling here, such as showing a snackbar
    }
  }

  // Remove a bet
  void removeBet(RBetModel bet) {
    final index = state.bets.indexOf(bet);
    if (index >= 0) {
      // Create a new list without the bet
      final newBets = List<RBetModel>.from(state.bets);
      newBets.removeAt(index);

      // Update state
      state = state.copyWith(total: state.total - bet.total, bets: newBets);
    }
  }

  Future<void> saveBets(
    WidgetRef ref,
    BuildContext context,
    Fighter fighter,
  ) async {
    // Check for match selection before async operations
    final selectedMatch = ref.read(selectedMatchProvider);
    if (selectedMatch == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Please select a match')));
      }
      return;
    }

    final matchId = selectedMatch.id;
    final fighterId = fighter.id;

    try {
      // Store current state to avoid race conditions
      final currentState = state;

      // Get bet service
      final betService = await ref.read(betServiceProvider.future);

      // Create batch bets
      await betService.createBatchBets(
        numbers: currentState.bets,
        fighterId: fighterId,
        matchId: matchId,
      );

      // Clear bets after successful save
      clearBets();

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Bets saved successfully')),
        );
      }
    } catch (e) {
      debugPrint('Error saving bets: $e');

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving bets: ${e.toString()}')),
        );
      }
    }
  }

  void clearBets() {
    state = BetRaw.empty();
  }
}

final betNotifierProvider = StateNotifierProvider<BetNotifier, BetRaw>((ref) {
  return BetNotifier();
});
