import '../models/bet.dart';
import '../daos/bet_dao.dart';

/// Repository for Bet operations
class BetRepository {
  /// Data access object for bets
  final BetDao _betDao;

  /// Constructor
  BetRepository(this._betDao);

  /// Get all bets
  Future<List<Bet>> getAllBets() async {
    return await _betDao.findAllBets();
  }

  /// Get a bet by id
  Future<Bet?> getBetById(int id) async {
    return await _betDao.findBetById(id);
  }

  /// Get bets by fighter id
  Future<List<Bet>> getBetsByFighter(int fighterId) async {
    return await _betDao.findBetsByFighter(fighterId);
  }

  /// Get bets by match id
  Future<List<Bet>> getBetsByMatch(int matchId) async {
    return await _betDao.findBetsByMatch(matchId);
  }

  /// Get bets by fighter and match
  Future<List<Bet>> getBetsByFighterAndMatch(int fighterId, int matchId) async {
    return await _betDao.findBetsByFighterAndMatch(fighterId, matchId);
  }

  /// Get bets by number
  Future<List<Bet>> getBetsByNumber(String number) async {
    return await _betDao.findBetsByNumber(number);
  }

  /// Save a bet (insert or update)
  Future<void> saveBet(Bet bet) async {
    final existingBet = await _betDao.findBetById(bet.id);
    if (existingBet == null) {
      await _betDao.insertBet(bet);
    } else {
      await _betDao.updateBet(bet);
    }
  }

  /// Save multiple bets
  Future<void> saveBets(List<Bet> bets) async {
    await _betDao.insertBets(bets);
  }

  /// Delete a bet (soft delete)
  Future<void> softDeleteBet(int id, String timestamp) async {
    await _betDao.softDeleteBet(id, timestamp);
  }

  /// Hard delete a bet
  Future<void> deleteBet(Bet bet) async {
    await _betDao.deleteBet(bet);
  }

  /// Get total amount bet on a fighter in a match
  Future<double> getTotalBetAmountByFighterAndMatch(
    int fighterId,
    int matchId,
  ) async {
    final total = await _betDao.getTotalBetAmountByFighterAndMatch(
      fighterId,
      matchId,
    );
    return total ?? 0.0;
  }

  /// Get count of bets on a fighter in a match
  Future<int> getBetCountByFighterAndMatch(int fighterId, int matchId) async {
    final count = await _betDao.getBetCountByFighterAndMatch(
      fighterId,
      matchId,
    );
    return count ?? 0;
  }
  /// Get bets by group timestamp
  /// 
  Future<List<Bet>> getBetsByGroupTimestamp(String groupTimestamp) async {
    return await _betDao.getBetsByGroupTimestamp(groupTimestamp);
  }
}
