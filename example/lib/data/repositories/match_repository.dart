import '../models/match.dart';
import '../daos/match_dao.dart';

/// Repository for Match operations
class MatchRepository {
  /// Data access object for matches
  final MatchDao _matchDao;

  /// Constructor
  MatchRepository(this._matchDao);

  /// Get all matches
  Future<List<Match>> getAllMatches() async {
    return await _matchDao.findAllMatches();
  }

  /// Get a match by id
  Future<Match?> getMatchById(int id) async {
    return await _matchDao.findMatchById(id);
  }

  /// Get last match
  Future<Match?> getLastMatch() async {
    return await _matchDao.findLastMatch();
  }

  /// Get matches by date
  Future<List<Match>> getMatchesByDate(String date) async {
    return await _matchDao.findMatchesByDate(date);
  }

  /// Save a match (insert or update)
  Future<void> saveMatch(Match match) async {
    final existingMatch = await _matchDao.findMatchById(match.id);
    if (existingMatch == null) {
      await _matchDao.insertMatch(match);
    } else {
      await _matchDao.updateMatch(match);
    }
  }

  /// Save multiple matches
  Future<void> saveMatches(List<Match> matches) async {
    await _matchDao.insertMatches(matches);
  }

  /// Delete a match (soft delete)
  Future<void> softDeleteMatch(int id, String timestamp) async {
    await _matchDao.softDeleteMatch(id, timestamp);
  }

  /// Hard delete a match
  Future<void> deleteMatch(Match match) async {
    await _matchDao.deleteMatch(match);
  }
}
