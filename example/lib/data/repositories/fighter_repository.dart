import '../models/fighter.dart';
import '../daos/fighter_dao.dart';

/// Repository for Fighter operations
class FighterRepository {
  /// Data access object for fighters
  final FighterDao _fighterDao;

  /// Constructor
  FighterRepository(this._fighterDao);

  /// Get all fighters
  Future<List<Fighter>> getAllFighters() async {
    return await _fighterDao.findAllFighters();
  }

  /// Get a fighter by id
  Future<Fighter?> getFighterById(int id) async {
    return await _fighterDao.findFighterById(id);
  }

  /// Get fighters by name
  Future<List<Fighter>> getFightersByName(String name) async {
    // Add wildcard for partial name search
    final searchPattern = '%$name%';
    return await _fighterDao.findFightersByName(searchPattern);
  }

  /// Save a fighter (insert or update)
  Future<void> saveFighter(Fighter fighter) async {
    final existingFighter = await _fighterDao.findFighterById(fighter.id);
    if (existingFighter == null) {
      await _fighterDao.insertFighter(fighter);
    } else {
      await _fighterDao.updateFighter(fighter);
    }
  }

  /// Save multiple fighters
  Future<void> saveFighters(List<Fighter> fighters) async {
    await _fighterDao.insertFighters(fighters);
  }

  /// Delete a fighter (soft delete)
  Future<void> softDeleteFighter(int id, String timestamp) async {
    await _fighterDao.softDeleteFighter(id, timestamp);
  }

  /// Hard delete a fighter
  Future<void> deleteFighter(Fighter fighter) async {
    await _fighterDao.deleteFighter(fighter);
  }
}
