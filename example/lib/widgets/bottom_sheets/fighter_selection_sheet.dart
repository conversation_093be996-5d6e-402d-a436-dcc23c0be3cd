import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/fighter.dart';

/// A bottom sheet for selecting a fighter.
class FighterSelectionSheet extends ConsumerWidget {
  final List<Fighter> fighters;
  final Fighter? selectedFighter;
  final Function(Fighter) onFighterSelected;

  const FighterSelectionSheet({
    super.key,
    required this.fighters,
    this.selectedFighter,
    required this.onFighterSelected,
  });

  /// Shows the fighter selection bottom sheet.
  static Future<Fighter?> show({
    required BuildContext context,
    required List<Fighter> fighters,
    Fighter? selectedFighter,
    required Function(Fighter) onFighterSelected,
  }) async {
    return await showModalBottomSheet<Fighter?>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => FighterSelectionSheet(
            fighters: fighters,
            selectedFighter: selectedFighter,
            onFighterSelected: onFighterSelected,
          ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(Icons.person, color: colorScheme.primary, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Select Fighter',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(Icons.close, color: colorScheme.onSurface),
                  onPressed: () => Navigator.of(context).pop(),
                  style: IconButton.styleFrom(
                    backgroundColor: colorScheme.surfaceContainerHighest,
                    foregroundColor: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),

          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.outline.withValues(alpha: 0.2),
          ),

          // Fighter list
          Flexible(
            child:
                fighters.isEmpty
                    ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.person_off,
                              size: 48,
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.5,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No fighters available',
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    : ListView.builder(
                      shrinkWrap: true,
                      itemCount: fighters.length,
                      itemBuilder: (context, index) {
                        final fighter = fighters[index];
                        final isSelected = selectedFighter?.id == fighter.id;

                        return Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? colorScheme.primaryContainer.withValues(
                                      alpha: 0.3,
                                    )
                                    : Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            border:
                                isSelected
                                    ? Border.all(
                                      color: colorScheme.primary,
                                      width: 2,
                                    )
                                    : null,
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            leading: CircleAvatar(
                              backgroundColor:
                                  fighter.isDealer
                                      ? colorScheme.errorContainer
                                      : colorScheme.primaryContainer,
                              child: Icon(
                                fighter.isDealer ? Icons.store : Icons.person,
                                color:
                                    fighter.isDealer
                                        ? colorScheme.error
                                        : colorScheme.primary,
                                size: 20,
                              ),
                            ),
                            title: Text(
                              fighter.name,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight:
                                    fighter.isDealer
                                        ? FontWeight.bold
                                        : FontWeight.w500,
                                color:
                                    fighter.isDealer
                                        ? colorScheme.error
                                        : colorScheme.onSurface,
                              ),
                            ),
                            subtitle:
                                fighter.isDealer
                                    ? Text(
                                      'Dealer',
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(
                                            color: colorScheme.error,
                                            fontWeight: FontWeight.w500,
                                          ),
                                    )
                                    : Text(
                                      'Commission: ${(fighter.commission * 100).toStringAsFixed(1)}%',
                                      style: theme.textTheme.bodySmall
                                          ?.copyWith(
                                            color: colorScheme.onSurface
                                                .withValues(alpha: 0.7),
                                          ),
                                    ),
                            trailing:
                                isSelected
                                    ? Icon(
                                      Icons.check_circle,
                                      color: colorScheme.primary,
                                      size: 24,
                                    )
                                    : Icon(
                                      Icons.radio_button_unchecked,
                                      color: colorScheme.outline,
                                      size: 24,
                                    ),
                            onTap: () {
                              onFighterSelected(fighter);
                              Navigator.of(context).pop(fighter);
                            },
                          ),
                        );
                      },
                    ),
          ),

          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 16),
        ],
      ),
    );
  }
}
