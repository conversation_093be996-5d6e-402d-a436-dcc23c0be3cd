import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/providers/simple_providers.dart';
import 'package:example/util/bet_checker.dart';

import '../../data/providers/bet_notifier.dart';
import '../../util/kconst.dart' as Str;

class BuiltInKeyboard extends ConsumerWidget {
  const BuiltInKeyboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textController = TextEditingController();
    void add(String num, String a) {
      textController.text = num + a;
    }

    bool isContains(String str, String sub) {
      String s = str.split("=")[0].replaceAll(RegExp(r'[ 0-9]'), '').trim();
      return sub == s;
    }

    void checkData(
      String input,
      BuildContext context,
      WidgetRef ref,
      TextEditingController controller,
    ) {
      Map<String, String> replacement = {
        "++": Str.ahpu,
        '**': Str.nakhat,
        '***': <PERSON>r.nyi<PERSON>,
      };

      debugPrint("Checking input: $input");
      if (ref.read(useBuiltInKeyboardProvider)) {
        // replacement.entries.map((e) {
        //   if (isContains(input, e.key)) {
        //     input = input.replaceAll(e.key, e.value);
        //   }
        // });
        String a = input.split("=")[1].replaceAll(RegExp(r'[^0-9]'), "");
        if (isContains(input, '-')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} ${Str.bate}';
          input = num + a;
        } else if (isContains(input, '--')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} ${Str.bk}';
          input = num + a;
        } else if (isContains(input, '++')) {
          input = input.replaceAll('++', Str.ahpu);
        } else if (isContains(input, '**')) {
          input = input.replaceAll('**', Str.nakhat);
        } else if (isContains(input, '***')) {
          input = input.replaceAll('***', Str.nyiko);
        } else if (isContains(input, '----')) {
          input = input.replaceAll('----', Str.mama);
        } else if (isContains(input, '//**')) {
          input = input.replaceAll('//**', Str.badatha);
        } else if (isContains(input, '++++')) {
          input = input.replaceAll('++++', Str.sonema);
        } else if (isContains(input, '--++')) {
          input = input.replaceAll('--++', Str.masone);
        } else if (isContains(input, '++--')) {
          String num = Str.sonema;
          input = input.replaceAll('++--', num);
        } else if (isContains(input, '*')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} ${Str.ahpar}';
          add(num, a);
        } else if (isContains(input, '/')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} ${Str.htate}';
          add(num, a);
        } else if (isContains(input, '/--')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} အ${Str.khway}';
          add(num, a);
        } else if (isContains(input, '/++')) {
          String num =
              '${input.split("=")[0].replaceAll(RegExp("[^0-9]"), "")} ${Str.khwaypu}';
          add(num, a);
        } else if (isContains(input, '//') && input.split('=')[0].length > 3) {
          String s = input.split("=")[0].replaceAll(RegExp("[^//0-9]"), "");
          String num = '${s.split('//')[0]}ကပ်${s.split('//')[1]}=';
          add(num, a);
        }
      }
      // You can validate or process the input here
      if (input.length < 4) return;
      checkBets(
        input,
        context,
        ref,
        onSuccess: (betRaw) {
          final betNotifier = ref.read(betNotifierProvider.notifier);
          log("[BuiltInKeyboard]: ${betRaw.toMap()}");
          betNotifier.addBets(betRaw);
          controller.clear();
        },
      );
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: textController,

        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(12),
          isDense: true,
          hintText: 'Enter bet...',
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
          ),
        ),
        onChanged: (value) {
          // Check if the last character is Enter (\n)
          if (value.isNotEmpty && value.endsWith('\n')) {
            // Remove the newline
            final trimmed = value.trimRight();
            String input = value;
            // Only replace if no '=' already exists
            if (!trimmed.contains('=')) {
              final newValue = '$trimmed=';

              // Update the text field without triggering onChanged again
              textController.value = TextEditingValue(
                text: newValue,
                selection: TextSelection.collapsed(offset: newValue.length),
              );

              // Call check method

              // _checkData(newValue);
            } else {
              input = trimmed;

              // Already has '=', just call check
              checkData(trimmed, context, ref, textController);
            }
          }
        },
        style: Theme.of(context).textTheme.bodyMedium,
        minLines: 1,
        maxLines: 10,
        textAlignVertical: TextAlignVertical.center,
      ),
    );
  }
}
