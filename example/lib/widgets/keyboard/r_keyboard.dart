import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rich_text_controller/rich_text_controller.dart';

import '../../data/providers/bet_notifier.dart';
import '../../theme/app_colors.dart';
import '../../util/bet_checker.dart';

class RKeyboard extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final Function()? onAddPressed;

  const RKeyboard({super.key, required this.controller, this.onAddPressed});

  @override
  ConsumerState<RKeyboard> createState() => _RKeyboardState();
}

class _RKeyboardState extends ConsumerState<RKeyboard> {
  bool _showFormatButtons = false;

  TextEditingController get controller => widget.controller;
  Function()? get onAddPressed => widget.onAddPressed;

  // Get theme-based colors
  Color getNumButtonBg(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode
        ? AppColors.primaryDark.withAlpha(50)
        : AppColors.primaryLight.withAlpha(50);
  }

  Color getNumButtonText(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode ? AppColors.primaryLight : AppColors.primary;
  }

  Color getOpButtonBg(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode ? AppColors.primaryDark : AppColors.primary;
  }

  Color getOpButtonText(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode ? Colors.white : Colors.white;
  }

  Color getAddButtonBg(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode ? AppColors.secondaryDark : AppColors.secondary;
  }

  Color getAddButtonText(BuildContext context) {
    return Colors.white;
  }

  Color getBackspaceBg(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode ? AppColors.error.withAlpha(200) : AppColors.error;
  }

  Color getBackspaceText(BuildContext context) {
    return Colors.white;
  }

  Color getFormatButtonBg(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return isDarkMode
        ? AppColors.primaryDark.withAlpha(200)
        : AppColors.primary;
  }

  Color getFormatButtonText(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return Colors.white;
  }

  void _onKeyTap(String value) {
    HapticFeedback.lightImpact();
    final text = controller.text;
    final selection = controller.selection;

    // List of format values that should have a space after them
    final List<String> formats = [
      'ထိပ်',
      'ဘိတ်',
      'ပါဝါ',
      'ခွေ',
      'ခွေပူး',
      'အပူး',
      'အပါ',
      'ညီကို',
      'နက္ခက်',
      'စုံစုံ',
      'မမ',
      'စုံမ',
      'မစုံ',
      'ဘရိတ်',
      'ပဒေသာ',
      'အကပ်',
    ];

    // Add a space after format values
    String valueToInsert = value;
    if (formats.contains(value)) {
      valueToInsert = '$value ';
    }

    // Fallback: append to the end if selection is invalid
    final isValidSelection = selection.start >= 0 && selection.end >= 0;

    if (!isValidSelection) {
      controller.text += valueToInsert;
      controller.selection = TextSelection.collapsed(
        offset: controller.text.length,
      );
    } else {
      final newText = text.replaceRange(
        selection.start,
        selection.end,
        valueToInsert,
      );
      final newPosition = selection.start + valueToInsert.length;

      controller.text = newText;
      controller.selection = TextSelection.collapsed(offset: newPosition);
    }
  }

  void _onBackspace() {
    final text = controller.text;
    final selection = controller.selection;

    if (selection.start == 0) return;

    final newText = text.replaceRange(selection.start - 1, selection.end, '');
    final newPosition = selection.start - 1;

    controller.text = newText;
    controller.selection = TextSelection.collapsed(offset: newPosition);
  }

  void _showPasteDialog(BuildContext context, WidgetRef ref) async {
    // Get clipboard data
    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);

    // Check if context is still valid after async operation
    if (!context.mounted) return;

    if (data != null && data.text != null) {
      final theme = Theme.of(context);
      final isDarkMode = theme.brightness == Brightness.dark;

      final RichTextController pasteController = RichTextController(
        text: data.text!,
        onMatch: (match) {
          // Use logger instead of print in production
          debugPrint('Match found: $match');
        },
        targetMatches: [
          MatchTargetItem(
            style: TextStyle(
              color: AppColors.secondary,
              fontWeight: FontWeight.bold,
            ),
            regex: RegExp(
              r'(\d+00[^0-9]*r[^0-9]*\d+|\d+50[^0-9]*r\d+|\b\d+(?:50|00)\b(?![a-zA-Z])|\b\d+00\b|\b\d+(?:50|00)\b(?![a-zA-Z]))|r[^0-9]*\d+0|=50r\d{2,}0|\b=50\b',
            ),
          ),
          MatchTargetItem(
            style: TextStyle(
              color: AppColors.error,
              fontWeight: FontWeight.bold,
              fontStyle: FontStyle.italic,
            ),
            regex: RegExp(r'\d{3,}'),
          ),
        ],
      );

      showDialog(
        context: context,
        builder:
            (dialogContext) => Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 8,
              backgroundColor: theme.colorScheme.surface,
              child: Container(
                width: double.infinity,
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.edit_note,
                            color: theme.colorScheme.onPrimary,
                            size: 28,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Edit Pasted Text',
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: theme.colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.close,
                              color: theme.colorScheme.onPrimary,
                            ),
                            onPressed: () => Navigator.pop(dialogContext),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: TextField(
                        controller: pasteController,
                        maxLines: 17,
                        style: theme.textTheme.bodyMedium,
                        decoration: InputDecoration(
                          hintText: 'Edit your pasted text here...',
                          filled: true,
                          fillColor:
                              isDarkMode
                                  ? theme.colorScheme.surface.withAlpha(80)
                                  : theme.colorScheme.surface,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.outline,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: theme.colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),

                    // Actions
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Clear button
                          TextButton.icon(
                            onPressed: () {
                              pasteController.clear();
                            },
                            icon: Icon(
                              Icons.clear,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            label: Text(
                              'Clear',
                              style: theme.textTheme.labelLarge?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 12,
                              ),
                            ),
                          ),
                          // Cancel button
                          OutlinedButton(
                            onPressed: () => Navigator.pop(dialogContext),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: theme.colorScheme.primary,
                              side: BorderSide(
                                color: theme.colorScheme.primary,
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: theme.textTheme.labelLarge,
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Add button
                          ElevatedButton(
                            onPressed: () {
                              // Add the text to the main controller
                              controller.text = pasteController.text;
                              Navigator.pop(dialogContext);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: theme.colorScheme.onPrimary,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Add',
                              style: theme.textTheme.labelLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.colorScheme.onPrimary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
      );
    } else {
      final theme = Theme.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Nothing to paste',
            style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
          ),
          backgroundColor: theme.colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
      // constraints: BoxConstraints(
      //   maxHeight:
      //       MediaQuery.of(context).size.height * 0.50, // 45% of screen height
      // ),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withAlpha(13),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Input display with paste button
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(12),
                    isDense: true,
                    hintText: 'Enter bet...',
                    hintStyle: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(128),
                    ),
                  ),
                  style: theme.textTheme.bodyMedium,
                  maxLines: 3,
                  minLines: 1,
                  readOnly: true,
                  showCursor: true,
                ),
              ),
              Container(
                margin: const EdgeInsets.fromLTRB(4, 6, 4, 8),
                child: SizedBox(
                  height: 54,
                  width: 70,
                  child: ElevatedButton(
                    onPressed: () => _showPasteDialog(context, ref),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: getNumButtonBg(context),
                      foregroundColor: getNumButtonText(context),
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 1,
                    ),
                    child: Icon(
                      Icons.content_paste_outlined,
                      size: 25,
                      color: getNumButtonText(context),
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Number pad and operators
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    Row(
                      children: [
                        _buildNumButton('7'),
                        const SizedBox(width: 4),
                        _buildNumButton('8'),
                        const SizedBox(width: 4),
                        _buildNumButton('9'),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        _buildNumButton('4'),
                        const SizedBox(width: 4),
                        _buildNumButton('5'),
                        const SizedBox(width: 4),
                        _buildNumButton('6'),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        _buildNumButton('1'),
                        const SizedBox(width: 4),
                        _buildNumButton('2'),
                        const SizedBox(width: 4),
                        _buildNumButton('3'),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        _buildNumButton('00'),
                        const SizedBox(width: 4),

                        _buildNumButton('0'),
                        const SizedBox(width: 4),
                        _buildNumButton(','),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    SizedBox(
                      height: 50,
                      width: double.infinity, // Added to match other buttons
                      child: ElevatedButton(
                        onPressed: _onBackspace,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: getBackspaceBg(context),
                          foregroundColor: getBackspaceText(context),
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 1,
                        ),
                        child: Icon(
                          Icons.backspace_outlined,
                          size: 20,
                          color: getBackspaceText(context),
                        ),
                      ),
                    ),
                    const SizedBox(height: 6),
                    _buildOpButton('R'),
                    const SizedBox(height: 6),
                    _buildOpButton('='),
                    const SizedBox(height: 6),
                    // Toggle format buttons
                    SizedBox(
                      height: 50,
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _showFormatButtons = !_showFormatButtons;
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              _showFormatButtons
                                  ? AppColors
                                      .secondary // Green when active
                                  : getNumButtonBg(
                                    context,
                                  ), // Theme color when inactive
                          foregroundColor:
                              _showFormatButtons
                                  ? Colors.white
                                  : getNumButtonText(context),
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 1,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _showFormatButtons ? 'ပိတ်ရန်' : 'ပုံစံများ',
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                color:
                                    _showFormatButtons
                                        ? Colors.white
                                        : getNumButtonText(context),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              _showFormatButtons
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              size: 16,
                              color:
                                  _showFormatButtons
                                      ? Colors.white
                                      : getNumButtonText(context),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 6),

          // Format buttons (conditionally visible)
          if (_showFormatButtons)
            Container(
              height: 50, // Fixed height for the scroll view
              margin: const EdgeInsets.only(bottom: 6),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildFormatButton('ထိပ်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ဘိတ်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ပါဝါ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ဘရိတ်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('အပါ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ခွေ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ခွေပူး'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ကပ်'),
                    const SizedBox(width: 4),

                    _buildFormatButton('မကပ်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('စုံကပ်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('ညီကို'),
                    const SizedBox(width: 4),
                    _buildFormatButton('နက္ခက်'),
                    const SizedBox(width: 4),
                    _buildFormatButton('စုံစုံ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('မမ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('စုံမ'),
                    const SizedBox(width: 4),
                    _buildFormatButton('မစုံ'),

                    const SizedBox(width: 4),
                    _buildFormatButton('ပဒေသာ'),
                  ],
                ),
              ),
            ),

          // Bottom row with useful buttons and Add button
          Row(
            children: [
              // Clear button
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onLongPress: () {
                      controller.clear();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error.withAlpha(50),
                      foregroundColor: AppColors.error,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 1,
                    ),
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Long press to clear',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.white,
                            ),
                          ),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          duration: const Duration(seconds: 2),
                          action: SnackBarAction(
                            label: 'OK',
                            textColor: theme.colorScheme.primary,
                            onPressed: () {},
                          ),
                        ),
                      );
                    },
                    child: Text(
                      'Clear',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 4),

              // Space button
              Expanded(
                flex: 4,
                child: SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () => _onKeyTap('\n'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: getNumButtonBg(context),
                      foregroundColor: getNumButtonText(context),
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 1,
                    ),
                    child: Text(
                      'Enter',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: getNumButtonText(context),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 4),

              // Add button
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed:
                        onAddPressed ??
                        () async {
                          bool result = await checkBets(
                            controller.text,
                            context,
                            ref,
                            onSuccess: (betRaw) {
                              final betNotifier = ref.read(
                                betNotifierProvider.notifier,
                              );
                              log("[R Keyborad]: ${betRaw.toMap()}");

                              betNotifier.addBets(betRaw);

                              // final _ = ref.invalidate(currentBetsProvider);
                              controller.clear();
                            },
                          );
                          if (result) {
                            controller.clear();
                          }
                        },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: getAddButtonBg(context),
                      foregroundColor: getAddButtonText(context),
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 1,
                    ),
                    child: Text(
                      'Add',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: getAddButtonText(context),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNumButton(String value, {int flex = 1}) {
    final theme = Theme.of(context);

    return Expanded(
      flex: flex,
      child: SizedBox(
        height: 50,
        child: ElevatedButton(
          onPressed: () => _onKeyTap(value),
          style: ElevatedButton.styleFrom(
            backgroundColor: getNumButtonBg(context),
            foregroundColor: getNumButtonText(context),
            padding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: theme.colorScheme.outline.withAlpha(50)),
            ),
            elevation: 1,
          ),
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOpButton(String value) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 50,
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _onKeyTap(value),
        style: ElevatedButton.styleFrom(
          backgroundColor: getOpButtonBg(context),
          foregroundColor: getOpButtonText(context),
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 1,
        ),
        child: Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: getOpButtonText(context),
          ),
        ),
      ),
    );
  }

  Widget _buildFormatButton(String value) {
    final theme = Theme.of(context);

    // For horizontal scroll view, we don't want to expand
    return SizedBox(
      height: 46, // Smaller than number buttons for compactness
      // Fixed width for consistent sizing in horizontal scroll
      width: 80,
      child: ElevatedButton(
        onPressed: () => _onKeyTap(value),
        style: ElevatedButton.styleFrom(
          backgroundColor: getFormatButtonBg(context),
          foregroundColor: getFormatButtonText(context),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: theme.colorScheme.outline.withAlpha(50)),
          ),
          elevation: 1,
        ),
        child: Text(
          value,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: getFormatButtonText(context),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
