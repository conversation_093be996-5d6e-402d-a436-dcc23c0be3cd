import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/fighter.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/theme/app_typography.dart';

/// A dialog for selecting a fighter.
class FighterSelectionDialog extends ConsumerWidget {
  final List<Fighter> fighters;
  final Fighter? selectedFighter;
  final Function(Fighter) onFighterSelected;

  const FighterSelectionDialog({
    super.key,
    required this.fighters,
    this.selectedFighter,
    required this.onFighterSelected,
  });

  /// Shows the fighter selection dialog.
  static Future<Fighter?> show({
    required BuildContext context,
    required List<Fighter> fighters,
    Fighter? selectedFighter,
    required Function(Fighter) onFighterSelected,
  }) async {
    return await showDialog<Fighter?>(
      context: context,
      builder:
          (context) => FighterSelectionDialog(
            fighters: fighters,
            selectedFighter: selectedFighter,
            onFighterSelected: onFighterSelected,
          ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_month, color: Colors.white),
                  const SizedBox(width: 12),
                  Text(
                    'Select Fighter',
                    style: AppTypography.amountMedium.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Fighter list
            Flexible(
              child:
                  fighters.isEmpty
                      ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(24.0),
                          child: Text('No fighters available'),
                        ),
                      )
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: fighters.length,
                        itemBuilder: (context, index) {
                          final fighter = fighters[index];
                          final isSelected = selectedFighter?.id == fighter.id;

                          return ListTile(
                            leading: CircleAvatar(
                              backgroundColor:
                                  fighter.isDealer
                                      ? AppColors.error.withOpacity(0.2)
                                      : AppColors.primary.withOpacity(0.2),
                              child: Icon(
                                Icons.person,
                                color:
                                    fighter.isDealer
                                        ? AppColors.error
                                        : AppColors.primary,
                              ),
                            ),
                            title: Text(
                              fighter.name,
                              style: TextStyle(
                                fontWeight:
                                    fighter.isDealer
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                color:
                                    fighter.isDealer ? AppColors.error : null,
                              ),
                            ),
                            subtitle:
                                fighter.isDealer
                                    ? const Text(
                                      'Dealer',
                                      style: TextStyle(color: AppColors.error),
                                    )
                                    : null,
                            trailing:
                                isSelected
                                    ? const Icon(
                                      Icons.check_circle,
                                      color: AppColors.primary,
                                    )
                                    : null,
                            onTap: () {
                              onFighterSelected(fighter);
                              Navigator.of(context).pop(fighter);
                            },
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
