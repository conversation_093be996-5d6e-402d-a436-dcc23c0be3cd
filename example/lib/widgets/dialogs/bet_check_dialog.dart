import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:example/data/models/bet_raw.dart';
import 'package:example/data/providers/bet_notifier.dart';
import 'package:example/theme/app_colors.dart';
import 'package:example/theme/app_typography.dart';
import 'package:example/util/format_utils.dart';

/// A dialog for checking bets before confirming them.
/// Shows both the list of bets and any errors that occurred.
class BetCheckDialog extends ConsumerStatefulWidget {
  /// The raw bet data to display
  final BetRaw betRaw;

  /// Callback when the dialog is closed
  final VoidCallback? onClose;

  /// List of errors to display (if any)
  final List<String> errors;

  const BetCheckDialog({
    super.key,
    required this.betRaw,
    this.onClose,
    this.errors = const [],
  });

  /// Shows the bet check dialog
  static Future<bool?> show({
    required BuildContext context,
    required BetRaw betRaw,
    List<String> errors = const [],
    VoidCallback? onClose,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) =>
              BetCheckDialog(betRaw: betRaw, errors: errors, onClose: onClose),
    );
  }

  @override
  ConsumerState<BetCheckDialog> createState() => _BetCheckDialogState();
}

class _BetCheckDialogState extends ConsumerState<BetCheckDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.errors.isEmpty ? 0 : 1,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hasErrors = widget.errors.isNotEmpty;
    final totalBets = widget.betRaw.bets.fold(
      0,
      (total, bet) => total + bet.bets.length,
    );

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                hasErrors
                    ? Icons.warning_amber_rounded
                    : Icons.check_circle_outline,
                color: hasErrors ? AppColors.warning : AppColors.success,
                size: 28,
              ),
              const SizedBox(width: 8),
              Text(
                hasErrors ? 'Check Errors' : 'Confirm Bets',
                style: AppTypography.headline4,
              ),
            ],
          ),
          const SizedBox(height: 16),
          TabBar(
            controller: _tabController,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.list_alt),
                    const SizedBox(width: 8),
                    Text('Bets (${totalBets})'),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline),
                    const SizedBox(width: 8),
                    Text('Errors (${widget.errors.length})'),
                  ],
                ),
              ),
            ],
            indicatorColor: Theme.of(context).colorScheme.primary,
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Colors.grey,
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.5,
        child: TabBarView(
          controller: _tabController,
          children: [_buildBetsTab(), _buildErrorsTab()],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(false);
            if (widget.onClose != null) widget.onClose!();
          },
          child: Text(
            'Cancel',
            style: AppTypography.button.copyWith(
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        if (!hasErrors)
          ElevatedButton(
            onPressed:
                _isSubmitting
                    ? null
                    : () async {
                      setState(() {
                        _isSubmitting = true;
                      });

                      try {
                        // Add each bet individually to trigger animations properly
                        final betNotifier = ref.read(
                          betNotifierProvider.notifier,
                        );

                        betNotifier.addBets(widget.betRaw);

                        if (mounted && context.mounted) {
                          Navigator.of(context).pop(true);
                          if (widget.onClose != null) widget.onClose!();
                        }
                      } catch (e) {
                        setState(() {
                          _isSubmitting = false;
                        });

                        if (mounted && context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Error: ${e.toString()}')),
                          );
                        }
                      }
                    },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              disabledBackgroundColor: Colors.grey,
            ),
            child:
                _isSubmitting
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Text('Confirm'),
          ),
      ],
    );
  }

  Widget _buildBetsTab() {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: widget.betRaw.bets.length,
            itemBuilder: (context, index) {
              final bet = widget.betRaw.bets[index];
              return _buildBetGroup(bet);
            },
          ),
        ),
        const Divider(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Amount:',
                style: AppTypography.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                widget.betRaw.total.toString(),
                style: AppTypography.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBetGroup(RBetModel bet) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ExpansionTile(
        title: Row(
          children: [
            Text(
              bet.number,
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Text(bet.amount, style: AppTypography.bodyMedium),
          ],
        ),
        subtitle: Text(
          '${bet.bets.length} bets, total: ${FormatUtils.formatAmount(bet.total)}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  bet.bets.map((individualBet) {
                    return Chip(
                      label: Text(
                        individualBet.number,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      labelPadding: const EdgeInsets.symmetric(horizontal: 4),
                      padding: EdgeInsets.zero,
                      backgroundColor: Colors.grey[200],
                      avatar: Container(
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        child: Text(
                          individualBet.amount < 1000
                              ? '${individualBet.amount}'
                              : FormatUtils.formatAmount(individualBet.amount),
                          style: const TextStyle(
                            fontSize: 9,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorsTab() {
    if (widget.errors.isEmpty) {
      return const Center(child: Text('No errors found'));
    }

    return ListView.builder(
      itemCount: widget.errors.length,
      itemBuilder: (context, index) {
        final error = widget.errors[index];
        return ListTile(
          leading: const Icon(Icons.error_outline, color: AppColors.error),
          title: Text(error, style: AppTypography.bodyMedium),
        );
      },
    );
  }
}
