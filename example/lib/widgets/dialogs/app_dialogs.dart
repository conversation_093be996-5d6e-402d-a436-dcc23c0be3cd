import 'package:flutter/material.dart';
import '../../data/models/fighter.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_typography.dart';
import '../bottom_sheets/fighter_selection_sheet.dart';
import 'fighter_selection_dialog.dart';

/// A collection of reusable dialog components for the ledger application.
class AppDialogs {
  /// Shows a confirmation dialog with customizable content.
  ///
  /// [context] - The build context.
  /// [title] - The title of the dialog.
  /// [message] - The message to display in the dialog.
  /// [confirmText] - The text for the confirm button (defaults to "Confirm").
  /// [cancelText] - The text for the cancel button (defaults to "Cancel").
  /// [onConfirm] - Callback function when the confirm button is pressed.
  /// [onCancel] - Callback function when the cancel button is pressed.
  static Future<bool?> showConfirmDialog({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(message, style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
                if (onCancel != null) onCancel();
              },
              child: Text(
                cancelText,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                if (onConfirm != null) onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  /// Shows a warning dialog with customizable content.
  ///
  /// [context] - The build context.
  /// [title] - The title of the dialog.
  /// [message] - The message to display in the dialog.
  /// [confirmText] - The text for the confirm button (defaults to "OK").
  /// [cancelText] - The text for the cancel button (defaults to "Cancel").
  /// [onConfirm] - Callback function when the confirm button is pressed.
  /// [onCancel] - Callback function when the cancel button is pressed.
  static Future<bool?> showWarningDialog({
    required BuildContext context,
    required String title,
    required String message,
    String confirmText = 'OK',
    String cancelText = 'Cancel',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: AppColors.warning,
                size: 28,
              ),
              const SizedBox(width: 8),
              Text(title, style: AppTypography.headline4),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text(message, style: AppTypography.bodyMedium)],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
                if (onCancel != null) onCancel();
              },
              child: Text(
                cancelText,
                style: AppTypography.button.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                if (onConfirm != null) onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: Colors.black,
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  /// Shows an error dialog with customizable content.
  ///
  /// [context] - The build context.
  /// [title] - The title of the dialog.
  /// [message] - The message to display in the dialog.
  /// [buttonText] - The text for the button (defaults to "OK").
  /// [onPressed] - Callback function when the button is pressed.
  static Future<void> showErrorDialog({
    required BuildContext context,
    required String title,
    required String message,
    String buttonText = 'OK',
    VoidCallback? onPressed,
  }) async {
    return await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.error_outline, color: AppColors.error, size: 28),
              const SizedBox(width: 8),
              Text(title, style: AppTypography.headline4),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text(message, style: AppTypography.bodyMedium)],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onPressed != null) onPressed();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
              ),
              child: Text(buttonText),
            ),
          ],
        );
      },
    );
  }

  /// Shows an information dialog with customizable content.
  ///
  /// [context] - The build context.
  /// [title] - The title of the dialog.
  /// [message] - The message to display in the dialog.
  /// [buttonText] - The text for the button (defaults to "OK").
  /// [onPressed] - Callback function when the button is pressed.
  static Future<void> showInfoDialog({
    required BuildContext context,
    required String title,
    required String message,
    String buttonText = 'OK',
    VoidCallback? onPressed,
  }) async {
    return await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.info_outline, color: AppColors.info, size: 28),
              const SizedBox(width: 8),
              Text(title, style: AppTypography.headline4),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text(message, style: AppTypography.bodyMedium)],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onPressed != null) onPressed();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.info,
                foregroundColor: Colors.white,
              ),
              child: Text(buttonText),
            ),
          ],
        );
      },
    );
  }

  /// Shows a fighter selection dialog.
  ///
  /// [context] - The build context.
  /// [fighters] - The list of fighters to choose from.
  /// [selectedFighter] - The currently selected fighter (if any).
  /// [onFighterSelected] - Callback function when a fighter is selected.
  static Future<void> showFighterSelectionDialog({
    required BuildContext context,
    required List<Fighter> fighters,
    Fighter? selectedFighter,
    required Function(Fighter) onFighterSelected,
  }) async {
    // Import the fighter model and dialog
    await FighterSelectionDialog.show(
      context: context,
      fighters: fighters,
      selectedFighter: selectedFighter,
      onFighterSelected: onFighterSelected,
    );
  }

  /// Shows a fighter selection bottom sheet.
  ///
  /// [context] - The build context.
  /// [fighters] - The list of fighters to choose from.
  /// [selectedFighter] - The currently selected fighter (if any).
  /// [onFighterSelected] - Callback function when a fighter is selected.
  static Future<void> showFighterSelectionBottomSheet({
    required BuildContext context,
    required List<Fighter> fighters,
    Fighter? selectedFighter,
    required Function(Fighter) onFighterSelected,
  }) async {
    // Import the fighter model and bottom sheet
    await FighterSelectionSheet.show(
      context: context,
      fighters: fighters,
      selectedFighter: selectedFighter,
      onFighterSelected: onFighterSelected,
    );
  }
}
