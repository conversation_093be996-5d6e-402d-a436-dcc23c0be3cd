import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_colors.dart';

/// A custom scaffold for the ledger application.
/// Provides a consistent layout structure across the app.
class AppScaffold extends StatelessWidget {
  /// The title displayed in the app bar.
  final String? title;

  /// The body of the scaffold.
  final Widget body;

  /// Optional leading widget for the app bar.
  final Widget? leading;

  /// Optional list of actions for the app bar.
  final List<Widget>? actions;

  /// Optional bottom navigation bar.
  final Widget? bottomNavigationBar;

  /// Optional floating action button.
  final Widget? floatingActionButton;

  /// Optional floating action button location.
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  /// Whether to show the back button in the app bar.
  final bool showBackButton;

  /// Optional drawer widget.
  final Widget? drawer;

  /// Optional bottom sheet.
  final Widget? bottomSheet;

  /// Optional app bar elevation.
  final double? appBarElevation;

  /// Whether to resize the body to avoid the bottom inset.
  final bool resizeToAvoidBottomInset;

  /// Optional background color for the scaffold.
  final Color? backgroundColor;

  /// Optional callback when the back button is pressed.
  final VoidCallback? onBackPressed;

  /// Optional app bar bottom widget.
  final PreferredSizeWidget? appBarBottom;

  /// Whether the app bar should be pinned.
  final bool isPinned;

  /// Whether to center the title in the app bar.
  final bool centerTitle;

  /// Whether to use a sliver app bar.
  final bool useSliverAppBar;

  /// Optional status bar brightness.
  final Brightness? statusBarBrightness;

  const AppScaffold({
    Key? key,
    this.title,
    required this.body,
    this.leading,
    this.actions,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.showBackButton = true,
    this.drawer,
    this.bottomSheet,
    this.appBarElevation,
    this.resizeToAvoidBottomInset = true,
    this.backgroundColor,
    this.onBackPressed,
    this.appBarBottom,
    this.isPinned = true,
    this.centerTitle = true,
    this.useSliverAppBar = false,
    this.statusBarBrightness,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Determine status bar brightness
    final effectiveStatusBarBrightness =
        statusBarBrightness ??
        (isDarkMode ? Brightness.dark : Brightness.light);

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            effectiveStatusBarBrightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark,
        systemNavigationBarColor:
            isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
        systemNavigationBarIconBrightness:
            isDarkMode ? Brightness.light : Brightness.dark,
      ),
    );

    // Create app bar
    final appBar = AppBar(
      title: Text(title ?? ""),
      centerTitle: centerTitle,
      elevation: appBarElevation,
      leading:
          showBackButton && Navigator.of(context).canPop()
              ? leading ??
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed:
                        onBackPressed ?? () => Navigator.of(context).pop(),
                  )
              : leading,
      actions: actions,
      bottom: appBarBottom,
    );

    // Create sliver app bar if needed
    final sliverAppBar = SliverAppBar(
      title: Text(title ?? ''),
      centerTitle: centerTitle,
      elevation: appBarElevation,
      leading:
          showBackButton && Navigator.of(context).canPop()
              ? leading ??
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed:
                        onBackPressed ?? () => Navigator.of(context).pop(),
                  )
              : leading,
      actions: actions,
      bottom: appBarBottom,
      floating: !isPinned,
      pinned: isPinned,
    );

    // Return scaffold with appropriate configuration
    return Scaffold(
      appBar: title == null ? null : (useSliverAppBar ? null : appBar),
      body:
          useSliverAppBar
              ? CustomScrollView(
                slivers: [sliverAppBar, SliverToBoxAdapter(child: body)],
              )
              : body,
      drawer: drawer,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomSheet: bottomSheet,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      backgroundColor: backgroundColor ?? theme.scaffoldBackgroundColor,
    );
  }
}

/// A version of AppScaffold that uses a SafeArea widget to ensure content
/// doesn't overlap with system UI elements.
class SafeAppScaffold extends StatelessWidget {
  /// The AppScaffold to wrap with SafeArea.
  final AppScaffold scaffold;

  /// Whether to avoid the top system UI elements.
  final bool top;

  /// Whether to avoid the bottom system UI elements.
  final bool bottom;

  /// Whether to avoid the left system UI elements.
  final bool left;

  /// Whether to avoid the right system UI elements.
  final bool right;

  const SafeAppScaffold({
    Key? key,
    required this.scaffold,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: scaffold.title,
      leading: scaffold.leading,
      actions: scaffold.actions,
      bottomNavigationBar: scaffold.bottomNavigationBar,
      floatingActionButton: scaffold.floatingActionButton,
      floatingActionButtonLocation: scaffold.floatingActionButtonLocation,
      showBackButton: scaffold.showBackButton,
      drawer: scaffold.drawer,
      bottomSheet: scaffold.bottomSheet,
      appBarElevation: scaffold.appBarElevation,
      resizeToAvoidBottomInset: scaffold.resizeToAvoidBottomInset,
      backgroundColor: scaffold.backgroundColor,
      onBackPressed: scaffold.onBackPressed,
      appBarBottom: scaffold.appBarBottom,
      isPinned: scaffold.isPinned,
      centerTitle: scaffold.centerTitle,
      useSliverAppBar: scaffold.useSliverAppBar,
      statusBarBrightness: scaffold.statusBarBrightness,
      body: SafeArea(
        top: top,
        bottom: bottom,
        left: left,
        right: right,
        child: scaffold.body,
      ),
    );
  }
}
