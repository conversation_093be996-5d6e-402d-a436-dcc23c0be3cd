import 'package:example/data/models/bet_raw.dart';

import '../util/kconst.dart';
import '../util/util.dart';

// Define a result class to hold the processed data
class ProcessingResult {
  final List<String> successList;
  final List<String> errorList;
  final List<String> closedNumbers;
  final bool hasErrors;

  ProcessingResult({
    required this.successList,
    required this.errorList,
    required this.closedNumbers,
    required this.hasErrors,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': successList,
      'errors': errorList,
      'closedNumbers': closedNumbers,
      'hasErrors': hasErrors,
      'successCount': successList.length,
      'errorCount': errorList.length,
    };
  }
}

String getFixed(String input) {
  try {
    // Split the input into lines
    List<String> lines =
        input
            .toLowerCase()
            .split('\n')
            .map((line) => line.trim())
            .where((line) => line.isNotEmpty)
            .toList();

    // List to store the transformed lines
    List<String> transformedLines = [];
    String? lastSuffix;

    // Iterate through the lines
    for (String line in lines) {
      print(line);
      // If the line contains non-numeric characters (excluding 'r'), skip processing
      if (RegExp(r'[^\d]').hasMatch(line) &&
          line.replaceAll(' ', '').length != 2) {
        transformedLines.add(line);
        lastSuffix = line.split('=').last;
        continue;
      }
      transformedLines.add("$line=>$lastSuffix");
    }

    return transformedLines.join('\n');
  } catch (e) {
    throw Exception('Error in getFixed: ${e.toString()}');
  }
}

String replaceString(String str) {
  try {
    // Create a map for all replacements
    str = str.toLowerCase();

    replacement.forEach((key, value) {
      str = str.replaceAll(key, value);
    });

    RegExp regex = RegExp(r'_{5,}|-{5,}|total.*$|time.*$|du(?![a-z])');
    // Replace all matches with an empty string
    str = str.replaceAllMapped(regex, (match) => '');

    return str.toLowerCase();
  } catch (e) {
    throw Exception('Error in replaceString: ${e.toString()}');
  }
}

String fixAmount(String input) {
  try {
    RegExp regExp = RegExp(r'([^\d]\d{2,}r\d{2,}[^\d])');
    Iterable<Match> matches = regExp.allMatches(input);
    List<String> numbers = input.split(regExp);
    List<String> transformedLines = [];

    for (int i = 0; i < numbers.length; i++) {
      if (i < matches.length) {
        transformedLines.add(
          '${numbers[i].trim()} ${matches.elementAt(i).group(0)}',
        );
      } else {
        transformedLines.add(numbers[i].trim());
      }
    }

    return transformedLines.join('\n');
  } catch (e) {
    throw Exception('Error in fixAmount: ${e.toString()}');
  }
}

Future<ProcessingResult> processText(String str) async {
  List<String> errorList = [];
  List<String> numList = [];
  List<String> closedNumbers = [];

  try {
    // Validate input
    if (str.trim().isEmpty) {
      throw ArgumentError("Input string cannot be empty");
    }

    String diffSplit = '\n';

    // Pre-processing
    str = str.replaceAll('bk', bk);
    str = str.replaceAll('Du11', '');
    str = str.replaceAll('du11', '');

    RegExp regExp = RegExp(r'\[ [^\n]*:');
    str = str.replaceAll(regExp, '').trim();

    str = replaceString(str);
    RegExp regex = RegExp('[a-qst-zA-QST-Z]');
    str = str.replaceAll(regex, '');

    // Pattern matching and replacement
    str = str.replaceAllMapped(
      RegExp(r'(?<!\d)\d{2}([^\d])?r([^\d])?(?!50)\d{2}(?!\d)'),
      (match) {
        return match.group(0)!.replaceFirst('r', ',');
      },
    );

    str = str.replaceAllMapped(
      RegExp(
        r'(?:\d{1,}(00|50)(?:r\d{2,})?|r\d{2,})|([\u1000-\u109F]+\d{2,}r?(\d{2,})?)$',
      ),
      ((match) {
        return '=${match.group(0)}\n';
      }),
    );

    String str2 = '';
    for (String e in str.split('\n')) {
      if (!e.endsWith('0') && e.length > 2) {
        str2 += "$e,";
      } else {
        str2 += "$e\n";
      }
    }

    str = getFixed(str2);

    // Main processing logic
    RegExp reg = RegExp(r'[\.\=\-\,\s/\*]');
    RegExp onlyNum = RegExp(r'[^0-9]');
    str = replaceString(str);

    for (String s in str.split(diffSplit)) {
      try {
        RegExp nonAlphanumericAtEnd = RegExp(r'[^\d]+$');
        s = s.replaceFirst(nonAlphanumericAtEnd, '');

        String nums = '';
        String amount = '';
        String error = '';

        // Process different betting patterns
        if (s.contains(ahpu)) {
          if (s.replaceAll(onlyNum, '').isNotEmpty) {
            numList.add('$ahpu ${s.replaceAll(onlyNum, '')}');
          }
        } else if ((s.contains('p') || s.contains(ahpar)) &&
            !s.contains('ဝါ')) {
          if (s.replaceAll(onlyNum, '').isNotEmpty) {
            numList.add(
              '${s.split('ပါ').first.replaceAll(onlyNum, '')} အပါ ${s.split('ပါ').last.replaceAll(onlyNum, '')}',
            );
          }
        } else if (s.contains(bk)) {
          if (s.replaceAll(onlyNum, '').isNotEmpty) {
            numList.add(
              '${s.split(bk).first.replaceAll(onlyNum, '')} $bk ${s.split(bk).last.replaceAll(onlyNum, '')}',
            );
          }
        } else if (s.contains('နခ') || s.contains('nk') || s.contains(nakhat)) {
          numList.add('$nakhat ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(nyiko)) {
          numList.add('$nyiko ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(mama)) {
          numList.add('$mama ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(sonema)) {
          numList.add('$sonema ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(masone)) {
          numList.add('$masone ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(badatha)) {
          numList.add('$badatha ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(sonesone)) {
          numList.add('$sonesone ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains('power') || s.contains(power)) {
          numList.add('$power ${s.replaceAll(onlyNum, '')}');
        } else if (s.contains(htate) &&
            !s.contains('ထိပ်$bate') &&
            !s.contains('ထိပ်ပိတ်')) {
          _processHtate(s, htate, bate, onlyNum, numList);
        } else if ((s.contains(bate) || s.contains('ပိတ်')) &&
            !s.contains('ထိပ်$bate') &&
            !s.contains('ထိပ်ပိတ်')) {
          s = s.replaceAll('ပိတ်', bate);
          numList.add(
            '${s.split(bate)[0].replaceAll(onlyNum, '')} $bate ${s.split(bate)[1].replaceAll(onlyNum, '')}',
          );
        } else if (s.contains('ထိပ်$bate') || s.contains('ထိပ်ပိတ်')) {
          _processTopBate(s, htate, bate, onlyNum, numList);
        } else if (s.contains(khway) && !s.contains(khwaypu)) {
          numList.add(
            "${s.split(khway)[0].replaceAll(onlyNum, '')} အ$khway ${s.split(khway)[1].replaceAll(onlyNum, '')}",
          );
        } else if (s.contains(khwaypu)) {
          numList.add(
            "${s.split(khwaypu)[0].replaceAll(onlyNum, '')} $khwaypu ${s.split(khwaypu)[1].replaceAll(onlyNum, '')}",
          );
        } else if (s.contains('ကပ်')) {
          _processKat(s, onlyNum, numList);
        } else {
          _processOther(s, reg, onlyNum, numList, errorList);
        }

        if (error.isNotEmpty) {
          errorList.add(error);
        }
      } catch (e) {
        errorList.add('Error processing line "$s": ${e.toString()}');
      }
    }

    // Process closed numbers
    await _processClosedNumbers(numList, closedNumbers);

    // Clean up results
    List<String> cleanedNumList =
        numList
            .map((s) => s.replaceAll(",", "."))
            .where((s) => s.trim().isNotEmpty)
            .toList();

    List<String> cleanedErrorList =
        errorList
            .map((s) => s.replaceAll(",", "."))
            .where((s) => s.trim().isNotEmpty)
            .toList();

    return ProcessingResult(
      successList: cleanedNumList,
      errorList: cleanedErrorList,
      closedNumbers: closedNumbers,
      hasErrors: cleanedErrorList.isNotEmpty,
    );
  } catch (e) {
    // Return error result if processing fails
    return ProcessingResult(
      successList: [],
      errorList: ['Processing failed: ${e.toString()}'],
      closedNumbers: [],
      hasErrors: true,
    );
  }
}

// Helper methods to break down complex logic
void _processHtate(
  String s,
  String htate,
  String bate,
  RegExp onlyNum,
  List<String> numList,
) {
  if (s.split(htate)[1].contains('r')) {
    String am1 = s.split(htate)[1].split('r')[0].replaceAll(onlyNum, '');
    String am2 = s.split(htate)[1].split('r')[1].replaceAll(onlyNum, '');
    numList.add(
      '${s.split(htate)[0].replaceAll(onlyNum, '')} $htate ${am1.isEmpty ? am2 : am1}',
    );
    numList.add('${s.split(htate)[0].replaceAll(onlyNum, '')} $bate $am2');
  } else if (s.split(htate)[1].contains(bate)) {
    String am1 = s.split(htate)[1].split(bate)[0].replaceAll(onlyNum, '');
    String am2 = s.split(htate)[1].split(bate)[1].replaceAll(onlyNum, '');
    numList.add(
      '${s.split(htate)[0].replaceAll(onlyNum, '')} $htate ${am1.isEmpty ? am2 : am1}',
    );
    numList.add('${s.split(htate)[0].replaceAll(onlyNum, '')} $bate $am2');
  } else {
    numList.add(
      '${s.split(htate)[0].replaceAll(onlyNum, '')} $htate ${s.split(htate)[1].replaceAll(onlyNum, '')}',
    );
  }
}

void _processTopBate(
  String s,
  String htate,
  String bate,
  RegExp onlyNum,
  List<String> numList,
) {
  numList.add(
    '${s.split(htate)[0].replaceAll(onlyNum, '')} $htate ${s.split(htate)[1].replaceAll(onlyNum, '')}',
  );
  s = s.replaceAll('ပိတ်', bate);
  numList.add(
    '${s.split(bate)[0].replaceAll(onlyNum, '')} $bate ${s.split(bate)[1].replaceAll(onlyNum, '')}',
  );
}

void _processKat(String s, RegExp onlyNum, List<String> numList) {
  if (s.contains('=')) {
    String a = s.split('=')[0];
    String ko = a.split('ကို')[0].replaceAll(onlyNum, '');
    String kat = a.split('ကို')[1].replaceAll(onlyNum, '');
    String amount = s.split('=')[1];
    String fm = "$koကို$katကပ် $amount";
    numList.add(fm);
  }
}

void _processOther(
  String s,
  RegExp reg,
  RegExp onlyNum,
  List<String> numList,
  List<String> errorList,
) {
  if (s.contains(reg) && s.replaceAll(onlyNum, '').length > 3) {
    List<String> parts =
        s.split(RegExp(r'[^r0-9]')).where((part) {
          return part.isNotEmpty;
        }).toList();

    String nums = '';
    String error = '';

    for (int i = 0; i < parts.length - 1; i++) {
      if (parts[i].replaceAll(onlyNum, '').length == 2) {
        nums += '${parts[i]},';
      } else {
        error += '$s=>${parts[i]},';
      }
    }

    String amount =
        parts.isNotEmpty ? parts.last.replaceAll(RegExp(r'[^r0-9]'), '') : '';

    if (nums.isNotEmpty && amount.isNotEmpty) {
      nums = nums.substring(0, nums.length - 1);
      numList.add("$nums $amount");
    } else {
      errorList.add(error.isNotEmpty ? error : s);
    }
  } else {
    if (s.contains('r') && s.replaceAll(onlyNum, '').length > 2) {
      List<String> rParts = s.split('r');
      if (rParts.length >= 2 && rParts[0].length == 2 && rParts[1].isNotEmpty) {
        numList.add('${rParts[0]} r${rParts.last}');
      } else {
        errorList.add(s);
      }
    } else {
      if (s.replaceAll(onlyNum, '').length > 2) {
        errorList.add(s);
      }
    }
  }
}

Future<void> _processClosedNumbers(
  List<String> numList,
  List<String> closedNumbers,
) async {
  try {
    print(numList);
   var res= multiGenerateTwo(numList.map((e) => e.replaceAll(' ', '=')).join('\n'));
   print(res.total);
    await Future.forEach(numList, (String number) async {
      try {
        String nnn = number.split(' ')[0];
        if (closedNumbers.isNotEmpty) {
          RBetModel n = generateTwo('$nnn=100');
          List<String> nums = n.bets.map((e) => e.number).toList();
          for (String element in nums) {
            if (closedNumbers.contains(element)) {
              closedNumbers.add("$number=>$element!!");
            }
          }
        }
      } catch (e) {
        print('Error processing closed number for: $number - ${e.toString()}');
      }
    });
  } catch (e) {
    print('Error in _processClosedNumbers: ${e.toString()}');
  }
}

// Usage example:
Future<void> example() async {
  try {
    String inputText = "Your betting text here...";
    ProcessingResult result = await processText(inputText);

    if (result.hasErrors) {
      print('Processing completed with errors:');
      print('Errors: ${result.errorList}');
    } else {
      print('Processing completed successfully!');
    }

    print('Success count: ${result.successList.length}');
    print('Success data: ${result.successList}');
    print('Closed numbers: ${result.closedNumbers}');

    // Convert to JSON if needed
    Map<String, dynamic> jsonResult = result.toJson();
    print('JSON Result: $jsonResult');
  } catch (e) {
    print('Failed to process text: ${e.toString()}');
  }
}
