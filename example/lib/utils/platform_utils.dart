import 'package:flutter/foundation.dart';
    import 'dart:io' as io;

/// Utility class for platform-specific operations
class PlatformUtils {
  /// Check if the app is running on Android
  static bool get isAndroid {
    // For web, we can't use Platform.isAndroid
    if (kIsWeb) {
      return false;
    }
    
    // For non-web platforms, we can use dart:io
    try {
      // Use dynamic import to avoid issues with web
      return _isAndroidPlatform();
    } catch (e) {
      // If there's an error, assume it's not Android
      return false;
    }
  }
  
  /// Check if the app is running on iOS
  static bool get isIOS {
    // For web, we can't use Platform.isIOS
    if (kIsWeb) {
      return false;
    }
    
    // For non-web platforms, we can use dart:io
    try {
      return _isIOSPlatform();
    } catch (e) {
      // If there's an error, assume it's not iOS
      return false;
    }
  }
  
  /// Check if the app is running on macOS
  static bool get isMacOS {
    // For web, we can't use Platform.isMacOS
    if (kIsWeb) {
      return false;
    }
    
    // For non-web platforms, we can use dart:io
    try {
      return _isMacOSPlatform();
    } catch (e) {
      // If there's an error, assume it's not macOS
      return false;
    }
  }
  
  /// Check if the app is running on Windows
  static bool get isWindows {
    // For web, we can't use Platform.isWindows
    if (kIsWeb) {
      return false;
    }
    
    // For non-web platforms, we can use dart:io
    try {
      return _isWindowsPlatform();
    } catch (e) {
      // If there's an error, assume it's not Windows
      return false;
    }
  }
  
  /// Check if the app is running on Linux
  static bool get isLinux {
    // For web, we can't use Platform.isLinux
    if (kIsWeb) {
      return false;
    }
    
    // For non-web platforms, we can use dart:io
    try {
      return _isLinuxPlatform();
    } catch (e) {
      // If there's an error, assume it's not Linux
      return false;
    }
  }
  
  /// Private method to check Android platform
  static bool _isAndroidPlatform() {
    // This import is only used in this method
    // to avoid issues with web compilation

    return io.Platform.isAndroid;
  }
  
  /// Private method to check iOS platform
  static bool _isIOSPlatform() {
    return io.Platform.isIOS;
  }
  
  /// Private method to check macOS platform
  static bool _isMacOSPlatform() {

    return io.Platform.isMacOS;
  }
  
  /// Private method to check Windows platform
  static bool _isWindowsPlatform() {

    return io.Platform.isWindows;
  }
  
  /// Private method to check Linux platform
  static bool _isLinuxPlatform() {

    return io.Platform.isLinux;
  }
}
