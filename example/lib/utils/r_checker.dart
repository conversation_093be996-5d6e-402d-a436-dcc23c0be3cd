import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'fix.dart';

// Result data class (assuming this is from your previous code)

void rChecker(BuildContext context, WidgetRef ref, ProcessingResult result) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return RCheckerDialog(result: result);
    },
  );
}

class RCheckerDialog extends ConsumerStatefulWidget {
  final ProcessingResult result;

  const RCheckerDialog({Key? key, required this.result}) : super(key: key);

  @override
  ConsumerState<RCheckerDialog> createState() => _RCheckerDialogState();
}

class _RCheckerDialogState extends ConsumerState<RCheckerDialog>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showDetails = false;

  @override
  void initState() {
    super.initState();
    // Create tabs based on available data
    int tabCount = 1; // Always have summary
    if (widget.result.successList.isNotEmpty) tabCount++;
    if (widget.result.errorList.isNotEmpty) tabCount++;
    if (widget.result.closedNumbers.isNotEmpty) tabCount++;

    _tabController = TabController(length: tabCount, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(context, colorScheme),
            _buildTabBar(context, colorScheme),
            Expanded(child: _buildTabContent(context, colorScheme)),
            _buildActionButtons(context, colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ColorScheme colorScheme) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            widget.result.hasErrors
                ? colorScheme.errorContainer
                : colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            widget.result.hasErrors
                ? Icons.warning_rounded
                : Icons.check_circle_rounded,
            color:
                widget.result.hasErrors
                    ? colorScheme.onErrorContainer
                    : colorScheme.onPrimaryContainer,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'R-Checker Results',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color:
                        widget.result.hasErrors
                            ? colorScheme.onErrorContainer
                            : colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.result.hasErrors
                      ? 'Processing completed with ${widget.result.errorList.length} error(s)'
                      : 'Processing completed successfully',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color:
                        widget.result.hasErrors
                            ? colorScheme.onErrorContainer
                            : colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close,
              color:
                  widget.result.hasErrors
                      ? colorScheme.onErrorContainer
                      : colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, ColorScheme colorScheme) {
    List<Tab> tabs = [
      const Tab(text: 'Summary', icon: Icon(Icons.analytics_outlined)),
    ];

    if (widget.result.successList.isNotEmpty) {
      tabs.add(
        Tab(
          text: 'Success (${widget.result.successList.length})',
          icon: const Icon(Icons.check_circle_outline),
        ),
      );
    }

    if (widget.result.errorList.isNotEmpty) {
      tabs.add(
        Tab(
          text: 'Errors (${widget.result.errorList.length})',
          icon: const Icon(Icons.error_outline),
        ),
      );
    }

    if (widget.result.closedNumbers.isNotEmpty) {
      tabs.add(
        Tab(
          text: 'Closed (${widget.result.closedNumbers.length})',
          icon: const Icon(Icons.block),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: colorScheme.outline.withOpacity(0.2)),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: tabs,
        isScrollable: tabs.length > 3,
        labelColor: colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurface.withOpacity(0.6),
        indicatorColor: colorScheme.primary,
      ),
    );
  }

  Widget _buildTabContent(BuildContext context, ColorScheme colorScheme) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSummaryTab(context, colorScheme),
        if (widget.result.successList.isNotEmpty)
          _buildSuccessTab(context, colorScheme),
        if (widget.result.errorList.isNotEmpty)
          _buildErrorTab(context, colorScheme),
        if (widget.result.closedNumbers.isNotEmpty)
          _buildClosedTab(context, colorScheme),
      ],
    );
  }

  Widget _buildSummaryTab(BuildContext context, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(
            'Successful Results',
            widget.result.successList.length.toString(),
            Icons.check_circle,
            colorScheme.primary,
            colorScheme.primaryContainer,
          ),
          const SizedBox(height: 12),
          _buildSummaryCard(
            'Errors Found',
            widget.result.errorList.length.toString(),
            Icons.error,
            colorScheme.error,
            colorScheme.errorContainer,
          ),
          const SizedBox(height: 12),
          _buildSummaryCard(
            'Closed Numbers',
            widget.result.closedNumbers.length.toString(),
            Icons.block,
            colorScheme.tertiary,
            colorScheme.tertiaryContainer,
          ),
          const SizedBox(height: 24),
          if (widget.result.hasErrors) _buildErrorSummary(context, colorScheme),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color iconColor,
    Color backgroundColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: iconColor.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: iconColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSummary(BuildContext context, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.error.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: colorScheme.error),
              const SizedBox(width: 8),
              Text(
                'Processing Issues Detected',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Some inputs could not be processed correctly. Check the Errors tab for details.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onErrorContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessTab(BuildContext context, ColorScheme colorScheme) {
    return _buildListTab(
      'Successfully Processed',
      widget.result.successList,
      Icons.check_circle,
      colorScheme.primary,
      colorScheme.primaryContainer,
    );
  }

  Widget _buildErrorTab(BuildContext context, ColorScheme colorScheme) {
    return _buildListTab(
      'Processing Errors',
      widget.result.errorList,
      Icons.error,
      colorScheme.error,
      colorScheme.errorContainer,
    );
  }

  Widget _buildClosedTab(BuildContext context, ColorScheme colorScheme) {
    return _buildListTab(
      'Closed Numbers',
      widget.result.closedNumbers,
      Icons.block,
      colorScheme.tertiary,
      colorScheme.tertiaryContainer,
    );
  }

  Widget _buildListTab(
    String title,
    List<String> items,
    IconData icon,
    Color iconColor,
    Color backgroundColor,
  ) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border(
              bottom: BorderSide(color: iconColor.withOpacity(0.2)),
            ),
          ),
          child: Row(
            children: [
              Icon(icon, color: iconColor),
              const SizedBox(width: 8),
              Text(
                '$title (${items.length})',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              if (items.isNotEmpty)
                IconButton(
                  onPressed: () => _copyToClipboard(items.join('\n')),
                  icon: const Icon(Icons.copy),
                  tooltip: 'Copy all',
                ),
            ],
          ),
        ),
        Expanded(
          child:
              items.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(icon, size: 48, color: iconColor.withOpacity(0.5)),
                        const SizedBox(height: 16),
                        Text(
                          'No ${title.toLowerCase()} found',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: backgroundColor,
                            child: Text(
                              '${index + 1}',
                              style: TextStyle(
                                color: iconColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(
                            items[index],
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                          trailing: IconButton(
                            onPressed: () => _copyToClipboard(items[index]),
                            icon: const Icon(Icons.copy, size: 18),
                            tooltip: 'Copy',
                          ),
                          dense: true,
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: colorScheme.outline.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          if (widget.result.successList.isNotEmpty)
            Expanded(
              child: ElevatedButton.icon(
                onPressed:
                    () =>
                        _copyToClipboard(widget.result.successList.join('\n')),
                icon: const Icon(Icons.copy),
                label: const Text('Copy Success'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primaryContainer,
                  foregroundColor: colorScheme.onPrimaryContainer,
                ),
              ),
            ),
          if (widget.result.successList.isNotEmpty &&
              widget.result.errorList.isNotEmpty)
            const SizedBox(width: 8),
          if (widget.result.errorList.isNotEmpty)
            Expanded(
              child: ElevatedButton.icon(
                onPressed:
                    () => _copyToClipboard(widget.result.errorList.join('\n')),
                icon: const Icon(Icons.error),
                label: const Text('Copy Errors'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.errorContainer,
                  foregroundColor: colorScheme.onErrorContainer,
                ),
              ),
            ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Copied to clipboard'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

// Usage example:
/*
void example(BuildContext context, WidgetRef ref) async {
  try {
    // Your processing logic here
    ProcessingResult result = await processText("your input text");
    
    // Show the result dialog
    rChecker(context, ref, result);
    
  } catch (e) {
    // Handle processing error
    ProcessingResult errorResult = ProcessingResult(
      successList: [],
      errorList: ['Processing failed: ${e.toString()}'],
      closedNumbers: [],
      hasErrors: true,
    );
    
    rChecker(context, ref, errorResult);
  }
}
*/
