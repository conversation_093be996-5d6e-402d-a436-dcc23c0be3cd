import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import '../theme/app_colors.dart';
import '../util/format_utils.dart';

class ImageExportService {
  /// Generate a complete ledger image that shows all data without scrolling
  /// This method is deprecated - use captureFromWidgetTree instead
  @deprecated
  static Future<Uint8List?> generateLedgerImage({
    required Map<String, int> ledger,
    required int total,
    required String matchTitle,
    required int brakeAmount,
  }) async {
    debugPrint(
      'generateLedgerImage is deprecated. Use captureFromWidgetTree instead with a BuildContext.',
    );
    return null;
  }

  /// Build the complete widget that contains all ledger data
  static Widget _buildCompleteImageWidget({
    required Map<String, int> ledger,
    required int total,
    required String matchTitle,
    required int brakeAmount,
    required double imageWidth,
    required double imageHeight,
    required int itemsPerRow,
  }) {
    final entries = ledger.entries.toList();
    final List<List<MapEntry<String, int>>> rows = [];

    for (int i = 0; i < entries.length; i += itemsPerRow) {
      final end =
          (i + itemsPerRow < entries.length) ? i + itemsPerRow : entries.length;
      rows.add(entries.sublist(i, end));
    }

    return Container(
      width: imageWidth,
      height: imageHeight,
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Ledger Report',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Match: $matchTitle',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Text(
                  'Generated: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                Text(
                  'Brake Amount: ${FormatUtils.formatNumber(brakeAmount)}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Legend
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red),
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Over Brake',
                style: TextStyle(fontSize: 12, color: Colors.black),
              ),
              const SizedBox(width: 24),
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.green),
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Under Brake',
                style: TextStyle(fontSize: 12, color: Colors.black),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Data grid - All rows without scrolling
          Expanded(
            child: Column(
              children:
                  rows
                      .map(
                        (row) => Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children:
                                row
                                    .map(
                                      (entry) => Expanded(
                                        child: Container(
                                          height: 70,
                                          margin: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color:
                                                entry.value > brakeAmount
                                                    ? Colors.red.withValues(
                                                      alpha: 0.1,
                                                    )
                                                    : Colors.green.withValues(
                                                      alpha: 0.1,
                                                    ),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            border: Border.all(
                                              color:
                                                  entry.value > brakeAmount
                                                      ? Colors.red.withValues(
                                                        alpha: 0.3,
                                                      )
                                                      : Colors.green.withValues(
                                                        alpha: 0.3,
                                                      ),
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                entry.key,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black,
                                                ),
                                              ),
                                              Text(
                                                FormatUtils.formatNumber(
                                                  entry.value,
                                                ),
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color:
                                                      entry.value > brakeAmount
                                                          ? Colors.red
                                                          : Colors.green,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                    .toList(),
                          ),
                        ),
                      )
                      .toList(),
            ),
          ),

          const SizedBox(height: 20),

          // Total section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primary, width: 2),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Text(
                  FormatUtils.formatNumber(total),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: total >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Render a widget to image bytes using a temporary overlay
  static Future<Uint8List?> _renderWidgetToImage(
    Widget widget,
    Size size,
  ) async {
    try {
      // This method needs to be called from within the widget tree
      // Return null and let the caller know to use the in-tree method
      debugPrint('Use captureFromWidgetTree method instead');
      return null;
    } catch (e) {
      debugPrint('Error rendering widget to image: $e');
      return null;
    }
  }

  /// Method to capture widget that's already in the tree
  static Future<Uint8List?> captureFromWidgetTree({
    required Map<String, int> ledger,
    required int total,
    required String matchTitle,
    required int brakeAmount,
    required BuildContext context,
  }) async {
    try {
      // Calculate dimensions
      const double imageWidth = 800;
      const double itemWidth = 120;
      const double headerHeight = 150;
      const double footerHeight = 100;
      const double padding = 20;

      final int itemsPerRow =
          ((imageWidth - (padding * 2)) / itemWidth).floor();
      final entries = ledger.entries.toList();
      final int numberOfRows = (entries.length / itemsPerRow).ceil();
      final double contentHeight = (numberOfRows * 80) + (numberOfRows - 1) * 8;
      final double totalImageHeight =
          headerHeight + contentHeight + footerHeight + (padding * 3);

      // Create the widget
      final widget = _buildCompleteImageWidget(
        ledger: ledger,
        total: total,
        matchTitle: matchTitle,
        brakeAmount: brakeAmount,
        imageWidth: imageWidth,
        imageHeight: totalImageHeight,
        itemsPerRow: itemsPerRow,
      );

      // Use an overlay to temporarily render the widget
      late OverlayEntry overlayEntry;
      final GlobalKey repaintKey = GlobalKey();
      final Completer<Uint8List?> completer = Completer<Uint8List?>();

      overlayEntry = OverlayEntry(
        builder:
            (context) => Positioned(
              left: -2000, // Position off-screen
              top: -2000,
              child: Material(
                child: RepaintBoundary(
                  key: repaintKey,
                  child: SizedBox(
                    width: imageWidth,
                    height: totalImageHeight,
                    child: widget,
                  ),
                ),
              ),
            ),
      );

      // Insert the overlay
      Overlay.of(context).insert(overlayEntry);

      // Wait for the next frame to ensure the widget is rendered
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        try {
          // Small delay to ensure rendering is complete
          await Future.delayed(const Duration(milliseconds: 100));

          // Capture the image
          final RenderRepaintBoundary boundary =
              repaintKey.currentContext?.findRenderObject()
                  as RenderRepaintBoundary;

          final ui.Image image = await boundary.toImage(pixelRatio: 1.0);
          final ByteData? byteData = await image.toByteData(
            format: ui.ImageByteFormat.png,
          );

          // Remove the overlay
          overlayEntry.remove();

          completer.complete(byteData?.buffer.asUint8List());
        } catch (e) {
          overlayEntry.remove();
          completer.complete(null);
          debugPrint('Error capturing image: $e');
        }
      });

      return await completer.future;
    } catch (e) {
      debugPrint('Error in captureFromWidgetTree: $e');
      return null;
    }
  }

  /// Simplified method - Use this if the above doesn't work
  /// This requires you to call it from within a widget that's already in the tree
  static Future<Uint8List?> captureWidgetAsImage(GlobalKey key) async {
    try {
      RenderRepaintBoundary boundary =
          key.currentContext!.findRenderObject() as RenderRepaintBoundary;

      ui.Image image = await boundary.toImage(pixelRatio: 1.0);
      ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      return byteData?.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error capturing image: $e');
      return null;
    }
  }

  /// Save image to gallery using ImageGallerySaverPlus
  static Future<bool> saveImageToGallery(
    Uint8List imageBytes,
    String fileName,
  ) async {
    try {
      final result = await ImageGallerySaverPlus.saveImage(
        imageBytes,
        name: fileName,
        isReturnImagePathOfIOS: true,
      );

      return result['isSuccess'] == true;
    } catch (e) {
      debugPrint('Error saving image to gallery: $e');
      return false;
    }
  }
}
