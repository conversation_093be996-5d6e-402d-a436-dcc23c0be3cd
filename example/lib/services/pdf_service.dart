import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:intl/intl.dart';

class PdfService {
  static Future<Uint8List> generateLedgerPdf({
    required Map<String, int> ledger,
    required int total,
    required String matchTitle,
    required int brakeAmount,
  }) async {
    final pdf = pw.Document();

    // Calculate how many items can fit per row based on page width
    final double pageWidth = PdfPageFormat.a4.width - 80; // Account for margins
    const double itemWidth = 80; // Width for each number-amount pair
    final int itemsPerRow = (pageWidth / itemWidth).floor();

    // Split ledger entries into chunks that fit on rows
    final entries = ledger.entries.toList();
    final List<List<MapEntry<String, int>>> rows = [];

    for (int i = 0; i < entries.length; i += itemsPerRow) {
      final end =
          (i + itemsPerRow < entries.length) ? i + itemsPerRow : entries.length;
      rows.add(entries.sublist(i, end));
    }

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(40),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey300,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Ledger Report',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'Match: $matchTitle',
                      style: pw.TextStyle(fontSize: 14),
                    ),
                    pw.Text(
                      'Generated: ${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        color: PdfColors.grey600,
                      ),
                    ),
                    pw.Text(
                      'Brake Amount: ${_formatNumber(brakeAmount)}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        color: PdfColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Legend
              pw.Row(
                children: [
                  pw.Container(width: 12, height: 12, color: PdfColors.red),
                  pw.SizedBox(width: 4),
                  pw.Text('Over Brake', style: pw.TextStyle(fontSize: 10)),
                  pw.SizedBox(width: 16),
                  pw.Container(width: 12, height: 12, color: PdfColors.green),
                  pw.SizedBox(width: 4),
                  pw.Text('Under Brake', style: pw.TextStyle(fontSize: 10)),
                ],
              ),

              pw.SizedBox(height: 16),

              // Data grid
              pw.Expanded(
                child: pw.Column(
                  children:
                      rows
                          .map(
                            (row) => pw.Container(
                              margin: const pw.EdgeInsets.only(bottom: 4),
                              child: pw.Row(
                                children:
                                    row
                                        .map(
                                          (entry) => pw.Expanded(
                                            child: pw.Container(
                                              margin: const pw.EdgeInsets.only(
                                                right: 4,
                                              ),
                                              padding: const pw.EdgeInsets.all(
                                                8,
                                              ),
                                              decoration: pw.BoxDecoration(
                                                border: pw.Border.all(
                                                  color: PdfColors.grey400,
                                                ),
                                                borderRadius: pw
                                                    .BorderRadius.circular(4),
                                                color:
                                                    entry.value > brakeAmount
                                                        ? PdfColors.red50
                                                        : PdfColors.green50,
                                              ),
                                              child: pw.Row(
                                                mainAxisAlignment:
                                                    pw
                                                        .MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  pw.Text(
                                                    entry.key,
                                                    style: pw.TextStyle(
                                                      fontSize: 10,
                                                      fontWeight:
                                                          pw.FontWeight.bold,
                                                    ),
                                                  ),
                                                  pw.Text(
                                                    _formatNumber(entry.value),
                                                    style: pw.TextStyle(
                                                      fontSize: 10,
                                                      color:
                                                          entry.value >
                                                                  brakeAmount
                                                              ? PdfColors.red
                                                              : PdfColors.green,
                                                      fontWeight:
                                                          pw.FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                              ),
                            ),
                          )
                          .toList(),
                ),
              ),

              // Total section
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey200,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColors.grey400, width: 2),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      'Total',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      _formatNumber(total),
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        color: total >= 0 ? PdfColors.green : PdfColors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  static String _formatNumber(int amount) {
    final formatter = NumberFormat('#,###');
    return formatter.format(amount);
  }
}
