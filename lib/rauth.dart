import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/app_update_screen.dart';
import 'package:rauth/banned_device_screen.dart';
import 'package:rauth/global_alert_screen.dart';
import 'package:rauth/maintainace.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/subscription_screen.dart';

import 'rlogin.dart';
import 'shut_down.dart';

class RAuth extends ConsumerWidget {
  final Widget home;
  const RAuth(this.home, {super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if device is banned
    final isDeviceBanned = ref.watch(isDeviceBannedProvider);
    // Check if there's a global alert
    final shouldShowAlert = ref.watch(shouldShowAlertProvider);
    final alert = ref.watch(globalAlertProvider);
    // Check if an update is available
    final updateInfo = ref.watch(updateAvailableProvider);

    return Scaffold(
      body: isDeviceBanned.when(
        data: (isBanned) {
          if (isBanned) {
            return const BannedDeviceScreen();
          }

          // If not banned, continue with normal flow
          Widget content = ref
              .watch(appStateProvider)
              .when(
                data:
                    (appState) => ref
                        .watch(userProvider)
                        .when(
                          data: (user) {
                            if (appState['maintenanceMode']) {
                              return const MaintenanceScreen();
                            }
                            if (appState['shutdown']) {
                              return const ShutdownScreen();
                            }
                            if (user == null) {
                              return RLoginScreen();
                            }
                            if(user .deviceId ==null){
                              return RLoginScreen();
                            }
                            if (user.banned ?? false) {
                              return const BannedDeviceScreen();
                            }
                            if (user.role == 'tester' &&
                                user.testerExpiration != null &&
                                user.testerExpiration!.toDate().isBefore(
                                  DateTime.now(),
                                )) {
                              return SubscriptionScreen();
                            }
                            return home;
                          },
                          error:
                              (error, stackTrace) =>
                                   RLoginScreen(),
                          loading:
                              () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                        ),
                error:
                    (error, stackTrace) => const Center(child: Text('Error')),
                loading: () => const Center(child: CircularProgressIndicator()),
              );

          // Wrap content with update screen if needed
          if (updateInfo['updateAvailable']) {
            content = AppUpdateScreen(
              forceUpdate: updateInfo['isMandatory'],
              child: content,
            );
          }

          // Show global alert if needed
          if (shouldShowAlert && alert != null) {
            return GlobalAlertScreen(
              alert: alert,
              onDismiss: () {
                // Add the alert ID to the dismissed alerts
                ref
                    .read(dismissedAlertsProvider.notifier)
                    .update((state) => {...state, alert.id});
              },
              child: content,
            );
          }

          return content;
        },
        error:
            (error, stackTrace) =>
                const Center(child: Text('Error checking device status')),
        loading: () => const Center(child: CircularProgressIndicator()),
      ),
    );
  }
}
