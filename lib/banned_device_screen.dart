import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';

class BannedDeviceScreen extends StatelessWidget {
  const BannedDeviceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF8B0000), Color(0xFFB22222), Color(0xFFDC143C)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Animated background blur elements
                  Positioned(
                    top: 50,
                    left: 40,
                    child: _buildBlurCircle(100, const Color(0x30FF4500)),
                  ),
                  Positioned(
                    bottom: 90,
                    right: 60,
                    child: _buildBlurCircle(120, const Color(0x30FF6347)),
                  ),

                  // Main content card
                  Container(
                    constraints: const BoxConstraints(maxWidth: 380),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 40,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(25),
                      borderRadius: BorderRadius.circular(32),
                      border: Border.all(
                        color: Colors.white.withAlpha(51),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(51),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Lottie animation
                          SizedBox(
                                child: Lottie.asset(
                                  'assets/lotties/banned.json',
                                  package: 'rauth',
                                  height: 200,
                                  repeat: true,
                                  // If banned.json doesn't exist, use a fallback
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.block,
                                      size: 100,
                                      color: Colors.white,
                                    );
                                  },
                                ),
                              )
                              .animate()
                              .fadeIn(duration: 600.ms)
                              .moveY(begin: 10, end: 0),
                          const SizedBox(height: 24),

                          // Main title
                          const Text(
                                'Device Banned',
                                style: TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                  letterSpacing: -0.5,
                                ),
                                textAlign: TextAlign.center,
                              )
                              .animate()
                              .fadeIn(delay: 300.ms, duration: 600.ms)
                              .moveY(begin: 10, end: 0),

                          const SizedBox(height: 16),

                          const Text(
                                'This device has been banned from accessing the application. Please contact support for assistance.',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  height: 1.5,
                                  letterSpacing: 0.2,
                                ),
                                textAlign: TextAlign.center,
                              )
                              .animate()
                              .fadeIn(delay: 500.ms, duration: 600.ms)
                              .moveY(begin: 10, end: 0),

                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBlurCircle(double size, Color color) {
    return Container(
          height: size,
          width: size,
          decoration: BoxDecoration(shape: BoxShape.circle, color: color),
        )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .scaleXY(
          begin: 0.8,
          end: 1.2,
          duration: 4000.ms,
          curve: Curves.easeInOut,
        );
  }
}
