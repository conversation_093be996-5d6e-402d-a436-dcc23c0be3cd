import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:rauth/models/global_alert.dart';
import 'package:url_launcher/url_launcher.dart';

class GlobalAlertScreen extends ConsumerWidget {
  final GlobalAlert alert;
  final VoidCallback onDismiss;
  final Widget child;

  const GlobalAlertScreen({
    super.key,
    required this.alert,
    required this.onDismiss,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        // The main content (blurred when alert is shown)
        Positioned.fill(child: child),

        // Alert overlay with modern glass effect
        Positioned.fill(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.black.withAlpha(102), // ~0.4 opacity
                    Colors.black.withAlpha(51), // ~0.2 opacity
                  ],
                ),
              ),
              child: Safe<PERSON>rea(child: Center(child: _buildAlertCard(context))),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAlertCard(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Determine colors based on alert type
    Color primaryColor;
    Color backgroundColor;
    String animationAsset;

    switch (alert.type) {
      case AlertType.warning:
        primaryColor = Colors.orange;
        backgroundColor =
            isDarkMode
                ? Colors.orange.shade900.withAlpha(51) // ~0.2 opacity
                : Colors.orange.shade50;
        animationAsset = 'assets/lotties/warning.json';
        break;
      case AlertType.error:
        primaryColor = Colors.red;
        backgroundColor =
            isDarkMode
                ? Colors.red.shade900.withAlpha(51) // ~0.2 opacity
                : Colors.red.shade50;
        animationAsset = 'assets/lotties/error.json';
        break;
      case AlertType.success:
        primaryColor = Colors.green;
        backgroundColor =
            isDarkMode
                ? Colors.green.shade900.withAlpha(51) // ~0.2 opacity
                : Colors.green.shade50;
        animationAsset = 'assets/lotties/success.json';
        break;
      case AlertType.info:
        primaryColor = Colors.blue;
        backgroundColor =
            isDarkMode
                ? Colors.blue.shade900.withAlpha(51) // ~0.2 opacity
                : Colors.blue.shade50;
        animationAsset = 'assets/lotties/info.json';
        break;
    }

    // Card background color based on theme
    final cardColor =
        isDarkMode
            ? Color.lerp(Colors.grey.shade900, primaryColor, 0.05)!
            : Colors.white;

    // Text colors based on theme
    final titleColor = primaryColor;
    final messageColor =
        isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800;

    return Container(
      margin: const EdgeInsets.all(24),
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 500),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
        clipBehavior: Clip.antiAlias,
        child: Container(
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: primaryColor.withAlpha(51), // ~0.2 opacity
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Alert header with color strip
              Container(width: double.infinity, height: 8, color: primaryColor),

              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Animation with modern container
                    Container(
                          height: 100,
                          width: 100,
                          decoration: BoxDecoration(
                            color: backgroundColor,
                            shape: BoxShape.circle,
                          ),
                          child: Lottie.asset(
                            animationAsset,
                            package: 'rauth',
                            repeat: true,
                            // Fallback if animation not found
                            errorBuilder: (context, error, stackTrace) {
                              IconData iconData;
                              switch (alert.type) {
                                case AlertType.warning:
                                  iconData = Icons.warning_amber_rounded;
                                  break;
                                case AlertType.error:
                                  iconData = Icons.error_outline;
                                  break;
                                case AlertType.success:
                                  iconData = Icons.check_circle_outline;
                                  break;
                                case AlertType.info:
                                  iconData = Icons.info_outline;
                                  break;
                              }
                              return Icon(
                                iconData,
                                size: 60,
                                color: primaryColor,
                              );
                            },
                          ),
                        )
                        .animate()
                        .fadeIn(duration: 500.ms)
                        .scale(
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1.0, 1.0),
                        ),

                    const SizedBox(height: 24),

                    // Title with modern typography
                    Text(
                          alert.title,
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            color: titleColor,
                            letterSpacing: -0.5,
                          ),
                          textAlign: TextAlign.center,
                        )
                        .animate()
                        .fadeIn(delay: 200.ms, duration: 500.ms)
                        .moveY(begin: 10, end: 0),

                    const SizedBox(height: 16),

                    // Message with improved readability
                    Text(
                          alert.message,
                          style: TextStyle(
                            fontSize: 16,
                            color: messageColor,
                            height: 1.5,
                            letterSpacing: 0.1,
                          ),
                          textAlign: TextAlign.center,
                        )
                        .animate()
                        .fadeIn(delay: 300.ms, duration: 500.ms)
                        .moveY(begin: 10, end: 0),

                    const SizedBox(height: 32),

                    // Modern action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (alert.dismissible)
                          TextButton(
                                onPressed: onDismiss,
                                style: TextButton.styleFrom(
                                  foregroundColor: primaryColor,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Dismiss',
                                  style: TextStyle(fontWeight: FontWeight.w500),
                                ),
                              )
                              .animate()
                              .fadeIn(delay: 400.ms, duration: 500.ms)
                              .moveY(begin: 10, end: 0),

                        if (alert.actionText != null &&
                            alert.actionUrl != null) ...[
                          const SizedBox(width: 16),
                          FilledButton(
                                onPressed: () async {
                                  final url = Uri.parse(alert.actionUrl!);
                                  if (await canLaunchUrl(url)) {
                                    await launchUrl(url);
                                  }
                                },
                                style: FilledButton.styleFrom(
                                  backgroundColor: primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  alert.actionText!,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                              )
                              .animate()
                              .fadeIn(delay: 400.ms, duration: 500.ms)
                              .moveY(begin: 10, end: 0),
                        ],
                      ],
                    ),

                    // Show expiration info if available
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
