import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/app_user.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/r_scafold.dart';
import 'package:rauth/r_services.dart';

/// A screen that allows the user to edit their profile information
class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final authService = RAuthService(
      ref.watch(authProvider),
      ref.watch(firestoreProvider),
    );

    return RScafold(
      title: "Edit Profile",
      showBackButton: true,
      body: ref
          .watch(userProvider)
          .when(
            data: (user) {
              if (user == null) {
                return const Center(child: Text("You are not logged in"));
              }

              // Initialize controllers with user data
              _initControllers(user);

              return _buildForm(context, user, authService, colorScheme);
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Center(child: Text("Error: $error")),
          ),
    );
  }

  void _initControllers(AppUser user) {
    // Only initialize if the controllers are empty
    if (_nameController.text.isEmpty) {
      _nameController.text = user.name;
    }
    if (_emailController.text.isEmpty) {
      _emailController.text = user.email;
    }
    if (_phoneController.text.isEmpty) {
      _phoneController.text = user.phone;
    }
  }

  Widget _buildForm(
    BuildContext context,
    AppUser user,
    RAuthService authService,
    ColorScheme colorScheme,
  ) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile header with avatar
              Center(
                child: Column(
                  children: [
                    CircleAvatar(
                          radius: 50,
                          backgroundColor: colorScheme.primaryContainer,
                          child: Text(
                            _getInitials(user.name),
                            style: TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: colorScheme.onPrimaryContainer,
                            ),
                          ),
                        )
                        .animate()
                        .fadeIn(duration: 600.ms)
                        .moveY(begin: 0.8, end: 1.0),
                    const SizedBox(height: 16),
                    Text(
                          "Edit Your Profile",
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        )
                        .animate()
                        .fadeIn(duration: 600.ms, delay: 200.ms)
                        .moveY(begin: 20, end: 0),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Name field
              _buildTextField(
                    controller: _nameController,
                    label: "Name",
                    icon: Icons.person_outline,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return "Please enter your name";
                      }
                      return null;
                    },
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 300.ms)
                  .moveY(begin: 20, end: 0),

              const SizedBox(height: 16),

              // Email field
              _buildTextField(
                    controller: _emailController,
                    label: "Email",
                    icon: Icons.email_outlined,

                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return "Please enter your email";
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return "Please enter a valid email";
                      }
                      return null;
                    },
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 400.ms)
                  .moveY(begin: 20, end: 0),

              const SizedBox(height: 16),

              // Phone field
              _buildTextField(
                    controller: _phoneController,
                    label: "Phone",
                    icon: Icons.phone_outlined,
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return "Please enter your phone number";
                      }
                      return null;
                    },
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 500.ms)
                  .moveY(begin: 20, end: 0),

              const SizedBox(height: 32),

              // Save button
              Center(
                child: ElevatedButton.icon(
                      onPressed:
                          _isLoading
                              ? null
                              : () => _saveProfile(context, authService),
                      icon:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : const Icon(Icons.save),
                      label: Text(_isLoading ? "Saving..." : "Save Changes"),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    )
                    .animate()
                    .fadeIn(duration: 600.ms, delay: 600.ms)
                    .moveY(begin: 20, end: 0),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      enabled: label != 'Email',
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Future<void> _saveProfile(
    BuildContext context,
    RAuthService authService,
  ) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await authService.updateProfile(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
      );

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Profile updated successfully"),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.of(context).pop();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error updating profile: $e"),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '';

    final nameParts = name.split(' ');
    if (nameParts.length == 1) {
      return nameParts[0][0].toUpperCase();
    }

    return nameParts[0][0].toUpperCase() + nameParts.last[0].toUpperCase();
  }
}
