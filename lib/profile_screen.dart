import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/admin/admin_panel_screen.dart';
import 'package:rauth/app_user.dart';
import 'package:rauth/change_password.dart';
import 'package:rauth/edit_profile_screen.dart';
import 'package:rauth/logout_button.dart';
import 'package:rauth/profile_picture_screen.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/r_scafold.dart';
import 'package:rauth/r_services.dart';
import 'package:rauth/time_left_text.dart';
import 'package:rauth/widgets.dart';

/// A screen that displays the user's profile information
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final authService = RAuthService(
      ref.watch(authProvider),
      ref.watch(firestoreProvider),
    );

    return RScafold(
      title: "Profile",
      actions: [
        OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EditProfileScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.edit),
              label: const Text('Edit Profile'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            )
            .animate()
            .fadeIn(duration: 600.ms, delay: 500.ms)
            .moveY(begin: 20, end: 0),
      ],
      showBackButton: true,
      body: ref
          .watch(userProvider)
          .when(
            data: (user) {
              if (user == null) {
                return const Center(child: Text("You are not logged in"));
              }
              return _buildProfileContent(
                context,
                user,
                authService,
                colorScheme,
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Center(child: Text("Error: $error")),
          ),
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    AppUser user,
    RAuthService authService,
    ColorScheme colorScheme,
  ) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header with avatar
            _buildProfileHeader(context, user, colorScheme),

            const SizedBox(height: 16),

            // Edit profile button

            // Account information section
            _buildInfoSection(
              title: "Account Information",
              icon: Icons.person_outline,
              colorScheme: colorScheme,
              children: [
                _buildInfoItem(
                  label: "Name",
                  value: user.name,
                  icon: Icons.person,
                  colorScheme: colorScheme,
                ),
                _buildInfoItem(
                  label: "Email",
                  value: user.email,
                  icon: Icons.email_outlined,
                  colorScheme: colorScheme,
                ),
                _buildInfoItem(
                  label: "Phone",
                  value: user.phone,
                  icon: Icons.phone_outlined,
                  colorScheme: colorScheme,
                ),
                _buildInfoItem(
                  label: "Role",
                  value: _formatRole(user.role),
                  icon: Icons.badge_outlined,
                  colorScheme: colorScheme,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Account status section
            _buildInfoSection(
              title: "Account Status",
              icon: Icons.security_outlined,
              colorScheme: colorScheme,
              children: [
                if (user.role == 'tester') ...[
                  _buildInfoItem(
                    label: "Account Type",
                    value: "Tester Account",
                    icon: Icons.verified_outlined,
                    colorScheme: colorScheme,
                  ),
                  _buildTimeLeftItem(colorScheme),
                ] else
                  _buildInfoItem(
                    label: "Account Type",
                    value: _formatRole(user.role),
                    icon: Icons.verified_outlined,
                    colorScheme: colorScheme,
                  ),
                _buildInfoItem(
                  label: "Last Login",
                  value:
                      user.lastUsed != null
                          ? _formatDate(user.lastUsed!.toDate())
                          : "Unknown",
                  icon: Icons.access_time,
                  colorScheme: colorScheme,
                ),
                _buildInfoItem(
                  label: "Device ID",
                  value: user.deviceId ?? "None",
                  icon: Icons.devices_outlined,
                  colorScheme: colorScheme,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),

            const SizedBox(height: 18),

            // Admin panel button (only for admins)
            if (user.role == 'admin') ...[
              // Center(
              //   child: OutlinedButton.icon(
              //         onPressed: () {
              //           Navigator.of(context).push(
              //             MaterialPageRoute(
              //               builder: (context) => const AdminPanelScreen(),
              //             ),
              //           );
              //         },
              //         icon: const Icon(Icons.admin_panel_settings),
              //         label: const Text("Admin Panel"),
              //         style: OutlinedButton.styleFrom(
              //           foregroundColor: colorScheme.primary,
              //           side: BorderSide(color: colorScheme.primary),
              //           padding: const EdgeInsets.symmetric(
              //             horizontal: 24,
              //             vertical: 12,
              //           ),
              //         ),
              //       )
              //       .animate()
              //       .fadeIn(duration: 600.ms, delay: 400.ms)
              //       .moveY(begin: 20, end: 0),
              // ),
              buildActionButton(
                context,
                "Admin Panel",
                "Manage users and payments",
                Icons.admin_panel_settings,
                colorScheme.primary,
                () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const AdminPanelScreen(),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],

            // Security section with change password and logout
            _buildInfoSection(
              title: "Security",
              icon: Icons.security,
              colorScheme: colorScheme,
              children: [
                // Change Password Button
                const ChangePasswordButton()
                    .animate()
                    .fadeIn(duration: 600.ms, delay: 300.ms)
                    .moveY(begin: 20, end: 0),
              ],
            ),

            const SizedBox(height: 24),

            // Sign out button
            LogoutButton()
                .animate()
                .fadeIn(duration: 600.ms, delay: 400.ms)
                .moveY(begin: 20, end: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(
    BuildContext context,
    AppUser user,
    ColorScheme colorScheme,
  ) {
    double size = 100;

    return Center(
      child: Column(
        children: [
          // Avatar with profile picture or initials
          GestureDetector(
                onTap: () => _navigateToProfilePictureScreen(context, user),
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: size / 2,
                      backgroundColor: colorScheme.primaryContainer,
                      child: ClipOval(
                        child: Image.network(
                          user.profilePictureUrl,
                          fit: BoxFit.cover,
                          width: size,
                          height: size,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: SizedBox(
                                width: size / 2,
                                height: size / 2,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2.0,
                                  valueColor: AlwaysStoppedAnimation(
                                    colorScheme.onPrimary,
                                  ),
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.account_circle,
                              size: size,
                              color: colorScheme.onPrimary,
                            );
                          },
                        ),
                      ),
                    ),

                    // Edit icon overlay
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: colorScheme.primary,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: colorScheme.surface,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          Icons.edit,
                          size: 16,
                          color: colorScheme.onPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              )
              .animate()
              .fadeIn(duration: 600.ms)
              .scale(
                begin: const Offset(0.8, 0.8),
                end: const Offset(1.0, 1.0),
              ),

          const SizedBox(height: 8),

          // // User name
          // Text(
          //       user.name,
          //       style: TextStyle(
          //         fontSize: 24,
          //         fontWeight: FontWeight.bold,
          //         color: colorScheme.primary,
          //       ),
          //     )
          //     .animate()
          //     .fadeIn(duration: 600.ms, delay: 200.ms)
          //     .moveY(begin: 20, end: 0),

          // const SizedBox(height: 4),

          // // User email
          // Text(
          //       user.email,
          //       style: TextStyle(
          //         fontSize: 16,
          //         color: colorScheme.onSurfaceVariant,
          //       ),
          //     )
          //     .animate()
          //     .fadeIn(duration: 600.ms, delay: 300.ms)
          //     .moveY(begin: 20, end: 0),

          // Role badge
          Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: _getRoleColor(user.role, colorScheme),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _formatRole(user.role),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onPrimary,
                  ),
                ),
              )
              .animate()
              .fadeIn(duration: 600.ms, delay: 400.ms)
              .moveY(begin: 20, end: 0),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required ColorScheme colorScheme,
    required List<Widget> children,
  }) {
    return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, color: colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const Divider(height: 24),
                ...children,
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 600.ms, delay: 200.ms)
        .moveY(begin: 20, end: 0);
  }

  Widget _buildInfoItem({
    required String label,
    required String value,
    required IconData icon,
    required ColorScheme colorScheme,
    int maxLines = 2,
    TextOverflow overflow = TextOverflow.ellipsis,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: colorScheme.primary.withAlpha(179)),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(fontSize: 16),
                  maxLines: maxLines,
                  overflow: overflow,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeLeftItem(ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.timer_outlined,
            size: 20,
            color: colorScheme.primary.withAlpha(179),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Time Remaining",
                  style: TextStyle(
                    fontSize: 14,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                TimeLeftText(
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                  expiredText: "Account Expired",
                  useLiveProvider: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatRole(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Administrator';

      case 'tester':
        return 'Tester';
      case 'user':
        return 'User';
      default:
        return role.capitalize();
    }
  }

  Color _getRoleColor(String role, ColorScheme colorScheme) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;

      case 'tester':
        return Colors.orange;
      case 'user':
        return Colors.green;
      default:
        return colorScheme.primary;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return 'Just now';
        }
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    }

    return '${date.day}/${date.month}/${date.year}';
  }

  /// Navigate to the profile picture screen
  void _navigateToProfilePictureScreen(
    BuildContext context,
    AppUser user,
  ) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => ProfilePictureScreen(user: user)),
    );

    // If the profile picture was updated, refresh the profile screen
    if (result == true) {
      // The user provider will automatically refresh
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
