import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/r_services.dart';

import 'providers.dart';
import 'r_scafold.dart';

class RSignUpScreen extends ConsumerStatefulWidget {
  const RSignUpScreen({super.key});

  @override
  ConsumerState<RSignUpScreen> createState() => _RSignUpScreenState();
}

class _RSignUpScreenState extends ConsumerState<RSignUpScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLoading = false;
  late final RAuthService _authService;

  @override
  void initState() {
    super.initState();
    _authService = RAuthService(
      ref.read(authProvider),
      ref.read(firestoreProvider),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  Future<void> _handleSignUp() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _authService.signUp(
        _emailController.text.trim(),
        _passwordController.text.trim(),
        _nameController.text.trim(),
        _phoneController.text.trim(),
      );

      if (!mounted) return;
      Navigator.of(context).pop();
    } catch (e) {
      if (!mounted) return;

      String errorMessage = 'Error: $e';

      // Check for specific device account error
      if (e.toString().contains('This device already has an account')) {
        errorMessage =
            'This device already has an account. Only one account per device is allowed.';
      } else if (e.toString().contains('This device is banned')) {
        errorMessage = 'This device has been banned from creating accounts.';
      }

      _showErrorSnackBar(errorMessage);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return RScafold(
      showBackButton: true,
      title: "Sign Up",
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildField(
              label: 'Name',
              controller: _nameController,
              icon: Icons.person_outline,
              colorScheme: colorScheme,
            ).animate().fadeIn(duration: 600.ms).moveY(begin: 20, end: 0),
            const SizedBox(height: 16),
            _buildField(
                  label: 'Phone Number',
                  controller: _phoneController,
                  icon: Icons.phone_outlined,
                  keyboardType: TextInputType.phone,
                  colorScheme: colorScheme,
                )
                .animate()
                .fadeIn(duration: 600.ms, delay: 100.ms)
                .moveY(begin: 20, end: 0),
            const SizedBox(height: 16),
            _buildField(
                  label: 'Email',
                  controller: _emailController,
                  icon: Icons.email_outlined,
                  keyboardType: TextInputType.emailAddress,
                  colorScheme: colorScheme,
                )
                .animate()
                .fadeIn(duration: 600.ms, delay: 200.ms)
                .moveY(begin: 20, end: 0),
            const SizedBox(height: 16),
            _buildField(
                  label: 'Password',
                  controller: _passwordController,
                  icon: Icons.lock_outline,
                  obscure: true,
                  colorScheme: colorScheme,
                )
                .animate()
                .fadeIn(duration: 600.ms, delay: 300.ms)
                .moveY(begin: 20, end: 0),
            const SizedBox(height: 32),
            ElevatedButton.icon(
                  onPressed: _isLoading ? null : _handleSignUp,
                  icon:
                      _isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                          : const Icon(Icons.person_add),
                  label: Text(_isLoading ? 'Creating Account...' : 'Register'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                )
                .animate()
                .fadeIn(duration: 600.ms, delay: 400.ms)
                .moveY(begin: 20, end: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildField({
    required String label,
    required TextEditingController controller,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    bool obscure = false,
    required ColorScheme colorScheme,
  }) {
    return TextField(
      controller: controller,
      style: TextStyle(color: colorScheme.primary),
      keyboardType: keyboardType,
      obscureText: obscure,
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: colorScheme.primary),
        prefixIcon: Icon(icon, color: colorScheme.primary),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: colorScheme.inversePrimary),
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: colorScheme.primary),
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
