import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/r_services.dart';
import 'package:rauth/widgets.dart';

import 'providers.dart';

class ChangePasswordButton extends ConsumerWidget {
  const ChangePasswordButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;

    return buildActionButton(
      context,
      'Change Password',
      'Update your account password',
      Icons.lock_outline_rounded,
      colorScheme.secondary,
      () {
        _showChangePasswordDialog(context, colorScheme, ref);
      },
    );
  }

  void _showChangePasswordDialog(
    BuildContext context,
    ColorScheme colorScheme,
    WidgetRef ref,
  ) {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;
    bool isLoading = false;
    String? errorMessage;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: Text(
                'Change Password',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (errorMessage != null) ...[
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline, color: colorScheme.error),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                errorMessage!,
                                style: TextStyle(color: colorScheme.error),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16),
                    ],

                    // Current Password
                    TextField(
                      controller: currentPasswordController,
                      obscureText: obscureCurrentPassword,
                      decoration: InputDecoration(
                        labelText: 'Current Password',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            obscureCurrentPassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                          ),
                          onPressed: () {
                            setState(() {
                              obscureCurrentPassword = !obscureCurrentPassword;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // New Password
                    TextField(
                      controller: newPasswordController,
                      obscureText: obscureNewPassword,
                      decoration: InputDecoration(
                        labelText: 'New Password',
                        helperText: 'At least 6 characters',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            obscureNewPassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                          ),
                          onPressed: () {
                            setState(() {
                              obscureNewPassword = !obscureNewPassword;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Confirm Password
                    TextField(
                      controller: confirmPasswordController,
                      obscureText: obscureConfirmPassword,
                      decoration: InputDecoration(
                        labelText: 'Confirm New Password',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            obscureConfirmPassword
                                ? Icons.visibility_outlined
                                : Icons.visibility_off_outlined,
                          ),
                          onPressed: () {
                            setState(() {
                              obscureConfirmPassword = !obscureConfirmPassword;
                            });
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading ? null : () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: colorScheme.onSurface.withAlpha(
                        153,
                      ), // 0.6 * 255 = 153
                    ),
                  ),
                ),
                FilledButton(
                  onPressed:
                      isLoading
                          ? null
                          : () async {
                            // Validate inputs
                            if (currentPasswordController.text.isEmpty) {
                              setState(() {
                                errorMessage =
                                    'Please enter your current password';
                              });
                              return;
                            }

                            if (newPasswordController.text.isEmpty) {
                              setState(() {
                                errorMessage = 'Please enter a new password';
                              });
                              return;
                            }

                            if (newPasswordController.text.length < 6) {
                              setState(() {
                                errorMessage =
                                    'Password must be at least 6 characters';
                              });
                              return;
                            }

                            if (newPasswordController.text !=
                                confirmPasswordController.text) {
                              setState(() {
                                errorMessage = 'Passwords do not match';
                              });
                              return;
                            }

                            // Set loading state
                            setState(() {
                              isLoading = true;
                              errorMessage = null;
                            });

                            try {
                              // Create auth service
                              final authService = RAuthService(
                                ref.read(authProvider),
                                ref.read(firestoreProvider),
                              );

                              // Use the changePassword method from RAuthService
                              await authService.changePassword(
                                currentPasswordController.text,
                                newPasswordController.text,
                              );

                              // Check if the widget is still mounted before using context
                              if (!context.mounted) return;

                              Navigator.pop(context);

                              // Show success message
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Password successfully updated',
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            } catch (e) {
                              // Handle specific Firebase errors
                              String message = 'Failed to update password';

                              if (e.toString().contains(
                                'Current password is incorrect',
                              )) {
                                message = 'Current password is incorrect';
                              } else if (e.toString().contains(
                                'wrong-password',
                              )) {
                                message = 'Current password is incorrect';
                              } else if (e.toString().contains(
                                'requires-recent-login',
                              )) {
                                message =
                                    'Please sign out and sign in again before changing your password';
                              }

                              setState(() {
                                isLoading = false;
                                errorMessage = message;
                              });
                            }
                          },
                  style: FilledButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                  ),
                  child:
                      isLoading
                          ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: colorScheme.onPrimary,
                              strokeWidth: 2,
                            ),
                          )
                          : const Text('Change Password'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
