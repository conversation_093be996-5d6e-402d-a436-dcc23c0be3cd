import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for app version information
class AppVersion {
  /// The version code (numeric representation for comparison)
  final int versionCode;
  
  /// The version name (semantic version string like "1.2.3")
  final String versionName;
  
  /// Whether this update is mandatory
  final bool isMandatory;
  
  /// Minimum version code required (if null, no minimum)
  final int? minRequiredVersionCode;
  
  /// Release notes for this version
  final String releaseNotes;
  
  /// URL to update the app (platform specific)
  final Map<String, String> updateUrls;
  
  /// When this version was released
  final DateTime releaseDate;

  AppVersion({
    required this.versionCode,
    required this.versionName,
    required this.isMandatory,
    this.minRequiredVersionCode,
    required this.releaseNotes,
    required this.updateUrls,
    required this.releaseDate,
  });

  /// Create an AppVersion from a Firestore document
  factory AppVersion.fromMap(Map<String, dynamic> map) {
    return AppVersion(
      versionCode: map['versionCode'] ?? 0,
      versionName: map['versionName'] ?? '1.0.0',
      isMandatory: map['isMandatory'] ?? false,
      minRequiredVersionCode: map['minRequiredVersionCode'],
      releaseNotes: map['releaseNotes'] ?? 'Bug fixes and improvements',
      updateUrls: Map<String, String>.from(map['updateUrls'] ?? {}),
      releaseDate: map['releaseDate'] != null 
          ? (map['releaseDate'] as Timestamp).toDate() 
          : DateTime.now(),
    );
  }

  /// Convert the AppVersion to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'versionCode': versionCode,
      'versionName': versionName,
      'isMandatory': isMandatory,
      'minRequiredVersionCode': minRequiredVersionCode,
      'releaseNotes': releaseNotes,
      'updateUrls': updateUrls,
      'releaseDate': Timestamp.fromDate(releaseDate),
    };
  }

  /// Check if the current version requires an update based on the device's version
  bool requiresUpdate(int deviceVersionCode) {
    if (minRequiredVersionCode != null) {
      return deviceVersionCode < minRequiredVersionCode!;
    }
    return deviceVersionCode < versionCode;
  }

  /// Check if an update is available but not mandatory
  bool updateAvailable(int deviceVersionCode) {
    return deviceVersionCode < versionCode && 
           (minRequiredVersionCode == null || 
            deviceVersionCode >= minRequiredVersionCode!);
  }

  /// Get the appropriate update URL for the current platform
  String? getUpdateUrl(String platform) {
    return updateUrls[platform.toLowerCase()];
  }
}
