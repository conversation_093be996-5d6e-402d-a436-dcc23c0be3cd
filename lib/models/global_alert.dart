import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for global alerts that can be shown to all users
class GlobalAlert {
  /// The title of the alert
  final String title;
  
  /// The message body of the alert
  final String message;
  
  /// The type of alert (info, warning, error, success)
  final AlertType type;
  
  /// Whether the alert can be dismissed by the user
  final bool dismissible;
  
  /// The action button text (if any)
  final String? actionText;
  
  /// The URL to open when the action button is pressed (if any)
  final String? actionUrl;
  
  /// The expiration date of the alert (if any)
  final DateTime? expiresAt;
  
  /// Unique ID for the alert
  final String id;

  GlobalAlert({
    required this.title,
    required this.message,
    required this.type,
    required this.id,
    this.dismissible = true,
    this.actionText,
    this.actionUrl,
    this.expiresAt,
  });

  /// Create a GlobalAlert from a Firestore document
  factory GlobalAlert.fromMap(Map<String, dynamic> map) {
    return GlobalAlert(
      id: map['id'] ?? 'alert-${DateTime.now().millisecondsSinceEpoch}',
      title: map['title'] ?? 'Alert',
      message: map['message'] ?? 'Please check for updates.',
      type: _parseAlertType(map['type']),
      dismissible: map['dismissible'] ?? true,
      actionText: map['actionText'],
      actionUrl: map['actionUrl'],
      expiresAt: map['expiresAt'] != null 
          ? (map['expiresAt'] as Timestamp).toDate() 
          : null,
    );
  }

  /// Convert the GlobalAlert to a map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.name,
      'dismissible': dismissible,
      'actionText': actionText,
      'actionUrl': actionUrl,
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
    };
  }

  /// Check if the alert is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Parse the alert type from a string
  static AlertType _parseAlertType(String? type) {
    switch (type?.toLowerCase()) {
      case 'warning':
        return AlertType.warning;
      case 'error':
        return AlertType.error;
      case 'success':
        return AlertType.success;
      case 'info':
      default:
        return AlertType.info;
    }
  }
}

/// Types of alerts that can be shown
enum AlertType {
  info,
  warning,
  error,
  success,
}

/// Extension to get color and icon for alert types
extension AlertTypeExtension on AlertType {
  String get name {
    switch (this) {
      case AlertType.info:
        return 'info';
      case AlertType.warning:
        return 'warning';
      case AlertType.error:
        return 'error';
      case AlertType.success:
        return 'success';
    }
  }
}
