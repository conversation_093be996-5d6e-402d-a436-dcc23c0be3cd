import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import 'package:shared_preferences/shared_preferences.dart';

Future<void> setupFirebase(FirebaseOptions options, {bool init = false}) async {
  try {
    await Firebase.initializeApp(options: options);
  } catch (e) {
    debugPrint('Firebase init error: $e');
  }

  final firestore = FirebaseFirestore.instance;
  firestore.settings = const Settings(
    persistenceEnabled: true,
    cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
  );

  if (init) {
    await firestore.collection('app_state').doc('global').set({
      'maintenanceMode': false,
      'shutdown': false,
      'globalAlert': null, // Set to null by default, can be updated later
      'bannedDevices': [],
    }, SetOptions(merge: true));

    // Example of how to set a global alert (commented out by default)

    await firestore.collection('app_state').doc('global').update({
      'globalAlert': {
        'id': 'welcome-alert-${DateTime.now().millisecondsSinceEpoch}',
        'title': 'Welcome to Our App',
        'message':
            'Thank you for using our application. We hope you enjoy the experience!',
        'type': 'info',
        'dismissible': true,
        'actionText': 'Learn More',
        'actionUrl': 'https://example.com',
        'expiresAt': Timestamp.fromDate(
          DateTime.now().add(const Duration(days: 7)),
        ),
      },
    });
  }

  // Cache app state
  firestore.collection('app_state').doc('global').snapshots().listen((
    snapshot,
  ) {
    if (snapshot.exists) {
      SharedPreferences.getInstance().then((prefs) {
        prefs.setString('app_state', snapshot.data().toString());
      });
    }
  });
}
