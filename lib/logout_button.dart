import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'providers.dart';
import 'r_services.dart';
import 'widgets.dart';

class LogoutButton extends ConsumerWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final authService = RAuthService(
      ref.watch(authProvider),
      ref.watch(firestoreProvider),
    );
    return buildActionButton(
      context,
      'Logout',
      'Sign out from your account',
      Icons.logout_rounded,
      colorScheme.error,
      () {
        // Show confirmation dialog
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                title: const Text('Logout'),
                content: const Text('Are you sure you want to logout?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      'Cancel',
                      style: TextStyle(color: colorScheme.primary),
                    ),
                  ),
                  FilledButton(
                    onPressed: () async {
                      // Close the dialog first
                      Navigator.pop(context);
                      // Then sign out
                      await authService.signOut();
                      // No need to navigate manually as the auth state change should trigger navigation
                    },
                    style: FilledButton.styleFrom(
                      backgroundColor: colorScheme.error,
                      foregroundColor: colorScheme.onError,
                    ),
                    child: const Text('Logout'),
                  ),
                ],
              ),
        );
      },
    );
  }
}
