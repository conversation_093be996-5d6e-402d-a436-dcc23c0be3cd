import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RScafold extends ConsumerWidget {
  final Widget body;
  final String? title;
  final bool showBackButton;
  final Color? gradientStartColor;
  final Color? gradientEndColor;
  final List<Widget>? actions;

  const RScafold({
    super.key,
    required this.body,
    this.title,
    this.showBackButton = false,
    this.gradientStartColor,
    this.gradientEndColor,
    this.actions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                vertical: isTablet ? 30 : 15,
                horizontal: isTablet ? 32 : 20,
              ),
              child: Row(
                children: [
                  if (showBackButton && Navigator.canPop(context))
                    IconButton(
                      icon: Icon(Icons.arrow_back, color: colorScheme.primary),
                      onPressed: () => Navigator.pop(context),
                    ),
                  if (title != null)
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                                title!,
                                style: TextStyle(
                                  fontSize: isTablet ? 42 : 23,
                                  fontWeight: FontWeight.w900,
                                  color: colorScheme.primary,
                                  letterSpacing: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              )
                              .animate()
                              .fadeIn(duration: 600.ms, delay: 400.ms)
                              .moveY(begin: 20, end: 0),
                        ],
                      ),
                    ),
                  if (actions != null) ...[...actions!.map((e) => e)],
                ],
              ),
            ),
            Expanded(child: body),
          ],
        ),
      ),
    );
  }
}
