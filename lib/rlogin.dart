import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:rauth/r_scafold.dart';
import 'package:rauth/r_services.dart';
import 'package:rauth/rsign_up.dart';

import 'providers.dart';

class RLoginScreen extends ConsumerWidget {
  RLoginScreen({super.key});

  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authService = RAuthService(
      ref.watch(authProvider),
      ref.watch(firestoreProvider),
    );
    final colorScheme = Theme.of(context).colorScheme;

    return RScafold(
      title: "Login",
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Email Field
              TextField(
                controller: _emailController,
                style: TextStyle(color: colorScheme.primary),
                decoration: InputDecoration(
                  labelText: 'Email',
                  labelStyle: TextStyle(color: colorScheme.primary),
                  prefixIcon: Icon(
                    Icons.email_outlined,
                    color: colorScheme.primary,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.inversePrimary),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: colorScheme.primary),
                  ),
                ),
                keyboardType: TextInputType.emailAddress,
              ).animate().fadeIn(duration: 600.ms).moveY(begin: 20, end: 0),

              const SizedBox(height: 20),

              // Password Field
              TextField(
                    controller: _passwordController,
                    obscureText: true,
                    style: TextStyle(color: colorScheme.primary),
                    decoration: InputDecoration(
                      labelText: 'Password',
                      labelStyle: TextStyle(color: colorScheme.primary),
                      prefixIcon: Icon(
                        Icons.lock_outline,
                        color: colorScheme.primary,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: colorScheme.inversePrimary,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: colorScheme.primary),
                      ),
                    ),
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 200.ms)
                  .moveY(begin: 20, end: 0),

              const SizedBox(height: 28),

              // Login Button
              ElevatedButton.icon(
                    onPressed: () async {
                      try {
                        await authService.signIn(
                          _emailController.text.trim(),
                          _passwordController.text.trim(),
                        );
                      } catch (e) {
                        String errorMessage;

                        if (e is FirebaseAuthException) {
                          errorMessage = 'Invalid email or password';
                        } else if (e.toString().contains(
                          'This device is banned',
                        )) {
                          errorMessage = 'This device has been banned';
                        } else {
                          // Handle other exceptions
                          errorMessage = e.toString().split(':').last.trim();
                        }

                        Fluttertoast.showToast(
                          msg: errorMessage,
                          backgroundColor: Colors.red,
                          toastLength: Toast.LENGTH_LONG,
                        );

                        debugPrint(
                          'Login error: ${e.runtimeType} - $errorMessage',
                        );
                      }
                    },
                    icon: const Icon(Icons.login),
                    label: const Text('Login'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 400.ms)
                  .moveY(begin: 20, end: 0),

              const SizedBox(height: 16),

              // Signup Link
              TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => RSignUpScreen()),
                      );
                    },
                    child: Text(
                      'Don\'t have an account? Sign Up',
                      style: TextStyle(color: colorScheme.secondary),
                    ),
                  )
                  .animate()
                  .fadeIn(duration: 600.ms, delay: 600.ms)
                  .moveY(begin: 20, end: 0),
            ],
          ),
        ),
      ),
    );
  }
}
