// Providers
import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:flutter/foundation.dart' show kIsWeb;

import 'app_user.dart';
import 'models/app_version.dart';
import 'models/global_alert.dart';
import 'web_device_id.dart';
import 'package:package_info_plus/package_info_plus.dart';

// Providers
final authProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);
final firestoreProvider = Provider<FirebaseFirestore>(
  (ref) => FirebaseFirestore.instance,
);
final sharedPrefsProvider = FutureProvider<SharedPreferences>(
  (ref) => SharedPreferences.getInstance(),
);

//
final userProvider = StreamProvider<AppUser?>((ref) {
  final auth = ref.watch(authProvider);
  final firestore = ref.watch(firestoreProvider);

  return auth.authStateChanges().asBroadcastStream().asyncExpand((user) {
    debugPrint('Auth state changed: user=${user?.uid}');
    if (user == null) {
      return Stream.value(null);
    }

    return ref.read(deviceIdProvider.future).asStream().asyncExpand((deviceId) {
      debugPrint('Device ID: $deviceId');

      final docRef = firestore.collection('users').doc(user.uid);

      return docRef.get().asStream().asyncExpand((doc) async* {
        try {
          debugPrint('Firestore doc exists: ${doc.exists}');

          if (!doc.exists) {
            await docRef.set({
              'deviceId': deviceId,
              'lastUsed': FieldValue.serverTimestamp(),
            });
            debugPrint('Created new user document');
            yield null;
          } else {
            final data = doc.data()!;
            debugPrint('Firestore data: $data');

            // 🔥 Device ID Mismatch Fix: Yield null and return
            if (data['deviceId'] != deviceId && data['deviceId'] != null) {
              debugPrint('Device ID mismatch. Signing out.');
              await auth.signOut();
              yield null;
              return; // ✅ Stop further processing
            }

            // Yield the initial user object
            yield AppUser(
              uid: user.uid,
              role: data['role'] ?? 'tester',
              name: data['name'] ?? '',
              email: data['email'] ?? '',
              phone: data['phone'] ?? '',
              deviceId: data['deviceId'],
              lastUsed: (data['lastUsed'] as Timestamp?),
              testerExpiration: (data['testerExpiration'] as Timestamp?),
              banned: data['banned'],
              profilePictureUrl:
                  data['profilePictureUrl'] ??
                  "https://eu.ui-avatars.com/api/?name= ${Uri.encodeQueryComponent(data['name'] ?? '')}&size=50",
            );
          }

          // 🔁 Now listen to real-time updates
          yield* docRef.snapshots().map((snapshot) {
            if (!snapshot.exists) return null;

            final data = snapshot.data()!;
            debugPrint('User document updated: ${snapshot.id}');

            // 🔥 Device ID Mismatch Fix: Return null on mismatch
            if (data['deviceId'] != deviceId && data['deviceId'] != null) {
              debugPrint('Device ID mismatch in update. Signing out.');
              unawaited(auth.signOut()); // Non-blocking
              return null;
            }

            return AppUser(
              uid: user.uid,
              role: data['role'] ?? 'tester',
              name: data['name'] ?? '',
              email: data['email'] ?? '',
              phone: data['phone'] ?? '',
              deviceId: data['deviceId'],
              lastUsed: (data['lastUsed'] as Timestamp?),
              testerExpiration: (data['testerExpiration'] as Timestamp?),
              banned: data['banned'],
              profilePictureUrl:
                  data['profilePictureUrl'] ??
                  "https://eu.ui-avatars.com/api/?name= ${Uri.encodeQueryComponent(data['name'] ?? '')}&size=50",
            );
          });
        } catch (e, s) {
          debugPrint('userProvider error: $e\n$s');
          rethrow;
        }
      });
    });
  });
});

// App State Provider (now a StreamProvider)
final appStateProvider = StreamProvider<Map<String, dynamic>>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return firestore
      .collection('app_state')
      .doc('global')
      .snapshots()
      .map((snapshot) {
        if (!snapshot.exists) {
          debugPrint('App state document missing, returning default');
          return {
            'maintenanceMode': false,
            'shutdown': true,
            'globalAlert': null,
            'bannedDevices': <String>[],
          };
        }
        debugPrint('App state updated: ${snapshot.data()}');
        return snapshot.data()!;
      })
      .handleError((error) {
        debugPrint('appStateProvider error: $error');
        return {
          'maintenanceMode': false,
          'shutdown': true,
          'globalAlert': null,
          'bannedDevices': <String>[],
        };
      });
});

// Device ID Provider
final deviceIdProvider = FutureProvider<String>((ref) async {
  if (kIsWeb) {
    // For web, use a persistent UUID stored in SharedPreferences
    return await WebDeviceId.getWebDeviceId();
  } else {
    // For mobile, get the device ID
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    return androidInfo.id;
  }
});

// Check if the current device is banned
final isDeviceBannedProvider = FutureProvider<bool>((ref) async {
  final deviceId = await ref.watch(deviceIdProvider.future);
  final appState = await ref.watch(appStateProvider.future);

  // Check if bannedDevices exists and is a list
  final bannedDevices = appState['bannedDevices'];
  if (bannedDevices == null) return false;

  // Convert to List<String> if it's a List<dynamic>
  if (bannedDevices is List) {
    return bannedDevices.contains(deviceId);
  }

  return false;
});

// Global alert provider
final globalAlertProvider = Provider<GlobalAlert?>((ref) {
  final appState = ref.watch(appStateProvider).value;
  if (appState == null || appState['globalAlert'] == null) return null;

  final alertData = appState['globalAlert'];
  if (alertData is! Map<String, dynamic>) return null;

  final alert = GlobalAlert.fromMap(alertData);

  // Don't show expired alerts
  if (alert.isExpired) return null;

  return alert;
});

// Provider to track dismissed alerts
final dismissedAlertsProvider = StateProvider<Set<String>>((ref) => {});

// Provider to check if an alert should be shown
final shouldShowAlertProvider = Provider<bool>((ref) {
  final alert = ref.watch(globalAlertProvider);
  if (alert == null) return false;

  // If the alert is not dismissible, always show it
  if (!alert.dismissible) return true;

  // Check if the alert has been dismissed
  final dismissedAlerts = ref.watch(dismissedAlertsProvider);
  return !dismissedAlerts.contains(alert.id);
});

// App version provider
final appVersionProvider = StreamProvider<AppVersion?>((ref) {
  final firestore = ref.watch(firestoreProvider);
  return firestore
      .collection('app_state')
      .doc('versions')
      .snapshots()
      .map((snapshot) {
        if (!snapshot.exists || !snapshot.data()!.containsKey('latest')) {
          debugPrint('No app version document found');
          return null;
        }

        final versionData = snapshot.data()!['latest'];
        if (versionData == null) return null;

        return AppVersion.fromMap(versionData);
      })
      .handleError((error) {
        debugPrint('appVersionProvider error: $error');
        return null;
      });
});

// Current app version info provider
final currentAppVersionProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final packageInfo = await PackageInfo.fromPlatform();

  // Parse version code from version name if needed
  // This assumes semantic versioning like "1.2.3" and converts to 10203
  final versionParts = packageInfo.version.split('.');
  int versionCode = 0;

  if (versionParts.length >= 3) {
    try {
      final major = int.parse(versionParts[0]);
      final minor = int.parse(versionParts[1]);
      final patch = int.parse(
        versionParts[2].split('+').first,
      ); // Remove any build number

      versionCode = major * 10000 + minor * 100 + patch;
    } catch (e) {
      debugPrint('Error parsing version: $e');
      versionCode = 0;
    }
  }

  return {
    'versionName': packageInfo.version,
    'versionCode': versionCode,
    'buildNumber': packageInfo.buildNumber,
    'packageName': packageInfo.packageName,
    'appName': packageInfo.appName,
  };
});

// Provider to check if an update is available
final updateAvailableProvider = Provider<Map<String, dynamic>>((ref) {
  final appVersion = ref.watch(appVersionProvider).value;
  final currentVersion = ref.watch(currentAppVersionProvider).value;

  if (appVersion == null || currentVersion == null) {
    return {
      'updateAvailable': false,
      'isMandatory': false,
      'appVersion': null,
      'currentVersion': currentVersion,
    };
  }

  final currentVersionCode = currentVersion['versionCode'] as int;
  final updateRequired = appVersion.requiresUpdate(currentVersionCode);
  final updateAvailable = appVersion.updateAvailable(currentVersionCode);

  return {
    'updateAvailable': updateRequired || updateAvailable,
    'isMandatory': updateRequired,
    'appVersion': appVersion,
    'currentVersion': currentVersion,
  };
});

// Provider for the time left text for the current user
final timeLeftTextProvider = Provider<String>((ref) {
  final user = ref.watch(userProvider).value;

  // If there's no user or the user is not a tester, return an empty string
  if (user == null || user.role != 'tester') {
    return '';
  }

  // If the tester expiration is null, return 'Expired'
  if (user.testerExpiration == null) {
    return 'Expired';
  }

  // Calculate the time left
  final now = DateTime.now();
  final expiration = user.testerExpiration!.toDate();

  // If the account has expired, return 'Expired'
  if (now.isAfter(expiration)) {
    return 'Expired';
  }

  // Calculate the time left
  final difference = expiration.difference(now);
  final days = difference.inDays;
  final hours = difference.inHours % 24;
  final minutes = difference.inMinutes % 60;

  // Format the time left text
  if (days > 0) {
    return '$days days ${hours > 0 ? "${hours}h" : ""} left';
  } else if (hours > 0) {
    return '${hours}h ${minutes > 0 ? "${minutes}m" : ""} left';
  } else {
    return '${minutes}m left';
  }
});

// Stream provider for the time left text that updates every second
final liveTimeLeftTextProvider = StreamProvider<String>((ref) {
  // Get the current user
  final user = ref.watch(userProvider).value;

  // If there's no user or the user is not a tester, return an empty stream
  if (user == null || user.role != 'tester') {
    return Stream.value('VIP');
  }

  // If the tester expiration is null, return 'Expired'
  if (user.testerExpiration == null) {
    return Stream.value('Expired');
  }

  // Create a stream that emits a value every second
  return Stream.periodic(const Duration(seconds: 1), (_) {
    // Calculate the time left
    final now = DateTime.now();
    final expiration = user.testerExpiration!.toDate();

    // If the account has expired, return 'Expired'
    if (now.isAfter(expiration)) {
      return 'Expired';
    }

    // Calculate the time left
    final difference = expiration.difference(now);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;
    final seconds = difference.inSeconds % 60;

    // Format the time left text
    if (days > 0) {
      return '$days days ${hours > 0 ? "${hours}h" : ""} left';
    } else if (hours > 0) {
      return '${hours}h ${minutes > 0 ? "${minutes}m" : ""} left';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s left';
    } else {
      return '${seconds}s left';
    }
  });
});
