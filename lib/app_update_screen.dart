import 'dart:developer';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:rauth/models/app_version.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/services/app_update_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A screen that shows when an app update is available
class AppUpdateScreen extends ConsumerStatefulWidget {
  /// The child widget to display behind the update screen
  final Widget child;

  /// Whether to force the update (no dismiss option)
  final bool forceUpdate;

  const AppUpdateScreen({
    super.key,
    required this.child,
    this.forceUpdate = false,
  });

  @override
  ConsumerState<AppUpdateScreen> createState() => _AppUpdateScreenState();
}

class _AppUpdateScreenState extends ConsumerState<AppUpdateScreen> {
  static const String _lastDismissedVersionKey =
      'last_dismissed_update_version';
  bool _showUpdateDialog = true;

  @override
  void initState() {
    super.initState();
    _checkIfShouldShowUpdate();
  }

  /// Check if we should show the update dialog based on user preferences
  Future<void> _checkIfShouldShowUpdate() async {
    if (widget.forceUpdate) {
      setState(() {
        _showUpdateDialog = true;
      });
      return;
    }

    final updateInfo = ref.read(updateAvailableProvider);
    if (!updateInfo['updateAvailable']) {
      setState(() {
        _showUpdateDialog = false;
      });
      return;
    }

    final appVersion = updateInfo['appVersion'] as AppVersion?;
    if (appVersion == null) {
      setState(() {
        _showUpdateDialog = false;
      });
      return;
    }

    // Check if this version was previously dismissed
    final prefs = await SharedPreferences.getInstance();
    final lastDismissedVersion = prefs.getInt(_lastDismissedVersionKey) ?? 0;

    setState(() {
      _showUpdateDialog = lastDismissedVersion < appVersion.versionCode;
    });
  }

  /// Dismiss the update dialog and remember the version
  Future<void> _dismissUpdate() async {
    final updateInfo = ref.read(updateAvailableProvider);
    final appVersion = updateInfo['appVersion'] as AppVersion?;

    if (appVersion != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastDismissedVersionKey, appVersion.versionCode);
    }

    setState(() {
      _showUpdateDialog = false;
    });
  }

  // State for download progress
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String? _downloadError;

  /// Launch the update URL for the current platform
  Future<void> _launchUpdateUrl() async {
    final updateInfo = ref.read(updateAvailableProvider);
    final appVersion = updateInfo['appVersion'] as AppVersion?;

    if (appVersion == null) return;

    // Determine platform
    String platform =
        Theme.of(context).platform == TargetPlatform.iOS ? 'ios' : 'android';

    if (platform == 'android') {
      // For Android, download and install the APK automatically
      await _downloadAndInstallUpdate(appVersion);
    } else {
      // For iOS, launch the App Store URL
      final updateUrl = appVersion.getUpdateUrl(platform);

      if (updateUrl != null) {
        final url = Uri.parse(updateUrl);
        if (await canLaunchUrl(url)) {
          await launchUrl(url, mode: LaunchMode.externalApplication);
        }
      }
    }
  }

  /// Download and install the update for Android
  Future<void> _downloadAndInstallUpdate(AppVersion appVersion) async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _downloadError = null;
    });
    log('Downloading update...');

    try {
      // Start the download and listen to progress updates
      await for (final progress in AppUpdateService.downloadAndInstallUpdate(
        appVersion,
        onError: (error) {
          setState(() {
            _downloadError = error;
            _isDownloading = false;
          });
        },
        onSuccess: () {
          setState(() {
            _isDownloading = false;
          });
        },
      )) {
        setState(() {
          _downloadProgress = progress;
        });
      }
    } catch (e) {
      log('Error downloading update: ${e.toString()}');
      setState(() {
        _downloadError = 'Error: ${e.toString()}';
        _isDownloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // If we shouldn't show the update dialog, just show the child
    if (!_showUpdateDialog) {
      return widget.child;
    }

    final updateInfo = ref.watch(updateAvailableProvider);
    final appVersion = updateInfo['appVersion'] as AppVersion?;
    final isMandatory = updateInfo['isMandatory'] as bool;
    final currentVersion =
        updateInfo['currentVersion'] as Map<String, dynamic>?;

    // If there's no app version or current version, just show the child
    if (appVersion == null || currentVersion == null) {
      return widget.child;
    }

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final cardColor = isDarkMode ? Colors.grey.shade900 : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black;

    return Stack(
      children: [
        // The main content (blurred when update is shown)
        Positioned.fill(child: widget.child),

        // Update overlay with glass effect
        Positioned.fill(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.black.withAlpha(102), // ~0.4 opacity
                    Colors.black.withAlpha(51), // ~0.2 opacity
                  ],
                ),
              ),
              child: SafeArea(
                child: Center(
                  child: _buildUpdateCard(
                    context,
                    appVersion,
                    currentVersion,
                    isMandatory,
                    cardColor,
                    textColor,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateCard(
    BuildContext context,
    AppVersion appVersion,
    Map<String, dynamic> currentVersion,
    bool isMandatory,
    Color cardColor,
    Color textColor,
  ) {
    return Container(
      margin: const EdgeInsets.all(24),
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 500),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
        clipBehavior: Clip.antiAlias,
        child: Container(
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withAlpha(51), // ~0.2 opacity
                blurRadius: 20,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Update header with color strip
              Container(width: double.infinity, height: 8, color: Colors.blue),

              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Animation
                    Container(
                          height: 100,
                          width: 100,
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(30),
                            shape: BoxShape.circle,
                          ),
                          child: Lottie.asset(
                            'assets/lotties/update.json',
                            package: 'rauth',
                            repeat: true,
                            // Fallback if animation not found
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.system_update,
                                size: 60,
                                color: Colors.blue,
                              );
                            },
                          ),
                        )
                        .animate()
                        .fadeIn(duration: 500.ms)
                        .scale(
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1.0, 1.0),
                        ),

                    const SizedBox(height: 24),

                    // Title
                    Text(
                          isMandatory ? 'Update Required' : 'Update Available',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            color: Colors.blue,
                            letterSpacing: -0.5,
                          ),
                          textAlign: TextAlign.center,
                        )
                        .animate()
                        .fadeIn(delay: 200.ms, duration: 500.ms)
                        .moveY(begin: 10, end: 0),

                    const SizedBox(height: 16),

                    // Version info
                    Text(
                          'Version ${appVersion.versionName} is now available',
                          style: TextStyle(
                            fontSize: 16,
                            color: textColor,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        )
                        .animate()
                        .fadeIn(delay: 300.ms, duration: 500.ms)
                        .moveY(begin: 10, end: 0),

                    const SizedBox(height: 8),

                    // Current version
                    Text(
                          'You are currently using version ${currentVersion['versionName']}',
                          style: TextStyle(
                            fontSize: 14,
                            color: textColor.withAlpha(150),
                          ),
                          textAlign: TextAlign.center,
                        )
                        .animate()
                        .fadeIn(delay: 400.ms, duration: 500.ms)
                        .moveY(begin: 10, end: 0),

                    const SizedBox(height: 24),

                    // Release notes
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(20),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'What\'s New:',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: textColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            appVersion.releaseNotes,
                            style: TextStyle(
                              fontSize: 14,
                              color: textColor,
                              height: 1.5,
                            ),
                          ),
                        ],
                      ),
                    ).animate().fadeIn(delay: 500.ms, duration: 500.ms),

                    const SizedBox(height: 32),

                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (!isMandatory && !widget.forceUpdate)
                          TextButton(
                                onPressed: _dismissUpdate,
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.blue,
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Remind Me Later',
                                  style: TextStyle(fontWeight: FontWeight.w500),
                                ),
                              )
                              .animate()
                              .fadeIn(delay: 600.ms, duration: 500.ms)
                              .moveY(begin: 10, end: 0),

                        const SizedBox(width: 16),

                        _isDownloading
                            ? Column(
                              children: [
                                // Progress indicator
                                SizedBox(
                                  width: 150,
                                  child: LinearProgressIndicator(
                                    value: _downloadProgress,
                                    backgroundColor: Colors.blue.withAlpha(50),
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                    minHeight: 10,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Downloading... ${(_downloadProgress * 100).toInt()}%',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: textColor.withAlpha(200),
                                  ),
                                ),
                                if (_downloadError != null) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    _downloadError!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ],
                            )
                            : FilledButton(
                                  onPressed: _launchUpdateUrl,
                                  style: FilledButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 24,
                                      vertical: 12,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Update Now',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.3,
                                    ),
                                  ),
                                )
                                .animate()
                                .fadeIn(delay: 600.ms, duration: 500.ms)
                                .moveY(begin: 10, end: 0),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
