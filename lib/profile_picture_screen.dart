import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;

import 'app_user.dart';
import 'providers.dart';
import 'r_scafold.dart';
import 'r_services.dart';
import 'theme/app_colors.dart';
import 'theme/app_typography.dart';

/// A screen for changing the user's profile picture
class ProfilePictureScreen extends ConsumerStatefulWidget {
  /// The current user
  final AppUser user;

  const ProfilePictureScreen({
    super.key,
    required this.user,
  });

  @override
  ConsumerState<ProfilePictureScreen> createState() => _ProfilePictureScreenState();
}

class _ProfilePictureScreenState extends ConsumerState<ProfilePictureScreen> {
  // Image file for profile picture
  String? _imagePath;
  String? _imageUrl;
  XFile? _pickedImage;
  bool _isUploading = false;
  String? _uploadError;
  bool _uploadSuccess = false;

  // ImgBB API key - in a real app, this would be stored securely
  final String _imgbbApiKey = 'ef5af124088ca4d2825ae6a01f3ff5b4';
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final authService = RAuthService(
      ref.watch(authProvider),
      ref.watch(firestoreProvider),
    );

    return RScafold(
      title: "Change Profile Picture",
      showBackButton: true,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 16),
              
              // Current profile picture or initials
              _buildCurrentProfilePicture(colorScheme),
              
              const SizedBox(height: 24),
              
              // Image uploader
              _buildImageUploader(),
              
              const SizedBox(height: 24),
              
              // Upload status
              if (_uploadError != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: AppColors.error),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _uploadError!,
                          style: const TextStyle(color: AppColors.error),
                        ),
                      ),
                    ],
                  ),
                ),
              
              if (_uploadSuccess)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.check_circle_outline, color: AppColors.success),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'Profile picture updated successfully!',
                          style: TextStyle(color: AppColors.success),
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 32),
              
              // Save button
              ElevatedButton.icon(
                onPressed: _isUploading || _imageUrl == null
                    ? null
                    : () => _saveProfilePicture(authService),
                icon: const Icon(Icons.save),
                label: const Text('Save Profile Picture'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  minimumSize: const Size(double.infinity, 48),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Remove button (only if user has a profile picture)
              if (widget.user.profilePictureUrl != null || _imageUrl != null)
                OutlinedButton.icon(
                  onPressed: _isUploading
                      ? null
                      : () => _removeProfilePicture(authService),
                  icon: const Icon(Icons.delete_outline),
                  label: const Text('Remove Profile Picture'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.error,
                    side: const BorderSide(color: AppColors.error),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildCurrentProfilePicture(ColorScheme colorScheme) {
    // If we have a new image selected, show that
    if (_pickedImage != null && !_isUploading) {
      return Column(
        children: [
          CircleAvatar(
            radius: 60,
            backgroundImage: FileImage(File(_pickedImage!.path)),
          ),
          const SizedBox(height: 8),
          const Text('New Profile Picture'),
        ],
      );
    }
    
    // If user has an existing profile picture, show that

      return Column(
        children: [
         

          CircleAvatar(
          radius: 60,
          backgroundColor: colorScheme.primaryContainer,
          child: ClipOval(
            child: Image.network(
             widget. user.profilePictureUrl,
              fit: BoxFit.cover,
        
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Center(
                  child: SizedBox(
                   
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation(
                   colorScheme.onPrimary,
                      ),
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.account_circle,

                  color: colorScheme.onPrimary,
                );
              },
            ),
          ),
        ),



          const SizedBox(height: 8),
          const Text('Current Profile Picture'),
        ],
      );

    
    // Otherwise show initials
 
  }
  
  Widget _buildImageUploader() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _imagePath != null ? AppColors.success : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: _isUploading ? null : _pickImage,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 200,
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child: _isUploading
              ? const Center(child: CircularProgressIndicator())
              : _imagePath != null
                  ? _buildUploadedImagePreview()
                  : _buildImageUploadPlaceholder(),
        ),
      ),
    );
  }
  
  Widget _buildUploadedImagePreview() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Show image preview if available
        if (_pickedImage != null && !_isUploading) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(_pickedImage!.path),
              height: 100,
              width: 100,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                height: 100,
                width: 100,
                color: AppColors.backgroundLight,
                child: const Icon(
                  Icons.image,
                  size: 48,
                  color: AppColors.borderLight,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        
        // Status text
        Text(
          _uploadSuccess
              ? 'Image uploaded successfully!'
              : 'Tap to change image',
          style: AppTypography.bodyMedium.copyWith(
            color: _uploadSuccess ? AppColors.success : null,
            fontWeight: _uploadSuccess ? FontWeight.bold : null,
          ),
        ),
      ],
    );
  }
  
  Widget _buildImageUploadPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_a_photo,
          color: AppColors.primary.withAlpha(179),
          size: 48,
        ),
        const SizedBox(height: 16),
        Text(
          'Tap to upload profile picture',
          style: AppTypography.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Supported formats: JPG, PNG',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondaryLight,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
  
  Future<void> _pickImage() async {
    try {
      // Reset state
      setState(() {
        _isUploading = true;
        _uploadError = null;
        _uploadSuccess = false;
        _imageUrl = null;
      });

      // Pick image from gallery
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Reduce image quality to save bandwidth
      );

      // If user canceled the picker
      if (pickedImage == null) {
        setState(() {
          _isUploading = false;
        });
        return;
      }

      // Save the picked image
      _pickedImage = pickedImage;
      _imagePath = pickedImage.path;

      // Upload the image to ImgBB
      await _uploadImageToImgBB(pickedImage);
    } catch (e) {
      // Handle any errors
      setState(() {
        _isUploading = false;
        _uploadError = 'Error picking image: ${e.toString()}';
      });
    }
  }

  Future<void> _uploadImageToImgBB(XFile imageFile) async {
    try {
      // Read the image file as bytes
      final bytes = await imageFile.readAsBytes();

      // Create a multipart request for ImgBB
      final uri = Uri.parse('https://api.imgbb.com/1/upload');

      // Create form data with API key and image
      final request = http.MultipartRequest('POST', uri);
      request.fields['key'] = _imgbbApiKey;

      // Add the image file
      request.files.add(
        http.MultipartFile.fromBytes('image', bytes, filename: imageFile.name),
      );

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final imageUrl = jsonData['data']['url'];

        // Update state with success
        setState(() {
          _isUploading = false;
          _uploadSuccess = true;
          _imageUrl = imageUrl;
        });
      } else {
        throw Exception('Failed to upload image: ${response.statusCode}');
      }
    } catch (e) {
      // Handle upload errors
      setState(() {
        _isUploading = false;
        _uploadError = 'Error uploading image: ${e.toString()}';
      });
    }
  }
  
  Future<void> _saveProfilePicture(RAuthService authService) async {
    if (_imageUrl == null) return;
    
    try {
      // Show loading indicator
      setState(() {
        _isUploading = true;
      });
      
      // Update profile picture in Firestore
      await authService.updateProfile(
        profilePictureUrl: _imageUrl,
      );
      
      // Show success message
      setState(() {
        _isUploading = false;
        _uploadSuccess = true;
      });
      
      // Navigate back after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      });
    } catch (e) {
      setState(() {
        _isUploading = false;
        _uploadError = 'Error saving profile picture: ${e.toString()}';
      });
    }
  }
  
  Future<void> _removeProfilePicture(RAuthService authService) async {
    try {
      // Show loading indicator
      setState(() {
        _isUploading = true;
      });
      
      // Update profile picture in Firestore (set to null)
      await authService.updateProfile(
        profilePictureUrl: null,
      );
      
      // Show success message
      setState(() {
        _isUploading = false;
        _uploadSuccess = true;
        _imageUrl = null;
        _imagePath = null;
        _pickedImage = null;
      });
      
      // Navigate back after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      });
    } catch (e) {
      setState(() {
        _isUploading = false;
        _uploadError = 'Error removing profile picture: ${e.toString()}';
      });
    }
  }
  
  String _getInitials(String name) {
    if (name.isEmpty) return '';

    final nameParts = name.split(' ');
    if (nameParts.length == 1) {
      return nameParts[0][0].toUpperCase();
    }

    return nameParts[0][0].toUpperCase() + nameParts.last[0].toUpperCase();
  }
}
