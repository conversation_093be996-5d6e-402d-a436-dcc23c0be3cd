import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/app_user.dart';
import 'package:rauth/time_left_text.dart';
import 'package:rauth/time_left_widget.dart';

import '../providers.dart';

/// An example screen showing how to use the TimeLeftText widget
class TimeLeftExampleScreen extends ConsumerWidget {
  const TimeLeftExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Time Left Example'),
        // Example of using TimeLeftText in the app bar
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(30),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: TimeLeftText(
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              showWhenNotTester: true,
              notTesterText: 'Regular account (no expiration)',
              showWhenNotLoggedIn: true,
              notLoggedInText: 'Please log in',
            ),
          ),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Example of using TimeLeftText with custom styling
            const Card(
              margin: EdgeInsets.all(16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Account Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    TimeLeftText(
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                      showWhenNotTester: true,
                      notTesterText: 'Regular Account',
                      showWhenExpired: true,
                      expiredText: 'Account Expired!',
                    ),
                  ],
                ),
              ),
            ),
            
            // Example of using SimpleTimeLeftText with a specific user
            Consumer(
              builder: (context, ref, child) {
                final user = ref.watch(userProvider).value;
                if (user == null) {
                  return const Text('Not logged in');
                }
                
                return Card(
                  margin: const EdgeInsets.all(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Text(
                          'Using TimeLeftWidget',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        SimpleTimeLeftText(
                          user: user,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                          notTesterText: 'Regular Account',
                          expiredText: 'Account Expired!',
                          timeLeftFormat: '{d} days {h}:{m}:{s} remaining',
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            
            // Example of using TimeLeftWidget with a specific user
            Consumer(
              builder: (context, ref, child) {
                final user = ref.watch(userProvider).value;
                if (user == null) {
                  return const SizedBox.shrink();
                }
                
                return Card(
                  margin: const EdgeInsets.all(16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Text(
                          'Custom Time Format',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TimeLeftWidget(
                          user: user,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                          notTesterText: 'Regular Account',
                          expiredText: 'Account Expired!',
                          timeLeftFormat: 'Time left: {d}d {h}h {m}m {s}s',
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
