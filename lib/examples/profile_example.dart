import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/profile_button.dart';
import 'package:rauth/profile_screen.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/time_left_text.dart';

/// An example screen showing how to use the profile components
class ProfileExampleScreen extends ConsumerWidget {
  const ProfileExampleScreen({super.key});

  @override
  Widget build(BuildContext context,ref) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Example'),
        actions: [
          // Example of using ProfileButton in the app bar
          ProfileButton(
            color: colorScheme.onPrimary,
            showInitials: true,
            backgroundColor: colorScheme.primaryContainer,
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Example of using ProfileAvatar
            const ProfileAvatar(
              size: 80,
              showBorder: true,
              navigateToProfile: true,
            ),
            
            const SizedBox(height: 16),
            
            // Display user name
            Consumer(
              builder: (context, ref, child) {
                final user = ref.watch(userProvider).value;
                if (user == null) {
                  return const Text('Not logged in');
                }
                
                return Text(
                  'Welcome, ${user.name}!',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
            
            const SizedBox(height: 8),
            
            // Display time left for tester accounts
            const TimeLeftText(
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              showWhenNotTester: true,
              notTesterText: 'Regular Account',
              useLiveProvider: true,
            ),
            
            const SizedBox(height: 32),
            
            // Button to navigate to profile screen
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.person),
              label: const Text('View Profile'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// An example of how to add a profile button to a custom app bar
class CustomAppBarWithProfile extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final List<Widget>? actions;
  
  const CustomAppBarWithProfile({
    super.key,
    required this.title,
    this.showBackButton = false,
    this.actions,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return AppBar(
      title: Text(title),
      leading: showBackButton && Navigator.canPop(context)
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            )
          : null,
      actions: [
        ...?actions,
        // Add profile avatar to app bar
        const ProfileAvatar(
          size: 36,
          showBorder: true,
        ),
        const SizedBox(width: 16),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(30),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: TimeLeftText(
            style: TextStyle(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
            showWhenNotTester: false,
          ),
        ),
      ),
    );
  }
  
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 30);
}
