import 'dart:async';
import 'dart:io';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:rauth/models/app_version.dart'; // Your model remains unchanged

/// Service for handling app updates
class AppUpdateService {
  /// Download and install an APK file from the given URL
  /// Returns a stream of download progress (0.0 to 1.0)
  static Stream<double> downloadAndInstallUpdate(
    AppVersion appVersion, {
    required Function(String) onError,
    required VoidCallback onSuccess,
  }) {
    final progressController = StreamController<double>.broadcast();

    _startDownload(appVersion, progressController, onError, onSuccess);

    return progressController.stream;
  }

  static Future<void> _startDownload(
    AppVersion appVersion,
    StreamController<double> progressController,
    Function(String) onError,
    VoidCallback onSuccess,
  ) async {
    try {
      final androidUrl = appVersion.getUpdateUrl('android');
      if (androidUrl == null || androidUrl.isEmpty) {
        onError('No Android update URL available');
        return;
      }

      // Request necessary permissions
      final status = await Permission.manageExternalStorage.request();
      if (!status.isGranted) {
        onError('Storage permission denied.');
        return;
      }

      final installStatus = await Permission.requestInstallPackages.request();
      if (!installStatus.isGranted) {
        onError('Install packages permission denied.');
        return;
      }

      log('All checks passed. Starting download...');

      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'app_update_${appVersion.versionName}.apk';
      final filePath = '${directory.path}/$fileName';

      final dio = Dio();

      await dio.download(
        androidUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            progressController.add(progress);
          }
        },
      );

      await _installApk(filePath);

      onSuccess();
    } catch (e, s) {
      log('Error downloading or installing update', error: e, stackTrace: s);
      onError('Error processing update: $e');
    } finally {
      await progressController.close();
    }
  }

  
static Future<void> _installApk(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception("APK file not found at $filePath");
    }

    // Open the APK file using OpenFile
    final result = await OpenFile.open(filePath);

    if (result.type != ResultType.done) {
      throw Exception("Failed to install APK: ${result.message}");
    }
  }
}
