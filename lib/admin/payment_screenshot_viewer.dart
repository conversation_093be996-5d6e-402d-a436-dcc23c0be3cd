import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:photo_view/photo_view.dart';

import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// A screen for viewing payment screenshots with zoom capabilities
class PaymentScreenshotViewer extends StatefulWidget {
  /// The URL of the screenshot to display
  final String imageUrl;

  /// The payment ID or other identifier
  final String? paymentId;

  /// The user's name
  final String? userName;

  /// The payment amount and currency
  final String? paymentDetails;

  const PaymentScreenshotViewer({
    super.key,
    required this.imageUrl,
    this.paymentId,
    this.userName,
    this.paymentDetails,
  });

  @override
  State<PaymentScreenshotViewer> createState() =>
      _PaymentScreenshotViewerState();
}

class _PaymentScreenshotViewerState extends State<PaymentScreenshotViewer> {
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Pre-load the image
    WidgetsBinding.instance.addPostFrameCallback((_) {
      precacheImage(NetworkImage(widget.imageUrl), context)
          .then((_) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          })
          .catchError((error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'Failed to load image: ${error.toString()}';
              });
            }
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Screenshot'),
        actions: [
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            tooltip: 'Open in browser',
            onPressed: _openInBrowser,
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            tooltip: 'Copy URL',
            onPressed: _copyUrlToClipboard,
          ),
        ],
      ),
      body: Column(
        children: [
          // Payment info header
          if (widget.userName != null ||
              widget.paymentId != null ||
              widget.paymentDetails != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: AppColors.backgroundLight,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.userName != null)
                    Text(
                      widget.userName!,
                      style: AppTypography.subtitle.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  if (widget.paymentDetails != null)
                    Text(
                      widget.paymentDetails!,
                      style: AppTypography.bodyMedium,
                    ),
                  if (widget.paymentId != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'ID: ${widget.paymentId}',
                            style: AppTypography.bodySmall.copyWith(
                              fontFamily: 'monospace',
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.copy, size: 16),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          tooltip: 'Copy ID',
                          onPressed: () => _copyToClipboard(widget.paymentId!),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

          // Image viewer
          Expanded(child: _buildImageViewer()),
        ],
      ),
    );
  }

  Widget _buildImageViewer() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading image...'),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: AppColors.error),
              const SizedBox(height: 16),
              Text(
                'Error Loading Image',
                style: AppTypography.subtitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage ?? 'Unknown error',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _openInBrowser,
                icon: const Icon(Icons.open_in_browser),
                label: const Text('Open in Browser'),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: Colors.black,
      child: PhotoView(
        imageProvider: NetworkImage(widget.imageUrl),
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.covered * 2,
        backgroundDecoration: const BoxDecoration(color: Colors.black),
        loadingBuilder:
            (context, event) => Center(
              child: CircularProgressIndicator(
                value:
                    event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                            (event.expectedTotalBytes ?? 1),
              ),
            ),
        errorBuilder:
            (context, error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 42,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading image',
                    style: AppTypography.bodyMedium.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  void _openInBrowser() async {
    final url = Uri.parse(widget.imageUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open the image URL'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _copyUrlToClipboard() {
    _copyToClipboard(widget.imageUrl);
  }

  void _copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Copied to clipboard'),
          backgroundColor: AppColors.success,
          duration: Duration(seconds: 1),
        ),
      );
    }
  }
}
