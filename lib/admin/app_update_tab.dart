import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../models/app_version.dart';
import '../providers.dart';
import '../theme/app_colors.dart';

/// Admin tab for managing app updates
class AppUpdateTab extends ConsumerStatefulWidget {
  const AppUpdateTab({super.key});

  @override
  ConsumerState<AppUpdateTab> createState() => _AppUpdateTabState();
}

class _AppUpdateTabState extends ConsumerState<AppUpdateTab> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isEditing = false;

  // Form controllers
  final _versionNameController = TextEditingController();
  final _versionCodeController = TextEditingController();
  final _releaseNotesController = TextEditingController();
  final _androidUrlController = TextEditingController();
  bool _isMandatory = false;
  final _minRequiredVersionController = TextEditingController();
  DateTime? _releaseDate;

  // Current version
  AppVersion? _currentVersion;

  @override
  void initState() {
    super.initState();
    _loadCurrentVersion();
  }

  @override
  void dispose() {
    _versionNameController.dispose();
    _versionCodeController.dispose();
    _releaseNotesController.dispose();
    _androidUrlController.dispose();
    _minRequiredVersionController.dispose();
    super.dispose();
  }

  /// Load the current app version from the provider
  void _loadCurrentVersion() {
    final version = ref.read(appVersionProvider).value;
    if (version != null) {
      setState(() {
        _currentVersion = version;
      });
    }
  }

  /// Reset the form fields
  void _resetForm() {
    _versionNameController.clear();
    _versionCodeController.clear();
    _releaseNotesController.clear();
    _androidUrlController.clear();
    _minRequiredVersionController.clear();
    _isMandatory = false;
    _releaseDate = null;
    setState(() {
      _isEditing = false;
      _errorMessage = null;
    });
  }

  /// Load the current version into the form for editing
  void _editCurrentVersion() {
    if (_currentVersion == null) return;

    _versionNameController.text = _currentVersion!.versionName;
    _versionCodeController.text = _currentVersion!.versionCode.toString();
    _releaseNotesController.text = _currentVersion!.releaseNotes;
    _androidUrlController.text = _currentVersion!.updateUrls['android'] ?? '';
    _isMandatory = _currentVersion!.isMandatory;
    _minRequiredVersionController.text =
        _currentVersion!.minRequiredVersionCode?.toString() ?? '';
    _releaseDate = _currentVersion!.releaseDate;

    setState(() {
      _isEditing = true;
      _errorMessage = null;
    });
  }

  /// Save the version to Firestore
  Future<void> _saveVersion() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Parse version code
      final versionCode = int.parse(_versionCodeController.text.trim());

      // Parse min required version code if provided
      int? minRequiredVersionCode;
      if (_minRequiredVersionController.text.isNotEmpty) {
        minRequiredVersionCode = int.parse(
          _minRequiredVersionController.text.trim(),
        );
      }

      // Create the update URLs map
      final updateUrls = <String, String>{};
      if (_androidUrlController.text.isNotEmpty) {
        updateUrls['android'] = _androidUrlController.text.trim();
      }

      // Create the version data
      final versionData = {
        'versionName': _versionNameController.text.trim(),
        'versionCode': versionCode,
        'releaseNotes': _releaseNotesController.text.trim(),
        'isMandatory': _isMandatory,
        'minRequiredVersionCode': minRequiredVersionCode,
        'updateUrls': updateUrls,
        'releaseDate':
            _releaseDate != null
                ? Timestamp.fromDate(_releaseDate!)
                : Timestamp.fromDate(DateTime.now()),
      };

      // Update the app_state document
      await firestore.collection('app_state').doc('versions').set({
        'latest': versionData,
      }, SetOptions(merge: true));

      // Reset the form
      _resetForm();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('App version saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving version: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Delete the current version
  Future<void> _deleteVersion() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Set the latest field to null
      await firestore.collection('app_state').doc('versions').update({
        'latest': null,
      });

      // Reset the form and current version
      _resetForm();
      setState(() {
        _currentVersion = null;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('App version deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error deleting version: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _selectReleaseDate(BuildContext context) {
    // Use a separate method to handle the async part
    _showDatePicker(context);
  }

  Future<void> _showDatePicker(BuildContext context) async {
    final initialDate = _releaseDate ?? DateTime.now();

    // Show date picker
    if (!mounted) return;
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now().subtract(Duration(days: 365)),
      lastDate: DateTime.now().add(Duration(days: 365)),
    );

    if (pickedDate != null && mounted) {
      setState(() {
        _releaseDate = pickedDate;
      });
    }
  }

  Future<void> _showDeleteConfirmation(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Delete Version'),
          content: SingleChildScrollView(
            child: Text(
              'Are you sure you want to delete this app version? This action cannot be undone.',
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteVersion();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Watch for changes to the app version
    final version = ref.watch(appVersionProvider).value;
    if (version != _currentVersion) {
      setState(() {
        _currentVersion = version;
      });
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'App Update Management',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          SizedBox(height: 16),
          _buildCurrentVersionCard(),
          SizedBox(height: 24),
          _buildVersionForm(),
        ],
      ),
    );
  }

  Widget _buildCurrentVersionCard() {
    if (_currentVersion == null) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'No Active Version',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 8),
              Text(
                'There is currently no active app version. Use the form below to create one.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    // Format release date
    final formatter = DateFormat('MMM d, yyyy');
    final releaseDateText = formatter.format(_currentVersion!.releaseDate);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.blue, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(25), // ~0.1 opacity
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.system_update, color: Colors.blue),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Current App Version',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                if (!_isEditing) ...[
                  IconButton(
                    icon: Icon(Icons.edit),
                    tooltip: 'Edit Version',
                    onPressed: _editCurrentVersion,
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    tooltip: 'Delete Version',
                    onPressed: () => _showDeleteConfirmation(context),
                  ),
                ],
              ],
            ),
          ),

          // Version details
          Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Version ${_currentVersion!.versionName} (${_currentVersion!.versionCode})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: 16),
                _buildVersionProperty(
                  'Type',
                  _currentVersion!.isMandatory ? 'Mandatory' : 'Optional',
                ),
                if (_currentVersion!.minRequiredVersionCode != null)
                  _buildVersionProperty(
                    'Min Required',
                    _currentVersion!.minRequiredVersionCode.toString(),
                  ),
                _buildVersionProperty('Release Date', releaseDateText),
                SizedBox(height: 16),
                Text(
                  'Release Notes:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(_currentVersion!.releaseNotes),
                ),
                SizedBox(height: 16),
                Text(
                  'Update URLs:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                if (_currentVersion!.updateUrls.containsKey('android'))
                  _buildVersionProperty(
                    'Android',
                    _currentVersion!.updateUrls['android']!,
                  ),
                if (_currentVersion!.updateUrls.containsKey('ios'))
                  _buildVersionProperty(
                    'iOS',
                    _currentVersion!.updateUrls['ios']!,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVersionProperty(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildVersionForm() {
    return Form(
      key: _formKey,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isEditing ? 'Edit App Version' : 'Create New App Version',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 16),

              // Version name field
              TextFormField(
                controller: _versionNameController,
                decoration: InputDecoration(
                  labelText: 'Version Name',
                  hintText: 'e.g., 1.2.3',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a version name';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Version code field
              TextFormField(
                controller: _versionCodeController,
                decoration: InputDecoration(
                  labelText: 'Version Code',
                  hintText: 'e.g., 10203 (for version 1.2.3)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a version code';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Release notes field
              TextFormField(
                controller: _releaseNotesController,
                decoration: InputDecoration(
                  labelText: 'Release Notes',
                  hintText: 'What\'s new in this version?',
                  border: OutlineInputBorder(),
                ),
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter release notes';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Android URL field (APK download URL)
              TextFormField(
                controller: _androidUrlController,
                decoration: InputDecoration(
                  labelText: 'Android APK Download URL',
                  hintText: 'e.g., https://example.com/downloads/app.apk',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an APK download URL';
                  }
                  final urlPattern = RegExp(
                    r'^(https?:\/\/)?[\w\.-]+\.[a-z]{2,}(\/.*)?$',
                  );
                  if (!urlPattern.hasMatch(value)) {
                    return 'Please enter a valid URL';
                  }
                  // if (!value.trim().toLowerCase().endsWith('.apk')) {
                  //   return 'URL should point to an APK file';
                  // }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Mandatory checkbox
              CheckboxListTile(
                title: Text('Mandatory Update'),
                subtitle: Text('Force users to update to this version'),
                value: _isMandatory,
                onChanged: (value) {
                  setState(() {
                    _isMandatory = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              SizedBox(height: 16),

              // Min required version field
              TextFormField(
                controller: _minRequiredVersionController,
                decoration: InputDecoration(
                  labelText: 'Minimum Required Version Code (Optional)',
                  hintText: 'e.g., 10100 (for version 1.1.0)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    if (int.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Release date picker
              InkWell(
                onTap: () => _selectReleaseDate(context),
                child: InputDecorator(
                  decoration: InputDecoration(
                    labelText: 'Release Date',
                    border: OutlineInputBorder(),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _releaseDate != null
                            ? DateFormat('MMM d, yyyy').format(_releaseDate!)
                            : 'Select a date',
                      ),
                      Icon(Icons.calendar_today),
                    ],
                  ),
                ),
              ),

              // Error message
              if (_errorMessage != null) ...[
                SizedBox(height: 16),
                Text(_errorMessage!, style: TextStyle(color: Colors.red)),
              ],

              SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : _resetForm,
                    child: Text('Cancel'),
                  ),
                  SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveVersion,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isLoading
                            ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : Text(
                              _isEditing ? 'Update Version' : 'Create Version',
                            ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
