
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

import '../providers.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import 'payment_screenshot_viewer.dart';

/// Payment verification tab for the admin panel
class PaymentVerificationTab extends ConsumerStatefulWidget {
  const PaymentVerificationTab({super.key});

  @override
  ConsumerState<PaymentVerificationTab> createState() =>
      _PaymentVerificationTabState();
}

class _PaymentVerificationTabState
    extends ConsumerState<PaymentVerificationTab> {
  bool _isLoading = true;
  String? _errorMessage;
  List<Map<String, dynamic>> _payments = [];
  String _filterStatus = 'pending';

  @override
  void initState() {
    super.initState();
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Create query based on filter
      Query query = firestore.collection('payments');

      // We need to handle the query differently to avoid the composite index error
      if (_filterStatus != 'all') {
        // When filtering by status, we'll get all documents with that status
        // without ordering in the query to avoid needing a composite index
        query = query.where(
          'status',
          isEqualTo:
              _filterStatus == 'pending' ? 'pending approval' : _filterStatus,
        );
      } else {
        // If not filtering by status, we can use orderBy
        query = query.orderBy('submittedAt', descending: true);
      }

      final snapshot = await query.get();

      // Convert to list of maps
      final payments =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            // Add document ID to data
            data['docId'] = doc.id;
            return data;
          }).toList();

      // Sort the payments by submittedAt if we're filtering by status
      // (since we couldn't use orderBy in the query to avoid index issues)
      if (_filterStatus != 'all') {
        payments.sort((a, b) {
          // Handle missing submittedAt values
          if (a['submittedAt'] == null && b['submittedAt'] == null) {
            return 0;
          }
          if (a['submittedAt'] == null) {
            return 1; // null values go last
          }
          if (b['submittedAt'] == null) {
            return -1; // null values go last
          }

          // Compare timestamps (descending order)
          final aTimestamp = a['submittedAt'] as Timestamp;
          final bTimestamp = b['submittedAt'] as Timestamp;
          return bTimestamp.compareTo(aTimestamp);
        });
      }

      setState(() {
        _payments = payments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading payments: ${e.toString()}';
      });
    }
  }

  Future<void> _updatePaymentStatus(
    String docId,
    String status,
    String userId,
  ) async {
    try {
      final firestore = ref.read(firestoreProvider);
      final currentUser = ref.read(userProvider).value;

      if (currentUser == null) {
        throw Exception('You must be logged in to perform this action');
      }

      // Get the payment document
      final paymentDoc =
          await firestore.collection('payments').doc(docId).get();
      if (!paymentDoc.exists) {
        throw Exception('Payment not found');
      }

      final paymentData = paymentDoc.data() as Map<String, dynamic>;

      // Update payment status
      await firestore.collection('payments').doc(docId).update({
        'status': status,
        'reviewedAt': FieldValue.serverTimestamp(),
        'reviewedBy': currentUser.uid,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // If approved, update user's subscription
      if (status == 'approved') {
        // Calculate expiration date
        final planDuration = paymentData['planDuration'] as int;
        DateTime? expirationDate;

        if (planDuration > 0) {
          expirationDate = DateTime.now().add(Duration(days: planDuration));
        }

        // Update user document
        await firestore.collection('users').doc(userId).update({
          'role': 'tester',
          'testerExpiration': Timestamp.fromDate(expirationDate!),
          'hasPaymentPending': false,
        });
      } else if (status == 'rejected') {
        // Update user document to remove pending status
        await firestore.collection('users').doc(userId).update({
          'hasPaymentPending': false,
        });
      }

      // Reload payments
      await _loadPayments();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Payment ${status == 'approved' ? 'approved' : 'rejected'} successfully',
            ),
            backgroundColor:
                status == 'approved' ? AppColors.success : AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Log error to console but don't show print in production
        debugPrint('Error updating payment status: ${e.toString()}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildFilterChips(),
          const SizedBox(height: 16),
          Expanded(child: _buildPaymentsList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Verifications',
              style: AppTypography.headline.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Manage payment submissions',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
        IconButton(
          onPressed: _loadPayments,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Wrap(
      spacing: 8,
      children: [
        FilterChip(
          label: const Text('Pending'),
          selected: _filterStatus == 'pending',
          onSelected: (selected) {
            setState(() {
              _filterStatus = 'pending';
            });
            _loadPayments();
          },
          backgroundColor: AppColors.backgroundLight,
          selectedColor: AppColors.warning.withOpacity(0.2),
          checkmarkColor: AppColors.warning,
          labelStyle: TextStyle(
            color: _filterStatus == 'pending' ? AppColors.warning : null,
            fontWeight: _filterStatus == 'pending' ? FontWeight.bold : null,
          ),
        ),
        FilterChip(
          label: const Text('Approved'),
          selected: _filterStatus == 'approved',
          onSelected: (selected) {
            setState(() {
              _filterStatus = 'approved';
            });
            _loadPayments();
          },
          backgroundColor: AppColors.backgroundLight,
          selectedColor: AppColors.success.withOpacity(0.2),
          checkmarkColor: AppColors.success,
          labelStyle: TextStyle(
            color: _filterStatus == 'approved' ? AppColors.success : null,
            fontWeight: _filterStatus == 'approved' ? FontWeight.bold : null,
          ),
        ),
        FilterChip(
          label: const Text('Rejected'),
          selected: _filterStatus == 'rejected',
          onSelected: (selected) {
            setState(() {
              _filterStatus = 'rejected';
            });
            _loadPayments();
          },
          backgroundColor: AppColors.backgroundLight,
          selectedColor: AppColors.error.withOpacity(0.2),
          checkmarkColor: AppColors.error,
          labelStyle: TextStyle(
            color: _filterStatus == 'rejected' ? AppColors.error : null,
            fontWeight: _filterStatus == 'rejected' ? FontWeight.bold : null,
          ),
        ),
        FilterChip(
          label: const Text('All'),
          selected: _filterStatus == 'all',
          onSelected: (selected) {
            setState(() {
              _filterStatus = 'all';
            });
            _loadPayments();
          },
          backgroundColor: AppColors.backgroundLight,
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary,
          labelStyle: TextStyle(
            color: _filterStatus == 'all' ? AppColors.primary : null,
            fontWeight: _filterStatus == 'all' ? FontWeight.bold : null,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentsList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: AppColors.error),
            const SizedBox(height: 16),
            Text(
              'Error Loading Payments',
              style: AppTypography.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(_errorMessage!, textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadPayments,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_payments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment_outlined,
              size: 64,
              color: AppColors.textSecondaryLight.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No payments found',
              style: AppTypography.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _filterStatus == 'all'
                  ? 'There are no payment submissions yet'
                  : 'There are no ${_filterStatus == 'pending' ? 'pending' : _filterStatus} payments',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _payments.length,
      itemBuilder: (context, index) {
        final payment = _payments[index];
        return _buildPaymentCard(payment);
      },
    );
  }

  Widget _buildPaymentCard(Map<String, dynamic> payment) {
    final status = payment['status'] as String;
    final planName = payment['planName'] as String;
    final amount = payment['amount'] as int;
    final currency = payment['currency'] as String? ?? 'MMK';
    final userName = payment['userName'] as String;
    final userEmail = payment['userEmail'] as String;
    final userId = payment['userId'] as String;
    final docId = payment['docId'] as String;
    final screenshotUrl = payment['screenshotUrl'] as String?;

    // Format amount with commas
    final formattedAmount = NumberFormat('#,###').format(amount);

    // Format submission date
    String submittedDate = 'Unknown';
    if (payment['submittedAt'] != null) {
      try {
        final timestamp = payment['submittedAt'] as Timestamp;
        submittedDate = DateFormat(
          'MMM d, yyyy h:mm a',
        ).format(timestamp.toDate());
      } catch (e) {
        // Ignore date parsing errors
      }
    }

    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case 'pending approval':
        statusColor = AppColors.warning;
        statusIcon = Icons.pending_outlined;
        break;
      case 'approved':
        statusColor = AppColors.success;
        statusIcon = Icons.check_circle_outline;
        break;
      case 'rejected':
        statusColor = AppColors.error;
        statusIcon = Icons.cancel_outlined;
        break;
      default:
        statusColor = AppColors.textSecondaryLight;
        statusIcon = Icons.help_outline;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: statusColor.withOpacity(0.3), width: 1),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: statusColor.withOpacity(0.1),
          child: Icon(statusIcon, color: statusColor),
        ),
        title: Text(
          '$planName - $formattedAmount $currency',
          style: AppTypography.subtitle.copyWith(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(userName, style: AppTypography.bodyMedium),
            Text(
              'Submitted: $submittedDate',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
        trailing:
            status == 'pending approval'
                ? const Icon(Icons.expand_more)
                : Chip(
                  label: Text(
                    status == 'pending approval'
                        ? 'Pending'
                        : status.substring(0, 1).toUpperCase() +
                            status.substring(1),
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  backgroundColor: statusColor.withOpacity(0.1),
                  padding: EdgeInsets.zero,
                  visualDensity: VisualDensity.compact,
                ),
        children: [
          if (screenshotUrl != null) ...[
            Row(
              children: [
                const Icon(
                  Icons.image_outlined,
                  size: 16,
                  color: AppColors.textSecondaryLight,
                ),
                const SizedBox(width: 8),
                const Text('Payment Screenshot:'),
                const Spacer(),
                TextButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder:
                            (context) => PaymentScreenshotViewer(
                              imageUrl: screenshotUrl,
                              userName: userName,
                              paymentId: docId,
                              paymentDetails:
                                  '$planName - $formattedAmount $currency',
                            ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.image, size: 16),
                  label: const Text('View Image'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    visualDensity: VisualDensity.compact,
                  ),
                ),
              ],
            ),
            const Divider(),
          ],
          Row(
            children: [
              const Icon(
                Icons.email_outlined,
                size: 16,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  userEmail,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.person_outline,
                size: 16,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'User ID: $userId',
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ),
          if (status == 'pending approval') ...[
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    _showRejectConfirmation(docId, userId);
                  },
                  icon: const Icon(Icons.cancel, size: 16),
                  label: const Text('Reject'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.error,
                    side: const BorderSide(color: AppColors.error),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    _showApproveConfirmation(docId, userId, planName);
                  },
                  icon: const Icon(Icons.check, size: 16),
                  label: const Text('Approve'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _showApproveConfirmation(String docId, String userId, String planName) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Approve Payment'),
            content: Text(
              'Are you sure you want to approve this payment for $planName? This will update the user\'s subscription status.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _updatePaymentStatus(docId, 'approved', userId);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Approve'),
              ),
            ],
          ),
    );
  }

  void _showRejectConfirmation(String docId, String userId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Reject Payment'),
            content: const Text(
              'Are you sure you want to reject this payment? The user will need to submit a new payment.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _updatePaymentStatus(docId, 'rejected', userId);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reject'),
              ),
            ],
          ),
    );
  }
}
