import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

import '../app_user.dart';
import '../providers.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// User management tab for the admin panel
class UserManagementTab extends ConsumerStatefulWidget {
  const UserManagementTab({super.key});

  @override
  ConsumerState<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends ConsumerState<UserManagementTab> {
  bool _isLoading = true;
  String? _errorMessage;
  List<AppUser> _users = [];
  String _filterRole = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Create query based on filter
      Query query = firestore.collection('users');

      // We need to handle the query differently based on whether we're filtering by role
      // to avoid the need for a composite index
      if (_filterRole != 'all') {
        // When filtering by role, we'll get all documents with that role
        // and sort them in memory to avoid needing a composite index
        query = query.where('role', isEqualTo: _filterRole);
      } else {
        // If not filtering by role, we can use orderBy
        query = query.orderBy('name');
      }

      final snapshot = await query.get();

      // Convert to AppUser objects
      final users =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;

            // Handle Timestamp conversions properly
            Timestamp? lastUsed;
            if (data['lastUsed'] is Timestamp) {
              lastUsed = data['lastUsed'] as Timestamp;
            }

            Timestamp? testerExpiration;
            if (data['testerExpiration'] is Timestamp) {
              testerExpiration = data['testerExpiration'] as Timestamp;
            } else if (data['testerExpiration'] is String) {
              // If testerExpiration is stored as ISO string, convert to Timestamp
              try {
                final date = DateTime.parse(data['testerExpiration'] as String);
                testerExpiration = Timestamp.fromDate(date);
              } catch (e) {
                // Ignore parsing errors
              }
            }

            return AppUser(
              uid: doc.id,
              name: data['name'] ?? '',
              email: data['email'] ?? '',
              phone: data['phone'] ?? '',
              role: data['role'] ?? '',
              deviceId: data['deviceId'],
              lastUsed: lastUsed,
              testerExpiration: testerExpiration,
              banned: data['banned'],
              profilePictureUrl:
                  data['profilePictureUrl'] ??
                  "https://eu.ui-avatars.com/api/?name=${Uri.encodeQueryComponent(data['name'] ?? '')}&size=50",
            );
          }).toList();

      // Sort users by name if we're filtering by role
      // (since we couldn't use orderBy in the query to avoid index issues)
      if (_filterRole != 'all') {
        users.sort((a, b) => a.name.compareTo(b.name));
      }

      // Apply search filter if needed
      final filteredUsers =
          _searchQuery.isEmpty
              ? users
              : users.where((user) {
                final query = _searchQuery.toLowerCase();
                return user.name.toLowerCase().contains(query) ||
                    user.email.toLowerCase().contains(query) ||
                    user.phone.toLowerCase().contains(query) ||
                    user.uid.toLowerCase().contains(query);
              }).toList();

      setState(() {
        _users = filteredUsers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading users: ${e.toString()}';
      });
    }
  }

  Future<void> _updateUserRole(String userId, String newRole) async {
    try {
      final firestore = ref.read(firestoreProvider);

      // Update user role
      await firestore.collection('users').doc(userId).update({'role': newRole});

      // Reload users
      await _loadUsers();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('User role updated to $newRole'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleUserBan(String userId, bool currentBanStatus) async {
    try {
      final firestore = ref.read(firestoreProvider);

      // Update user ban status
      await firestore.collection('users').doc(userId).update({
        'banned': !currentBanStatus,
      });

      // Reload users
      await _loadUsers();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(currentBanStatus ? 'User unbanned' : 'User banned'),
            backgroundColor:
                currentBanStatus ? AppColors.success : AppColors.warning,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _toggleDeviceBan(String deviceId, bool isBanned) async {
    if (deviceId.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No device ID available to ban'),
            backgroundColor: AppColors.error,
          ),
        );
      }
      return;
    }

    try {
      final firestore = ref.read(firestoreProvider);
      final appStateDoc =
          await firestore.collection('app_state').doc('global').get();

      if (!appStateDoc.exists) {
        throw Exception('App state document not found');
      }

      final appState = appStateDoc.data()!;
      List<dynamic> bannedDevices = List.from(appState['bannedDevices'] ?? []);

      if (isBanned) {
        // Remove device from banned list
        bannedDevices.remove(deviceId);
      } else {
        // Add device to banned list if not already present
        if (!bannedDevices.contains(deviceId)) {
          bannedDevices.add(deviceId);
        }
      }

      // Update the bannedDevices list in Firestore
      await firestore.collection('app_state').doc('global').update({
        'bannedDevices': bannedDevices,
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isBanned ? 'Device unbanned' : 'Device banned'),
            backgroundColor: isBanned ? AppColors.success : AppColors.warning,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<bool> _isDeviceBanned(String deviceId) async {
    if (deviceId.isEmpty) return false;

    try {
      final firestore = ref.read(firestoreProvider);
      final appStateDoc =
          await firestore.collection('app_state').doc('global').get();

      if (!appStateDoc.exists) return false;

      final appState = appStateDoc.data()!;
      final bannedDevices = appState['bannedDevices'];

      if (bannedDevices == null || bannedDevices is! List) return false;

      return bannedDevices.contains(deviceId);
    } catch (e) {
      debugPrint('Error checking if device is banned: ${e.toString()}');
      return false;
    }
  }

  Future<void> _updateTesterExpiration(
    String userId,
    DateTime? newExpiration,
  ) async {
    try {
      final firestore = ref.read(firestoreProvider);

      // Update user tester expiration
      await firestore.collection('users').doc(userId).update({
        'testerExpiration': Timestamp.fromDate(newExpiration?? DateTime.now()),
      });

      // Reload users
      await _loadUsers();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newExpiration == null
                  ? 'Tester expiration removed'
                  : 'Tester expiration updated to ${DateFormat('MMM d, yyyy').format(newExpiration)}',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildSearchAndFilter(),
          const SizedBox(height: 16),
          Expanded(child: _buildUsersList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Management',
              style: AppTypography.headline.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Manage user accounts and permissions',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
        IconButton(
          onPressed: _loadUsers,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
    );
  }

  Widget _buildSearchAndFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search users...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon:
                _searchQuery.isNotEmpty
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                        _loadUsers();
                      },
                    )
                    : null,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _loadUsers();
          },
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildRoleFilterChip('All', 'all'),
              const SizedBox(width: 8),
              _buildRoleFilterChip('Admins', 'admin'),
              const SizedBox(width: 8),
              _buildRoleFilterChip('Testers', 'tester'),
              const SizedBox(width: 8),
              _buildRoleFilterChip('Users', 'user'),
              const SizedBox(width: 8),
              _buildRoleFilterChip('Agents', 'user'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRoleFilterChip(String label, String role) {
    return FilterChip(
      label: Text(label),
      selected: _filterRole == role,
      onSelected: (selected) {
        setState(() {
          _filterRole = role;
        });
        _loadUsers();
      },
      backgroundColor: AppColors.backgroundLight,
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: _filterRole == role ? AppColors.primary : null,
        fontWeight: _filterRole == role ? FontWeight.bold : null,
      ),
    );
  }

  Widget _buildUsersList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: AppColors.error),
            const SizedBox(height: 16),
            Text(
              'Error Loading Users',
              style: AppTypography.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(_errorMessage!, textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadUsers,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.textSecondaryLight.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No users found',
              style: AppTypography.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty
                  ? 'No users match your search criteria'
                  : _filterRole == 'all'
                  ? 'There are no users in the system yet'
                  : 'There are no users with the role $_filterRole',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _users.length,
      itemBuilder: (context, index) {
        final user = _users[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(AppUser user) {
    // Format last used date
    String lastUsed = 'Never';
    if (user.lastUsed != null) {
      lastUsed = DateFormat(
        'MMM d, yyyy h:mm a',
      ).format(user.lastUsed!.toDate());
    }

    // Format tester expiration
    String testerExpiration = 'No expiration';
    if (user.testerExpiration != null) {
      final expirationDate = user.testerExpiration!.toDate();
      testerExpiration = DateFormat('MMM d, yyyy').format(expirationDate);

      // Check if expired
      if (expirationDate.isBefore(DateTime.now())) {
        testerExpiration += ' (Expired)';
      }
    }

    // Get device ID
    final deviceId = user.deviceId ?? '';

    // Determine role color
    Color roleColor;
    switch (user.role) {
      case 'admin':
        roleColor = Colors.red;
        break;
      case 'tester':
        roleColor = Colors.orange;
        break;
      case 'user':
        roleColor = Colors.blue;
        break;
      default:
        roleColor = Colors.green;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              user.banned == true
                  ? AppColors.error.withOpacity(0.3)
                  : Colors.transparent,
          width: 1,
        ),
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        childrenPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: roleColor.withOpacity(0.1),
          child: Text(
            user.name.isNotEmpty ? user.name[0].toUpperCase() : '?',
            style: TextStyle(color: roleColor, fontWeight: FontWeight.bold),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                user.name,
                style: AppTypography.subtitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (user.banned == true)
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'BANNED',
                  style: TextStyle(
                    color: AppColors.error,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              user.email,
              style: AppTypography.bodySmall,
              overflow: TextOverflow.ellipsis,
            ),
            Row(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: roleColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.role.toUpperCase(),
                    style: TextStyle(
                      color: roleColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
                if (user.role == 'tester' && user.testerExpiration != null) ...[
                  const SizedBox(width: 8),
                  Builder(
                    builder: (context) {
                      final expirationDate = user.testerExpiration!.toDate();
                      final isExpired = expirationDate.isBefore(DateTime.now());
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 12,
                            color:
                                isExpired ? AppColors.error : AppColors.success,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            DateFormat('MMM d, yyyy').format(expirationDate),
                            style: AppTypography.bodySmall.copyWith(
                              fontSize: 10,
                              color:
                                  isExpired
                                      ? AppColors.error
                                      : AppColors.success,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.expand_more),
        children: [
          Row(
            children: [
              const Icon(
                Icons.phone_outlined,
                size: 16,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 8),
              Text(
                'Phone: ${user.phone.isEmpty ? 'Not provided' : user.phone}',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.access_time_outlined,
                size: 16,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 8),
              Text('Last Active: $lastUsed'),
            ],
          ),
          const SizedBox(height: 8),
          FutureBuilder<bool>(
            future:
                deviceId.isNotEmpty
                    ? _isDeviceBanned(deviceId)
                    : Future.value(false),
            builder: (context, snapshot) {
              final isDeviceBanned = snapshot.data ?? false;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.devices_outlined,
                        size: 16,
                        color: AppColors.textSecondaryLight,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Device ID: ${deviceId.isEmpty ? 'None' : deviceId}',
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (deviceId.isNotEmpty && isDeviceBanned)
                        Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.error.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'BANNED DEVICE',
                            style: TextStyle(
                              color: AppColors.error,
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                          ),
                        ),
                    ],
                  ),
                  if (deviceId.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          onPressed: () {
                            _showDeviceBanConfirmation(
                              deviceId,
                              isDeviceBanned,
                            );
                          },
                          icon: Icon(
                            isDeviceBanned
                                ? Icons.lock_open
                                : Icons.phonelink_lock,
                            size: 14,
                            color:
                                isDeviceBanned
                                    ? AppColors.success
                                    : AppColors.error,
                          ),
                          label: Text(
                            isDeviceBanned ? 'Unban Device' : 'Ban Device',
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  isDeviceBanned
                                      ? AppColors.success
                                      : AppColors.error,
                            ),
                          ),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            visualDensity: VisualDensity.compact,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              );
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(
                Icons.person_outline,
                size: 16,
                color: AppColors.textSecondaryLight,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'User ID: ${user.uid}',
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (user.role == 'tester') ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today_outlined,
                  size: 16,
                  color: AppColors.textSecondaryLight,
                ),
                const SizedBox(width: 8),
                Text('Tester Expiration: $testerExpiration'),
                const Spacer(),
                TextButton.icon(
                  onPressed: () {
                    _showUpdateExpirationDialog(user);
                  },
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    visualDensity: VisualDensity.compact,
                  ),
                ),
              ],
            ),
          ],
          const Divider(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              PopupMenuButton<String>(
                onSelected: (role) {
                  _updateUserRole(user.uid, role);
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'user',
                        child: Text('Set as User'),
                      ),
                      const PopupMenuItem(
                        value: 'tester',
                        child: Text('Set as Tester'),
                      ),
                      const PopupMenuItem(
                        value: 'user',
                        child: Text('Set as Agent'),
                      ),
                      const PopupMenuItem(
                        value: 'admin',
                        child: Text('Set as Admin'),
                      ),
                    ],
                child: OutlinedButton.icon(
                  onPressed: null,
                  icon: const Icon(Icons.person, size: 16),
                  label: const Text('Change Role'),
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  _toggleUserBan(user.uid, user.banned == true);
                },
                icon: Icon(
                  user.banned == true ? Icons.lock_open : Icons.block,
                  size: 16,
                ),
                label: Text(user.banned == true ? 'Unban User' : 'Ban User'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      user.banned == true ? AppColors.success : AppColors.error,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDeviceBanConfirmation(String deviceId, bool isCurrentlyBanned) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isCurrentlyBanned ? 'Unban Device' : 'Ban Device'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isCurrentlyBanned
                      ? 'Are you sure you want to unban this device? The user will be able to access the app again.'
                      : 'Are you sure you want to ban this device? The user will not be able to access the app from this device.',
                ),
                const SizedBox(height: 16),
                Text(
                  'Device ID:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textSecondaryLight,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    deviceId,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _toggleDeviceBan(deviceId, isCurrentlyBanned);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isCurrentlyBanned ? AppColors.success : AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: Text(isCurrentlyBanned ? 'Unban' : 'Ban'),
              ),
            ],
          ),
    );
  }

  void _showUpdateExpirationDialog(AppUser user) {
    DateTime? selectedDate = user.testerExpiration!.toDate();

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Update Tester Expiration'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('User: ${user.name}'),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () async {
                                final now = DateTime.now();
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate:
                                      selectedDate ??
                                      now.add(const Duration(days: 30)),
                                  firstDate: now,
                                  lastDate: now.add(
                                    const Duration(days: 365 * 5),
                                  ),
                                );

                                if (date != null) {
                                  setState(() {
                                    selectedDate = date;
                                  });
                                }
                              },
                              child: Text(
                                selectedDate == null
                                    ? 'Select Date'
                                    : DateFormat(
                                      'MMM d, yyyy',
                                    ).format(selectedDate!),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                selectedDate = null;
                              });
                            },
                            icon: const Icon(Icons.clear),
                            tooltip: 'Clear Date',
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton(
                            onPressed: () {
                              setState(() {
                                selectedDate = DateTime.now().add(
                                  const Duration(days: 7),
                                );
                              });
                            },
                            child: const Text('+7 Days'),
                          ),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                selectedDate = DateTime.now().add(
                                  const Duration(days: 30),
                                );
                              });
                            },
                            child: const Text('+30 Days'),
                          ),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                selectedDate = DateTime.now().add(
                                  const Duration(days: 90),
                                );
                              });
                            },
                            child: const Text('+90 Days'),
                          ),
                        ],
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _updateTesterExpiration(user.uid, selectedDate);
                      },
                      child: const Text('Update'),
                    ),
                  ],
                ),
          ),
    );
  }
}
