import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

import '../providers.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// Dashboard tab for the admin panel
class AdminDashboardTab extends ConsumerStatefulWidget {
  /// Optional callback for tab navigation
  final Function(int tabIndex)? onNavigateToTab;

  const AdminDashboardTab({super.key, this.onNavigateToTab});

  @override
  ConsumerState<AdminDashboardTab> createState() => _AdminDashboardTabState();
}

class _AdminDashboardTabState extends ConsumerState<AdminDashboardTab> {
  bool _isLoading = true;
  String? _errorMessage;

  // Dashboard statistics
  int _totalUsers = 0;
  int _activeTesters = 0;
  int _pendingPayments = 0;
  int _totalPayments = 0;
  int _bannedUsers = 0;
  int _bannedDevices = 0;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Get total users count
      final usersSnapshot = await firestore.collection('users').get();
      _totalUsers = usersSnapshot.docs.length;

      // Get active testers count
      final now = DateTime.now();
      _activeTesters =
          usersSnapshot.docs.where((doc) {
            final data = doc.data();
            if (data['role'] != 'tester') return false;
            if (data['testerExpiration'] == null) return false;

            try {
              final expiration = DateTime.parse(data['testerExpiration']);
              return expiration.isAfter(now);
            } catch (e) {
              return false;
            }
          }).length;

      // Get banned users count
      _bannedUsers =
          usersSnapshot.docs.where((doc) {
            final data = doc.data();
            return data['banned'] == true;
          }).length;

      // Get payments counts
      final paymentsSnapshot = await firestore.collection('payments').get();
      _totalPayments = paymentsSnapshot.docs.length;

      // Get pending payments count
      _pendingPayments =
          paymentsSnapshot.docs.where((doc) {
            final data = doc.data();
            return data['status'] == 'pending approval';
          }).length;

      // Get banned devices count
      final appStateDoc =
          await firestore.collection('app_state').doc('global').get();
      if (appStateDoc.exists) {
        final appState = appStateDoc.data()!;
        final bannedDevices = appState['bannedDevices'];
        if (bannedDevices != null && bannedDevices is List) {
          _bannedDevices = bannedDevices.length;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading dashboard data: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: AppColors.error),
              const SizedBox(height: 16),
              Text(
                'Error Loading Dashboard',
                style: AppTypography.subtitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(_errorMessage!, textAlign: TextAlign.center),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadDashboardData,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dashboard',
              style: AppTypography.headline.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Overview of your application',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
            const SizedBox(height: 24),
            _buildStatisticsGrid(),
            const SizedBox(height: 32),
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'Total Users',
          value: _totalUsers.toString(),
          icon: Icons.people,
          color: AppColors.primary,
        ),
        _buildStatCard(
          title: 'Active Testers',
          value: _activeTesters.toString(),
          icon: Icons.verified_user,
          color: AppColors.success,
        ),
        _buildStatCard(
          title: 'Pending',
          value: _pendingPayments.toString(),
          icon: Icons.payment,
          color: AppColors.warning,
        ),
        _buildStatCard(
          title: 'Total',
          value: _totalPayments.toString(),
          icon: Icons.receipt_long,
          color: AppColors.info,
        ),
        _buildStatCard(
          title: 'Banned Users',
          value: _bannedUsers.toString(),
          icon: Icons.block,
          color: AppColors.error,
        ),
        _buildStatCard(
          title: 'Banned',
          value: _bannedDevices.toString(),
          icon: Icons.phonelink_lock,
          color: Colors.deepPurple,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondaryLight,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: AppTypography.headline.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTypography.subtitle.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionButton(
              label: 'Verify Payments',
              icon: Icons.check_circle_outline,
              color: AppColors.success,
              onTap: () {
                // Navigate to payments tab
                if (widget.onNavigateToTab != null) {
                  widget.onNavigateToTab!(1);
                } else {
                  _showNavigationErrorSnackbar();
                }
              },
            ),
            _buildActionButton(
              label: 'Manage Users',
              icon: Icons.people_outline,
              color: AppColors.primary,
              onTap: () {
                // Navigate to users tab
                if (widget.onNavigateToTab != null) {
                  widget.onNavigateToTab!(2);
                } else {
                  _showNavigationErrorSnackbar();
                }
              },
            ),
            _buildActionButton(
              label: 'Manage Banned Devices',
              icon: Icons.phonelink_lock,
              color: Colors.deepPurple,
              onTap: () {
                // Navigate to users tab
                if (widget.onNavigateToTab != null) {
                  widget.onNavigateToTab!(2);
                  // Show a snackbar with instructions
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Find users with devices to ban/unban in the User Management tab',
                      ),
                      duration: Duration(seconds: 5),
                    ),
                  );
                } else {
                  _showNavigationErrorSnackbar();
                }
              },
            ),
            _buildActionButton(
              label: 'Export Data',
              icon: Icons.download_outlined,
              color: AppColors.info,
              onTap: () {
                // Show export options dialog
                _showExportDialog();
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Export Data'),
            content: const Text(
              'This feature is not yet implemented. It will allow exporting user and payment data for reporting purposes.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showNavigationErrorSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigation error: Unable to switch tabs'),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
