import 'package:flutter/material.dart' as flutter;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers.dart';
import '../r_scafold.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import 'app_update_tab.dart';
import 'payment_verification_tab.dart';
import 'user_management_tab.dart';
import 'admin_dashboard_tab.dart';
import 'global_alert_tab.dart';

/// Admin panel screen that provides access to administrative functions
class AdminPanelScreen extends ConsumerStatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  ConsumerState<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends ConsumerState<AdminPanelScreen>
    with flutter.SingleTickerProviderStateMixin {
  late flutter.TabController _tabController;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = flutter.TabController(length: 5, vsync: this);
    _checkAdminAccess();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Check if the current user has admin access
  Future<void> _checkAdminAccess() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final user = ref.read(userProvider).value;
      if (user == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'You must be logged in to access the admin panel';
        });
        return;
      }

      if (user.role != 'admin') {
        setState(() {
          _isLoading = false;
          _errorMessage =
              'You do not have permission to access the admin panel';
        });
        return;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error checking admin access: ${e.toString()}';
      });
    }
  }

  @override
  flutter.Widget build(flutter.BuildContext context) {
    return RScafold(
      // title: 'Admin Panel',
      // showBackButton: true,
      body: _buildBody(),
    );
  }

  flutter.Widget _buildBody() {
    if (_isLoading) {
      return const flutter.Center(child: flutter.CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return _buildErrorScreen();
    }

    return flutter.Column(
      children: [
        _buildTabBar(),
        flutter.Expanded(
          child: flutter.TabBarView(
            controller: _tabController,
            children: [
              AdminDashboardTab(
                onNavigateToTab: (index) {
                  _tabController.animateTo(index);
                },
              ),
              const PaymentVerificationTab(),
              const UserManagementTab(),
              const GlobalAlertTab(),
               AppUpdateTab(),
            ],
          ),
        ),
      ],
    );
  }

  flutter.Widget _buildTabBar() {
    return flutter.Container(
      color: AppColors.primary,
      child: flutter.TabBar(
        controller: _tabController,
        indicatorColor: flutter.Colors.white,
        indicatorWeight: 3,
        labelColor: flutter.Colors.white,
        unselectedLabelColor: flutter.Colors.white.withOpacity(0.7),
        tabs: const [
          flutter.Tab(
            icon: flutter.Icon(flutter.Icons.dashboard),
            text: 'Dashboard',
          ),
          flutter.Tab(
            icon: flutter.Icon(flutter.Icons.payment),
            text: 'Payments',
          ),
          flutter.Tab(icon: flutter.Icon(flutter.Icons.people), text: 'Users'),
          flutter.Tab(
            icon: flutter.Icon(flutter.Icons.notifications),
            text: 'Alerts',
          ),
          flutter.Tab(
            icon: flutter.Icon(flutter.Icons.system_update),
            text: 'Updates',
          ),
        ],
      ),
    );
  }

  flutter.Widget _buildErrorScreen() {
    return flutter.Center(
      child: flutter.Padding(
        padding: const flutter.EdgeInsets.all(24.0),
        child: flutter.Column(
          mainAxisAlignment: flutter.MainAxisAlignment.center,
          children: [
            flutter.Icon(
              flutter.Icons.error_outline,
              size: 80,
              color: AppColors.error.withOpacity(0.8),
            ),
            const flutter.SizedBox(height: 24),
            flutter.Text(
              'Access Denied',
              style: AppTypography.headline.copyWith(
                color: AppColors.error,
                fontWeight: flutter.FontWeight.bold,
              ),
            ),
            const flutter.SizedBox(height: 16),
            flutter.Text(
              _errorMessage!,
              textAlign: flutter.TextAlign.center,
              style: AppTypography.bodyLarge,
            ),
            const flutter.SizedBox(height: 32),
            flutter.ElevatedButton.icon(
              onPressed: () {
                flutter.Navigator.of(context).pop();
              },
              icon: const flutter.Icon(flutter.Icons.arrow_back),
              label: const flutter.Text('Go Back'),
              style: flutter.ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: flutter.Colors.white,
                padding: const flutter.EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
