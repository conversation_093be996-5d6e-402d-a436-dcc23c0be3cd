import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:rauth/models/global_alert.dart';
import 'package:rauth/providers.dart';
import 'package:rauth/theme/app_colors.dart';

class GlobalAlertTab extends ConsumerStatefulWidget {
  const GlobalAlertTab({super.key});

  @override
  ConsumerState<GlobalAlertTab> createState() => _GlobalAlertTabState();
}

class _GlobalAlertTabState extends ConsumerState<GlobalAlertTab> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isEditing = false;

  // Form controllers
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  AlertType _selectedType = AlertType.info;
  bool _isDismissible = true;
  final _actionTextController = TextEditingController();
  final _actionUrlController = TextEditingController();
  DateTime? _expirationDate;

  // Current alert
  GlobalAlert? _currentAlert;

  @override
  void initState() {
    super.initState();
    _loadCurrentAlert();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _messageController.dispose();
    _actionTextController.dispose();
    _actionUrlController.dispose();
    super.dispose();
  }

  /// Load the current global alert from the provider
  void _loadCurrentAlert() {
    final alert = ref.read(globalAlertProvider);
    if (alert != null) {
      setState(() {
        _currentAlert = alert;
      });
    }
  }

  /// Reset the form fields
  void _resetForm() {
    _titleController.clear();
    _messageController.clear();
    _selectedType = AlertType.info;
    _isDismissible = true;
    _actionTextController.clear();
    _actionUrlController.clear();
    _expirationDate = null;
    setState(() {
      _isEditing = false;
      _errorMessage = null;
    });
  }

  /// Save the alert to Firestore
  Future<void> _saveAlert() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Create the alert data
      final alertData = {
        'id':
            _isEditing && _currentAlert != null
                ? _currentAlert!.id
                : 'alert-${DateTime.now().millisecondsSinceEpoch}',
        'title': _titleController.text.trim(),
        'message': _messageController.text.trim(),
        'type': _selectedType.name,
        'dismissible': _isDismissible,
        'actionText':
            _actionTextController.text.isEmpty
                ? null
                : _actionTextController.text.trim(),
        'actionUrl':
            _actionUrlController.text.isEmpty
                ? null
                : _actionUrlController.text.trim(),
        'expiresAt':
            _expirationDate != null
                ? Timestamp.fromDate(_expirationDate!)
                : null,
      };

      // Update the app_state document
      await firestore.collection('app_state').doc('global').update({
        'globalAlert': alertData,
      });

      // Reset the form
      _resetForm();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Global alert saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving alert: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Delete the current alert
  Future<void> _deleteAlert() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = ref.read(firestoreProvider);

      // Set the globalAlert field to null
      await firestore.collection('app_state').doc('global').update({
        'globalAlert': null,
      });

      // Reset the form and current alert
      _resetForm();
      setState(() {
        _currentAlert = null;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Global alert deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error deleting alert: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _selectExpirationDate(BuildContext context) {
    // Use a simpler approach with just a date picker for now
    _pickDate(context);
  }

  Future<void> _pickDate(BuildContext context) async {
    final initialDate =
        _expirationDate ?? DateTime.now().add(const Duration(days: 7));

    // Show date picker
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDate != null && mounted) {
      // Set the expiration date to the end of the selected day (11:59:59 PM)
      setState(() {
        _expirationDate = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          23,
          59,
          59,
        );
      });
    }
  }

  Future<void> _showDeleteConfirmation(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Alert'),
          content: const SingleChildScrollView(
            child: Text(
              'Are you sure you want to delete this global alert? This action cannot be undone.',
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteAlert();
              },
            ),
          ],
        );
      },
    );
  }

  IconData _getAlertIcon(AlertType type) {
    switch (type) {
      case AlertType.warning:
        return Icons.warning_amber_rounded;
      case AlertType.error:
        return Icons.error_outline;
      case AlertType.success:
        return Icons.check_circle_outline;
      case AlertType.info:
        return Icons.info_outline;
    }
  }

  Color _getAlertColor(AlertType type) {
    switch (type) {
      case AlertType.warning:
        return Colors.orange;
      case AlertType.error:
        return Colors.red;
      case AlertType.success:
        return Colors.green;
      case AlertType.info:
        return Colors.blue;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch for changes to the global alert
    final alert = ref.watch(globalAlertProvider);
    if (alert != _currentAlert) {
      setState(() {
        _currentAlert = alert;
      });
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [_buildAlertForm()],
      ),
    );
  }

  Widget _buildAlertForm() {
    return Form(
      key: _formKey,
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isEditing ? 'Edit Alert' : 'Create New Alert',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),

              // Title field
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Alert Title',
                  hintText: 'Enter a title for the alert',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Message field
              TextFormField(
                controller: _messageController,
                decoration: const InputDecoration(
                  labelText: 'Alert Message',
                  hintText: 'Enter the message for the alert',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a message';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Alert type dropdown
              DropdownButtonFormField<AlertType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Alert Type',
                  border: OutlineInputBorder(),
                ),
                items:
                    AlertType.values.map((type) {
                      return DropdownMenuItem<AlertType>(
                        value: type,
                        child: Row(
                          children: [
                            Icon(
                              _getAlertIcon(type),
                              color: _getAlertColor(type),
                            ),
                            const SizedBox(width: 8),
                            Text(type.name.toUpperCase()),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Dismissible checkbox
              CheckboxListTile(
                title: const Text('Allow users to dismiss this alert'),
                value: _isDismissible,
                onChanged: (value) {
                  setState(() {
                    _isDismissible = value ?? true;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              const SizedBox(height: 16),
              // Action text field
              TextFormField(
                controller: _actionTextController,
                decoration: const InputDecoration(
                  labelText: 'Action Button Text (Optional)',
                  hintText: 'Enter the text for the action button',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Action URL field
              TextFormField(
                controller: _actionUrlController,
                decoration: const InputDecoration(
                  labelText: 'Action Button URL (Optional)',
                  hintText:
                      'Enter the URL to open when the action button is pressed',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),

              // Expiration date field
              InkWell(
                onTap: () => _selectExpirationDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Expiration Date (Optional)',
                    hintText: 'When should this alert expire?',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _expirationDate != null
                            ? DateFormat(
                              'MMM d, yyyy h:mm a',
                            ).format(_expirationDate!)
                            : 'No expiration (alert will show indefinitely)',
                      ),
                      if (_expirationDate != null)
                        IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            setState(() {
                              _expirationDate = null;
                            });
                          },
                          tooltip: 'Clear date',
                        ),
                    ],
                  ),
                ),
              ),

              // Error message
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.red),
                  ),
                ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Current alert status
                  if (_currentAlert != null)
                    TextButton.icon(
                      icon: Icon(Icons.delete, color: Colors.red),
                      label: Text('Delete Current'),
                      onPressed: () => _showDeleteConfirmation(context),
                    )
                  else
                    Text('No active alert'),

                  // Save button
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveAlert,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : Text(
                              _isEditing ? 'Update Alert' : 'Create Alert',
                            ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
