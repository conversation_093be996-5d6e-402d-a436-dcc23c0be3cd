import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/profile_screen.dart';
import 'package:rauth/providers.dart';

/// A button that navigates to the profile screen
/// This can be used in app bars or other UI elements
class ProfileButton extends ConsumerWidget {
  /// The color of the icon
  final Color? color;

  /// The size of the icon
  final double size;

  /// Whether to show the user's initials instead of an icon
  final bool showInitials;

  /// The background color of the avatar when showing initials
  final Color? backgroundColor;

  const ProfileButton({
    super.key,
    this.color,
    this.size = 24.0,
    this.showInitials = false,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref
        .watch(userProvider)
        .when(
          data: (user) {
            if (user == null) {
              return const SizedBox.shrink();
            }

            final colorScheme = Theme.of(context).colorScheme;

            return IconButton(
              icon: CircleAvatar(
                radius: size / 2,
                backgroundImage: NetworkImage(user.profilePictureUrl),
                backgroundColor:
                    backgroundColor ?? colorScheme.primaryContainer,
                onBackgroundImageError: (_, __) {},
              ),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfileScreen(),
                  ),
                );
              },
              tooltip: 'Profile',
            );
          },
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        );
  }
}

/// A profile avatar that shows the user's initials
/// This can be used in app bars or other UI elements
class ProfileAvatar extends ConsumerWidget {
  /// The size of the avatar
  final double size;

  /// The background color of the avatar
  final Color? backgroundColor;

  /// The text color
  final Color? textColor;

  /// Whether to show a border
  final bool showBorder;

  /// The border color
  final Color? borderColor;

  /// The border width
  final double borderWidth;

  /// Whether to navigate to the profile screen when tapped
  final bool navigateToProfile;

  const ProfileAvatar({
    super.key,
    this.size = 40.0,
    this.backgroundColor,
    this.textColor,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2.0,
    this.navigateToProfile = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;

    return ref
        .watch(userProvider)
        .when(
          data: (user) {
            if (user == null) {
              return const SizedBox.shrink();
            }

            final avatar = CircleAvatar(
              radius: size / 2,
              backgroundColor: backgroundColor ?? colorScheme.primaryContainer,
              child: ClipOval(
                child: Image.network(
                  user.profilePictureUrl,
                  fit: BoxFit.cover,
                  width: size,
                  height: size,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: SizedBox(
                        width: size / 2,
                        height: size / 2,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.0,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            textColor ?? colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.account_circle,
                      size: size,
                      color: textColor ?? colorScheme.onPrimary,
                    );
                  },
                ),
              ),
            );

            final decoratedAvatar =
                showBorder
                    ? Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: borderColor ?? colorScheme.primary,
                          width: borderWidth,
                        ),
                      ),
                      child: avatar,
                    )
                    : avatar;

            if (navigateToProfile) {
              return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const ProfileScreen(),
                    ),
                  );
                },
                child: decoratedAvatar,
              );
            }

            return decoratedAvatar;
          },
          loading: () => const SizedBox.shrink(),
          error: (_, __) => const SizedBox.shrink(),
        );
  }

}
