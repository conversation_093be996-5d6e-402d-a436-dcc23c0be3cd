import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/providers.dart';

/// A widget that displays the time left for a tester account
/// It automatically updates every second
class TimeLeftText extends ConsumerWidget {
  /// The style of the text
  final TextStyle? style;

  /// Whether to show the text when the user is not a tester
  final bool showWhenNotTester;

  /// The text to display when the user is not a tester
  final String notTesterText;

  /// Whether to show the text when the user is not logged in
  final bool showWhenNotLoggedIn;

  /// The text to display when the user is not logged in
  final String notLoggedInText;

  /// Whether to show the text when the account has expired
  final bool showWhenExpired;

  /// The text to display when the account has expired
  final String expiredText;

  /// Whether to use the live provider that updates every second
  final bool useLiveProvider;

  const TimeLeftText({
    super.key,
    this.style,
    this.showWhenNotTester = false,
    this.notTesterText = '',
    this.showWhenNotLoggedIn = false,
    this.notLoggedInText = '',
    this.showWhenExpired = true,
    this.expiredText = 'Expired',
    this.useLiveProvider = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (useLiveProvider) {
      return ref
          .watch(liveTimeLeftTextProvider)
          .when(
            data: (timeLeftText) {
              // If the text is empty, it means the user is not a tester or not logged in
              if (timeLeftText.isEmpty) {
                final user = ref.watch(userProvider).value;

                // If the user is not logged in
                if (user == null) {
                  return showWhenNotLoggedIn
                      ? Text(notLoggedInText, style: style)
                      : const SizedBox.shrink();
                }

                // If the user is not a tester
                return showWhenNotTester
                    ? Text(notTesterText, style: style)
                    : const SizedBox.shrink();
              }

              // If the text is 'Expired'
              if (timeLeftText == 'Expired') {
                return showWhenExpired
                    ? Text(expiredText, style: style)
                    : const SizedBox.shrink();
              }

              // Otherwise, show the time left
              return Text(
                timeLeftText,
                style: style,
                overflow: TextOverflow.ellipsis,
              );
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          );
    } else {
      // Use the non-live provider
      final timeLeftText = ref.watch(timeLeftTextProvider);

      // If the text is empty, it means the user is not a tester or not logged in
      if (timeLeftText.isEmpty) {
        final user = ref.watch(userProvider).value;

        // If the user is not logged in
        if (user == null) {
          return showWhenNotLoggedIn
              ? Text(notLoggedInText, style: style)
              : const SizedBox.shrink();
        }

        // If the user is not a tester
        return showWhenNotTester
            ? Text(notTesterText, style: style)
            : const SizedBox.shrink();
      }

      // If the text is 'Expired'
      if (timeLeftText == 'Expired') {
        return showWhenExpired
            ? Text(expiredText, style: style)
            : const SizedBox.shrink();
      }

      // Otherwise, show the time left
      return Text(timeLeftText, style: style, overflow: TextOverflow.ellipsis);
    }
  }
}
