import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class AppUser {
  final String uid;
  final String name;
  final String email;
  final String phone;
  final String role; // admin, user, tester
  String? deviceId;
  final Timestamp? lastUsed;
  final Timestamp? testerExpiration;
  bool? banned;
  final String profilePictureUrl;

  AppUser({
    required this.uid,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.deviceId,
    this.lastUsed,
    this.testerExpiration,
    this.banned,
    required this.profilePictureUrl,
  });

  AppUser copyWith({
    String? uid,
    String? name,
    String? email,
    String? phone,
    String? role,
    String? deviceId,
    Timestamp? lastUsed,
    Timestamp? testerExpiration,
    bool? banned,
    String? profilePictureUrl,
  }) {
    return AppUser(
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      deviceId: deviceId ?? this.deviceId,
      lastUsed: lastUsed ?? this.lastUsed,
      testerExpiration: testerExpiration ?? this.testerExpiration,
      banned: banned ?? this.banned,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
    );
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'uid': uid});
    result.addAll({'name': name});
    result.addAll({'email': email});
    result.addAll({'phone': phone});
    result.addAll({'role': role});
    result.addAll({'profilePictureUrl': profilePictureUrl});
    if (deviceId != null) {
      result.addAll({'deviceId': deviceId});
    }
    if (lastUsed != null) {
      result.addAll({'lastUsed': lastUsed!.millisecondsSinceEpoch});
    }
    if (testerExpiration != null) {
      result.addAll({
        'testerExpiration': testerExpiration!.millisecondsSinceEpoch,
      });
    }
    if (banned != null) {
      result.addAll({'banned': banned});
    }

    return result;
  }

  factory AppUser.fromMap(Map<String, dynamic> map) {
    return AppUser(
      uid: map['uid'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      role: map['role'] ?? '',
      deviceId: map['deviceId'],
      lastUsed: map['lastUsed'],
      testerExpiration: map['testerExpiration'],
      banned: map['banned'],
      profilePictureUrl:
          map['profilePictureUrl'] ??
          "https://eu.ui-avatars.com/api/?name=${Uri.encodeQueryComponent(map['name'] ?? '')}&size=50",
    );
  }

  String toJson() => json.encode(toMap());

  factory AppUser.fromJson(String source) =>
      AppUser.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AppUser(uid: $uid, name: $name, email: $email, phone: $phone, role: $role, deviceId: $deviceId, lastUsed: $lastUsed, testerExpiration: $testerExpiration, banned: $banned, profilePictureUrl: $profilePictureUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppUser &&
        other.uid == uid &&
        other.name == name &&
        other.email == email &&
        other.phone == phone &&
        other.role == role &&
        other.deviceId == deviceId &&
        other.lastUsed == lastUsed &&
        other.testerExpiration == testerExpiration &&
        other.banned == banned &&
        other.profilePictureUrl == profilePictureUrl;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        role.hashCode ^
        deviceId.hashCode ^
        lastUsed.hashCode ^
        testerExpiration.hashCode ^
        banned.hashCode ^
        profilePictureUrl.hashCode;
  }
}
