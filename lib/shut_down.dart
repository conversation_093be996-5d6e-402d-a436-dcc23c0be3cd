import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';

class ShutdownScreen extends StatelessWidget {
  const ShutdownScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        // Modern gradient with softer colors
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF121E40), Color(0xFF1E3A5F), Color(0xFF2A4365)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Animated background blur elements
                  Positioned(
                    top: 50,
                    left: 40,
                    child: _buildBlurCircle(100, const Color(0x301E88E5)),
                  ),
                  Positioned(
                    bottom: 90,
                    right: 60,
                    child: _buildBlurCircle(120, const Color(0x3003A9F4)),
                  ),

                  // Main content card
                  Container(
                    constraints: const BoxConstraints(maxWidth: 380),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 40,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(32),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Lottie animation with soft pulsating effect
                          SizedBox(
                                child: Lottie.asset(
                                  'assets/lotties/shutdown.json',
                                  package: 'rauth',
                                  height: 200,
                                  repeat: true,
                                ),
                              )
                              .animate()
                              .fadeIn(duration: 600.ms)
                              .moveY(begin: 10, end: 0),
                          const SizedBox(height: 24),

                          // Main title with modern typography
                          const Text(
                                'We’ll Be Right Back',
                                style: TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                  letterSpacing: -0.5,
                                ),
                                textAlign: TextAlign.center,
                              )
                              .animate()
                              .fadeIn(delay: 300.ms, duration: 600.ms)
                              .moveY(begin: 10, end: 0),

                          const SizedBox(height: 16),

                          const Text(
                                'We’re temporarily powering down for upgrades.\nHang tight — we’ll be back shortly!',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  height: 1.5,
                                  letterSpacing: 0.2,
                                ),
                                textAlign: TextAlign.center,
                              )
                              .animate()
                              .fadeIn(delay: 500.ms, duration: 600.ms)
                              .moveY(begin: 10, end: 0),

                          const SizedBox(height: 40),

                          // Modern styled button
                          // _buildExitButton(context),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBlurCircle(double size, Color color) {
    return Container(
          height: size,
          width: size,
          decoration: BoxDecoration(shape: BoxShape.circle, color: color),
        )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .scaleXY(
          begin: 0.8,
          end: 1.2,
          duration: 4000.ms,
          curve: Curves.easeInOut,
        );
  }
}
