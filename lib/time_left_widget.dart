import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rauth/app_user.dart';

/// A widget that displays the time left for a tester account
/// It automatically updates every second
class TimeLeftWidget extends ConsumerStatefulWidget {
  /// The user to display time left for
  final AppUser user;

  /// The style of the text
  final TextStyle? style;

  /// The text to display when the account has expired
  final String expiredText;

  /// The text to display when the account is not a tester
  final String notTesterText;

  /// The format of the time left text
  /// {d} = days, {h} = hours, {m} = minutes, {s} = seconds
  final String timeLeftFormat;

  const TimeLeftWidget({
    super.key,
    required this.user,
    this.style,
    this.expiredText = 'Account expired',
    this.notTesterText = 'VIP',
    this.timeLeftFormat = '{d}d {h}h {m}m {s}s left',
  });

  @override
  ConsumerState<TimeLeftWidget> createState() => _TimeLeftWidgetState();
}

class _TimeLeftWidgetState extends ConsumerState<TimeLeftWidget> {
  late Timer _timer;
  late String _timeLeftText;

  @override
  void initState() {
    super.initState();
    _updateTimeLeft();

    // Update the time left every second
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        _updateTimeLeft();
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(TimeLeftWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.user != widget.user) {
      _updateTimeLeft();
    }
  }

  void _updateTimeLeft() {
    setState(() {
      _timeLeftText = _getTimeLeftText();
    });
  }

  String _getTimeLeftText() {
    // If the user is not a tester, return the not tester text
    if (widget.user.role != 'tester') {
      return widget.notTesterText;
    }

    // If the tester expiration is null, return the expired text
    if (widget.user.testerExpiration == null) {
      return widget.expiredText;
    }

    // Calculate the time left
    final now = DateTime.now();
    final expiration = widget.user.testerExpiration!.toDate();

    // If the account has expired, return the expired text
    if (now.isAfter(expiration)) {
      return widget.expiredText;
    }

    // Calculate the time left
    final difference = expiration.difference(now);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;
    final seconds = difference.inSeconds % 60;

    // Format the time left text
    return widget.timeLeftFormat
        .replaceAll('{d}', days.toString())
        .replaceAll('{h}', hours.toString().padLeft(2, '0'))
        .replaceAll('{m}', minutes.toString().padLeft(2, '0'))
        .replaceAll('{s}', seconds.toString().padLeft(2, '0'));
  }

  @override
  Widget build(BuildContext context) {
    return Text(_timeLeftText, style: widget.style);
  }
}

/// A simplified version of TimeLeftWidget that uses a StreamBuilder
/// This is useful when you don't need to customize the appearance
class SimpleTimeLeftText extends StatelessWidget {
  /// The user to display time left for
  final AppUser user;

  /// The style of the text
  final TextStyle? style;

  /// The text to display when the account has expired
  final String expiredText;

  /// The text to display when the account is not a tester
  final String notTesterText;

  /// The format of the time left text
  /// {d} = days, {h} = hours, {m} = minutes, {s} = seconds
  final String timeLeftFormat;

  const SimpleTimeLeftText({
    super.key,
    required this.user,
    this.style,
    this.expiredText = 'expired',
    this.notTesterText = 'VIP',
    this.timeLeftFormat = '{d}d {h}h {m}m {s}s left',
  });

  @override
  Widget build(BuildContext context) {
    // If the user is not a tester, return the not tester text
    if (user.role != 'tester') {
      return Text(notTesterText, style: style);
    }

    // If the tester expiration is null, return the expired text
    if (user.testerExpiration == null) {
      return Text(expiredText, style: style);
    }

    return StreamBuilder<int>(
      stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
      builder: (context, snapshot) {
        // Calculate the time left
        final now = DateTime.now();
        final expiration = user.testerExpiration!.toDate();

        // If the account has expired, return the expired text
        if (now.isAfter(expiration)) {
          return Text(expiredText, style: style);
        }

        // Calculate the time left
        final difference = expiration.difference(now);
        final days = difference.inDays;
        final hours = difference.inHours % 24;
        final minutes = difference.inMinutes % 60;
        final seconds = difference.inSeconds % 60;

        // Format the time left text
        final timeLeftText = timeLeftFormat
            .replaceAll('{d}', days.toString())
            .replaceAll('{h}', hours.toString().padLeft(2, '0'))
            .replaceAll('{m}', minutes.toString().padLeft(2, '0'))
            .replaceAll('{s}', seconds.toString().padLeft(2, '0'));

        return Text(timeLeftText, style: style);
      },
    );
  }
}
