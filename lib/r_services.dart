import 'package:cloud_firestore/cloud_firestore.dart'
    show FieldValue, FirebaseFirestore, Timestamp;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart'
    show EmailAuthProvider, FirebaseAuth, FirebaseAuthException;
import 'package:flutter/foundation.dart' show kIsWeb, debugPrint;
import 'package:path_provider/path_provider.dart';
import 'web_device_id.dart';

class RAuthService {
  final FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  RAuthService(this._auth, this._firestore);

  Future<void> signIn(String email, String password) async {
    // Get device ID based on platform
    String deviceId;
    Map<String, dynamic>? deviceInfo;

    if (kIsWeb) {
      // For web, use the UUID from SharedPreferences
      deviceId = await WebDeviceId.getWebDeviceId();
      deviceInfo = {'type': 'web', 'id': deviceId};
    } else {
      // For mobile, get the device ID
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidInfo.id;
      deviceInfo = {
        'id': deviceId,
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'manufacturer': androidInfo.manufacturer,
      };
    }

    // Check if this device is banned
    final appStateDoc =
        await _firestore.collection('app_state').doc('global').get();
    if (appStateDoc.exists) {
      final appState = appStateDoc.data();
      final bannedDevices = appState?['bannedDevices'];
      if (bannedDevices != null &&
          bannedDevices is List &&
          bannedDevices.contains(deviceId)) {
        throw Exception('This device is banned');
      }
    }

    await _auth.signInWithEmailAndPassword(email: email, password: password);
    await _firestore
        .collection('users')
        .doc(_auth.currentUser!.uid)
        .get()
        .then((doc) async {
          if (doc.exists) {
            final data = doc.data()!;
            if (data['role'] == 'tester' &&
                data['testerExpiration'].toDate().isBefore(DateTime.now())) {
              throw Exception('Tester account expired');
            }
            if (data['deviceId'] != null) {
              if (data['deviceId'] != deviceId) {
                throw Exception('Device ID mismatch');
              }
            } else {
              await _firestore
                  .collection('users')
                  .doc(_auth.currentUser!.uid)
                  .update({
                    'deviceId': deviceId,
                    'deviceInfo': deviceInfo,
                    'lastUsed': FieldValue.serverTimestamp(),
                  });
            }
          } else {
            throw Exception('User document does not exist');
          }
        })
        .catchError((error) {
          throw Exception('Error fetching user $error');
        });
  }

  Future<void> signUp(
    String email,
    String password,
    String name,
    String phone,
  ) async {
    // Get device ID based on platform
    String deviceId;
    Map<String, dynamic> deviceInfo;

    if (kIsWeb) {
      // For web, use the UUID from SharedPreferences
      deviceId = await WebDeviceId.getWebDeviceId();
      deviceInfo = {'type': 'web', 'id': deviceId};
    } else {
      // For mobile, get the device ID
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      deviceId = androidInfo.id;
      deviceInfo = {
        'id': deviceId,
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'manufacturer': androidInfo.manufacturer,
      };
    }

    // Check if this device is banned
    final appStateDoc =
        await _firestore.collection('app_state').doc('global').get();
    if (appStateDoc.exists) {
      final appState = appStateDoc.data();
      final bannedDevices = appState?['bannedDevices'];
      if (bannedDevices != null &&
          bannedDevices is List &&
          bannedDevices.contains(deviceId)) {
        throw Exception('This device is banned');
      }
    }

    // Check if this device has already created an account
    final deviceDoc =
        await _firestore.collection('devices').doc(deviceId).get();
    if (deviceDoc.exists && deviceDoc.data()?['accountCreated'] == true) {
      throw Exception('This device already has an account');
    }

    // Create the user account
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: password,
    );

    // Create user document
    await _firestore.collection('users').doc(userCredential.user!.uid).set({
      'role': 'tester',
      'name': name,
      'phone': phone,
      'email': email,
      'deviceId': deviceId,
      'deviceInfo': deviceInfo,
      'lastUsed': FieldValue.serverTimestamp(),
      'testerExpiration': Timestamp.fromDate(
        DateTime.now().add(Duration(hours: 3)),
      ),
    });

    // Mark this device as having created an account
    await _firestore.collection('devices').doc(deviceId).set({
      'accountCreated': true,
      'createdAt': FieldValue.serverTimestamp(),
      'userId': userCredential.user!.uid,
      'deviceInfo': deviceInfo,
      'platform': kIsWeb ? 'web' : 'android',
    });
  }

  Future<void> signOut() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        await _firestore.collection('users').doc(user.uid).update({
          'deviceId': null,
          'lastUsed': FieldValue.serverTimestamp(),
        });
      }
      

      if (kIsWeb) {
        await WebDeviceId.clearWebDeviceId();
      }

      await cleanCache();

      await _auth.signOut(); // ✅ Must be awaited

      debugPrint('User signed out successfully');
    } catch (e) {
      debugPrint('Error during sign out: $e');
      rethrow;
    }
  }

  Future<void> cleanCache() async {
    // Skip for web platform as getTemporaryDirectory() is not available
    if (!kIsWeb) {
      try {
        final directory = await getTemporaryDirectory();
        await directory.delete(recursive: true);
        debugPrint('Cache cleaned successfully');
      } catch (e) {
        // Log the error but don't throw it
        debugPrint('Error cleaning cache: $e');
      }
    }
  }

  /// Update the user's profile information
  ///
  /// This method allows updating the user's name, email, and phone number.
  /// Note that changing the email will not update the Firebase Auth email,
  /// it will only update the Firestore document.
  Future<void> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? profilePictureUrl,
  }) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User not logged in');
    }

    final Map<String, dynamic> updates = {};

    if (name != null && name.isNotEmpty) {
      updates['name'] = name;
    }

    if (email != null && email.isNotEmpty) {
      updates['email'] = email;
    }

    if (phone != null && phone.isNotEmpty) {
      updates['phone'] = phone;
    }

    if (profilePictureUrl != null) {
      updates['profilePictureUrl'] = profilePictureUrl;
    }

    if (updates.isNotEmpty) {
      updates['lastUpdated'] = FieldValue.serverTimestamp();
      await _firestore.collection('users').doc(user.uid).update(updates);
    }
  }

  /// Change the user's password
  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User not logged in');
    }

    if (user.email == null) {
      throw Exception('User has no email');
    }

    // Re-authenticate the user to verify the current password
    final credential = EmailAuthProvider.credential(
      email: user.email!,
      password: currentPassword,
    );

    try {
      await user.reauthenticateWithCredential(credential);
      await user.updatePassword(newPassword);
    } catch (e) {
      if (e is FirebaseAuthException) {
        if (e.code == 'wrong-password') {
          throw Exception('Current password is incorrect');
        }
      }
      rethrow;
    }
  }
}
