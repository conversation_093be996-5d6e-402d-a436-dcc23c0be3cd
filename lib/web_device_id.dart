import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

/// Class to handle web device identification
class WebDeviceId {
  static const String _webDeviceIdKey = 'web_device_id';
  
  /// Get the device ID for web users
  /// If a device ID doesn't exist, it creates a new one and stores it
  static Future<String> getWebDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Check if we already have a stored device ID
    String? deviceId = prefs.getString(_webDeviceIdKey);
    
    // If no device ID exists, create a new one
    if (deviceId == null) {
      deviceId = const Uuid().v4(); // Generate a random UUID
      await prefs.setString(_webDeviceIdKey, deviceId);
    }
    
    return deviceId;
  }
  
  /// Clear the stored web device ID
  /// This should be called when the user signs out
  static Future<void> clearWebDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_webDeviceIdKey);
  }
}
