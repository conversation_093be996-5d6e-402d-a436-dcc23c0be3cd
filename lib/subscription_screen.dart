import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart' show FieldValue;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;

import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

import 'rauth_exports.dart';

/// A model representing a subscription plan
class SubscriptionPlan {
  /// The name of the plan
  final String name;

  /// The duration in days
  final int durationDays;

  /// The price in MMK
  final int price;

  /// Any discount information
  final String? discountInfo;

  /// Whether this is the most popular plan
  final bool isPopular;

  /// Constructor
  SubscriptionPlan({
    required this.name,
    required this.durationDays,
    required this.price,
    this.discountInfo,
    this.isPopular = false,
  });
}

/// A screen for upgrading subscription
class SubscriptionScreen extends ConsumerStatefulWidget {
  /// Constructor
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen> {
  // List of available subscription plans
  final List<SubscriptionPlan> _plans = [
    SubscriptionPlan(
      name: '1 Day',
      durationDays: 1,
      price: 2000,
      isPopular: false,
    ),
    SubscriptionPlan(
      name: '1 Week',
      durationDays: 7,
      price: 10000,
      isPopular: true,
    ),
    SubscriptionPlan(
      name: '1 Month',
      durationDays: 30,
      price: 40000,
      isPopular: false,
    ),
    SubscriptionPlan(
      name: '3 Months',
      durationDays: 90,
      price: 120000,
      discountInfo: '1 month free',
      isPopular: false,
    ),
    SubscriptionPlan(
      name: '6 Months',
      durationDays: 180,
      price: 240000,
      discountInfo: '2 months free',
      isPopular: false,
    ),
    SubscriptionPlan(
      name: '1 Year',
      durationDays: 365,
      price: 400000,
      discountInfo: '4 months free',
      isPopular: false,
    ),
    SubscriptionPlan(
      name: 'VIP',
      durationDays: -1, // -1 represents VIP
      price: 600000,
      isPopular: false,
    ),
  ];

  // Currently selected plan
  SubscriptionPlan? _selectedPlan;

  @override
  void initState() {
    super.initState();
    // Set the most popular plan as the default selected plan
    _selectedPlan = _plans.firstWhere((plan) => plan.isPopular);
  }

  @override
  Widget build(BuildContext context) {
    final userStream = ref.watch(userProvider);

    return RScafold(
      title: 'Upgrade Subscription',
      body: SingleChildScrollView(
        child: userStream.when(
          data: (user) {
            if (user == null) {
              return const Center(child: Text("You are not logged in"));
            }
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const SizedBox(height: 24),
                  _buildCurrentPlan(user),
                  // const SizedBox(height: 32),
                  // _buildAccountDetails(user),
                  const SizedBox(height: 32),
                  _buildPlansSection(),
                  const SizedBox(height: 32),
                  _buildPaymentSection(),
                ],
              ),
            );
          },
          error: (error, stackTrace) => Center(child: Text("Error: $error")),
          loading: () => const Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }

  Widget _buildCurrentPlan(AppUser user) {
    final hasSubscription = user.testerExpiration != null;
    final expirationDate =
        hasSubscription
            ? DateFormat('MMM d, yyyy').format(user.testerExpiration!.toDate())
            : 'No active subscription';

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: hasSubscription ? AppColors.success : AppColors.warning,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasSubscription
                      ? Icons.verified
                      : Icons.warning_amber_rounded,
                  color:
                      hasSubscription ? AppColors.success : AppColors.warning,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Plan',
                  style: AppTypography.subtitle.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              hasSubscription ? 'Tester Plan' : 'Free Plan',
              style: AppTypography.bodyLarge,
            ),
            const SizedBox(height: 4),
            Text(
              hasSubscription
                  ? 'Expires on: $expirationDate'
                  : 'Limited features available',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlansSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose a Plan',
          style: AppTypography.subtitle.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 1,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: _plans.length,
          itemBuilder: (context, index) {
            return _buildPlanCard(_plans[index]);
          },
        ),
      ],
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan) {
    final isSelected = _selectedPlan == plan;
    final formatter = NumberFormat('#,###');

    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? AppColors.primary : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedPlan = plan;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      plan.name,
                      style: AppTypography.subtitle.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '${formatter.format(plan.price)} MMK',
                style: AppTypography.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                plan.durationDays == -1
                    ? 'Unlimited access'
                    : '${plan.durationDays} days',
                style: AppTypography.bodySmall,
              ),
              if (plan.discountInfo != null) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.success.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    plan.discountInfo!,
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              if (plan.isPopular) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Most Popular',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Image file for transaction screenshot
  String? _transactionImagePath;
  String? _transactionImageUrl;
  XFile? _pickedImage;
  bool _isUploading = false;
  String? _uploadError;
  bool _uploadSuccess = false;

  // ImgBB API key - in a real app, this would be stored securely
  final String _imgbbApiKey = 'ef5af124088ca4d2825ae6a01f3ff5b4';
  final ImagePicker _imagePicker = ImagePicker();

  Widget _buildPaymentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Verification',
          style: AppTypography.subtitle.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'Please upload a screenshot of your transaction to verify payment',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondaryLight,
          ),
        ),
        const SizedBox(height: 16),
        _buildTransactionImageUploader(),
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed:
                _transactionImagePath != null ? _handleSubscription : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              disabledBackgroundColor: AppColors.primary.withAlpha(128),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Submit Payment Verification',
              style: AppTypography.button.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        _buildPaymentInstructions(),
      ],
    );
  }

  Widget _buildTransactionImageUploader() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              _transactionImagePath != null
                  ? AppColors.success
                  : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: _isUploading ? null : _pickTransactionImage,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 250,
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          child:
              _isUploading
                  ? const Center(child: CircularProgressIndicator())
                  : _transactionImagePath != null
                  ? _buildUploadedImagePreview()
                  : _buildImageUploadPlaceholder(),
        ),
      ),
    );
  }

  Widget _buildUploadedImagePreview() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Show image preview if available
        if (_pickedImage != null && !_isUploading) ...[
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(_pickedImage!.path),
              height: 100,
              width: 100,
              fit: BoxFit.cover,
              errorBuilder:
                  (context, error, stackTrace) => Container(
                    height: 100,
                    width: 100,
                    color: AppColors.backgroundLight,
                    child: const Icon(
                      Icons.image,
                      size: 48,
                      color: AppColors.borderLight,
                    ),
                  ),
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Show different icons based on upload status
        if (_uploadSuccess)
          const Icon(Icons.check_circle, color: AppColors.success, size: 48)
        else if (_uploadError != null)
          const Icon(Icons.error_outline, color: AppColors.error, size: 48)
        else
          const Icon(
            Icons.check_circle_outline,
            color: AppColors.info,
            size: 48,
          ),

        const SizedBox(height: 16),

        // Show different text based on upload status
        Text(
          _uploadSuccess
              ? 'Payment screenshot uploaded successfully'
              : _uploadError != null
              ? 'Upload failed'
              : 'Transaction screenshot selected',
          style: AppTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: _uploadError != null ? AppColors.error : null,
          ),
          textAlign: TextAlign.center,
        ),

        // Show error message if there is one
        if (_uploadError != null) ...[
          const SizedBox(height: 8),
          Text(
            _uploadError!,
            style: AppTypography.bodySmall.copyWith(color: AppColors.error),
            textAlign: TextAlign.center,
          ),
        ],

        // Show image URL if upload was successful
        if (_uploadSuccess && _transactionImageUrl != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.backgroundLight,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderLight),
            ),
            child: Text(
              'Image URL: ${_transactionImageUrl!.substring(0, min(_transactionImageUrl!.length, 30))}${_transactionImageUrl!.length > 30 ? '...' : ''}',
              style: AppTypography.bodySmall.copyWith(fontFamily: 'monospace'),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],

        const SizedBox(height: 12),

        // Button to change the image
        TextButton.icon(
          onPressed: _pickTransactionImage,
          icon: const Icon(Icons.refresh, size: 16),
          label: const Text('Change Image'),
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primary,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildImageUploadPlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.upload_file,
          color: AppColors.primary.withAlpha(179),
          size: 48,
        ),
        const SizedBox(height: 16),
        Text(
          'Tap to upload transaction screenshot',
          style: AppTypography.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Supported formats: JPG, PNG',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondaryLight,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPaymentInstructions() {
    if (_selectedPlan == null) return const SizedBox.shrink();

    final formatter = NumberFormat('#,###');

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Payment Instructions',
                  style: AppTypography.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Please transfer ${formatter.format(_selectedPlan!.price)} MMK to one of the following accounts:',
              style: AppTypography.bodyMedium,
            ),
            const SizedBox(height: 12),
            _buildPaymentAccountItem('KBZ Pay', '***********', "kpay.png"),
            const Divider(height: 24),
            _buildPaymentAccountItem('Wave Pay', '***********', 'wave.png'),
            const SizedBox(height: 16),
            Text(
              'After making the payment, take a screenshot of the transaction and upload it above.',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondaryLight,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentAccountItem(String name, String number, String png) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Image.asset(
            'assets/images/$png',
            package: 'rauth',
            height: 26,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                number,
                style: AppTypography.bodyMedium.copyWith(letterSpacing: 2.5),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            // Copy to clipboard functionality would go here
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('$number copied to clipboard')),
            );
          },
          icon: const Icon(Icons.copy, size: 20),
          tooltip: 'Copy to clipboard',
          color: AppColors.primary,
        ),
      ],
    );
  }

  Future<void> _pickTransactionImage() async {
    try {
      // Reset state
      setState(() {
        _isUploading = true;
        _uploadError = null;
        _uploadSuccess = false;
        _transactionImageUrl = null;
      });

      // Pick image from gallery
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Reduce image quality to save bandwidth
      );

      // If user canceled the picker
      if (pickedImage == null) {
        setState(() {
          _isUploading = false;
        });
        return;
      }

      // Save the picked image
      _pickedImage = pickedImage;
      _transactionImagePath = pickedImage.path;

      // Upload the image to ImgBB
      await _uploadImageToImgBB(pickedImage);
    } catch (e) {
      // Handle any errors
      setState(() {
        _isUploading = false;
        _uploadError = 'Error picking image: ${e.toString()}';
      });
    }
  }

  Future<void> _uploadImageToImgBB(XFile imageFile) async {
    try {
      // Read the image file as bytes
      final bytes = await imageFile.readAsBytes();

      // Create a multipart request for ImgBB
      final uri = Uri.parse('https://api.imgbb.com/1/upload');

      // Create form data with API key and image
      final request = http.MultipartRequest('POST', uri);
      request.fields['key'] = _imgbbApiKey;

      // Add the image file
      request.files.add(
        http.MultipartFile.fromBytes('image', bytes, filename: imageFile.name),
      );

      // For demonstration purposes, we'll simulate a successful upload
      // In a real app, you would send the request and handle the response
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      // Simulate a successful response with a fake image URL
      // In a real app, you would parse the JSON response:
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final imageUrl = jsonData['data']['url'];

        // Update Firebase with the image URL and payment status
        await _updatePaymentStatus(imageUrl);

        // Update state with success
        setState(() {
          _isUploading = false;
          _uploadSuccess = true;
          _transactionImageUrl = imageUrl;
        });
      }
    } catch (e) {
      // Handle upload errors
      setState(() {
        _isUploading = false;
        _uploadError = 'Error uploading image: ${e.toString()}';
      });
    }
  }

  Future<void> _updatePaymentStatus(String imageUrl) async {
    try {
      // Get the current user
      final user = ref.read(userProvider).value;
      if (user == null) {
        throw Exception('User not logged in');
      }

      // Get the Firestore instance
      final firestore = ref.read(firestoreProvider);

      // Create a new payment record in a dedicated 'payments' collection
      await firestore.collection('payments').add({
        'userId': user.uid,
        'userName': user.name,
        'userEmail': user.email,
        'status': 'pending approval',
        'screenshotUrl': imageUrl,
        'planName': _selectedPlan?.name,
        'planDuration': _selectedPlan?.durationDays,
        'amount': _selectedPlan?.price,
        'submittedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update the user document with a reference to the payment status
      await firestore.collection('users').doc(user.uid).update({
        'hasPaymentPending': true,
        'lastPaymentSubmission': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      // If there's an error updating Firebase, throw it to be caught by the caller
      throw Exception('Error updating payment status: ${e.toString()}');
    }
  }

  Future<void> _handleSubscription() async {
    if (_selectedPlan == null) return;

    // Verify that we have an image URL
    if (_transactionImageUrl == null || !_uploadSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please upload a payment screenshot first'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show a loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Get the current user
      final user = ref.read(userProvider).value;
      if (user == null) {
        throw Exception('User not logged in');
      }

      // Get the Firestore instance
      final firestore = ref.read(firestoreProvider);

      // Calculate the new expiration date based on the selected plan
      DateTime? newExpirationDate;
      if (_selectedPlan!.durationDays > 0) {
        newExpirationDate = DateTime.now().add(
          Duration(days: _selectedPlan!.durationDays),
        );
      }

      // Create a payment reference ID
      final paymentId =
          'payment_${DateTime.now().millisecondsSinceEpoch}_${user.uid.substring(0, 8)}';

      // Create a new payment document in the payments collection
      await firestore.collection('payments').doc(paymentId).set({
        'id': paymentId,
        'userId': user.uid,
        'userName': user.name,
        'userEmail': user.email,
        'userPhone': user.phone,
        'status': 'pending approval',
        'screenshotUrl': _transactionImageUrl,
        'planName': _selectedPlan!.name,
        'planDuration': _selectedPlan!.durationDays,
        'amount': _selectedPlan!.price,
        'currency': 'MMK',
        'requestedExpirationDate': newExpirationDate?.toIso8601String(),
        'submittedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'reviewedAt': null,
        'reviewedBy': null,
        'notes': null,
      });

      // Update the user document with a reference to the payment
      await firestore.collection('users').doc(user.uid).update({
        'hasPaymentPending': true,
        'lastPaymentId': paymentId,
        'lastPaymentSubmission': FieldValue.serverTimestamp(),
      });

      // Simulate API call for admin approval
      await Future.delayed(const Duration(seconds: 1));

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Close the loading dialog
      Navigator.pop(context);

      // Show success dialog
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Payment Verification Submitted'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your payment for the ${_selectedPlan!.name} plan has been submitted for verification.',
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Payment ID: $paymentId',
                    style: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'monospace',
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'An administrator will review your payment and activate your subscription shortly.',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    // Navigator.pop(context); // Go back to previous screen
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
      );
    } catch (e) {
      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Close the loading dialog
      Navigator.pop(context);

      // Show error dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
